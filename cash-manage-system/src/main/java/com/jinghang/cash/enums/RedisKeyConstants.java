package com.jinghang.cash.enums;


/**
 * <AUTHOR>
 * @date 2024/1/16
 */
public class RedisKeyConstants {
    public static final String SPLIT_CHAR = ":";
    /**
     * PARENT
     */
    private static final String PARENT_L = "cash_business:";

    public static final String BIZ_LIMIT_LOAN = PARENT_L + "limit" + SPLIT_CHAR + "loan" + SPLIT_CHAR; // 放款限额

    public static final String SUSPEND_ACTIVE_LOAN = PARENT_L + "suspend_active" + SPLIT_CHAR + "loan" + SPLIT_CHAR; // 放款挂起激活

    public static final String APPROVAL_APPLY = "approval_apply:";

    public static final String SUBMIT_LOAN = "submit_loan:";

    public static final String REPAY_APPLY = "repay_apply:";

    public static final String ORDER_SUBMIT = "order_submit:";

    /**
     * 项目信息
     */
    public static final String PROJECT_INFO = PARENT_L + "project_info:";

    /**
     * 项目协议信息
     */
    public static final String PROJECT_AGREEMENT = PARENT_L + "project_agreement:";
}
