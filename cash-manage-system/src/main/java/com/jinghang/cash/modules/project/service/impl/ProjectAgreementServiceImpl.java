package com.jinghang.cash.modules.project.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jinghang.cash.api.dto.ProjectAgreementDto;
import com.jinghang.cash.api.enums.AbleStatusExt;
import com.jinghang.cash.api.enums.ActiveInactive;
import com.jinghang.cash.enums.DeletedEnum;
import com.jinghang.cash.enums.RedisKeyConstants;
import com.jinghang.cash.modules.project.domain.ProjectAgreement;
import com.jinghang.cash.modules.project.domain.dto.ProjectAgreementQueryCriteria;
import com.jinghang.cash.modules.project.mapper.ProjectAgreementMapper;
import com.jinghang.cash.modules.project.service.ProjectAgreementService;
import com.jinghang.cash.utils.PageResult;
import com.jinghang.cash.utils.PageUtil;
import com.jinghang.cash.utils.RedisUtils;
import com.jinghang.cash.utils.SecurityUtils;
import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 协议模板配置服务实现类
 *
 * @Author: Lior
 * @CreateTime: 2025/8/25 09:50
 */
@Service
public class ProjectAgreementServiceImpl implements ProjectAgreementService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectAgreementServiceImpl.class);

    @Autowired
    private ProjectAgreementMapper projectAgreementMapper;

    @Autowired
    private RedisUtils redisUtils;

    /**
     * 默认缓存过期时间：24 * 7小时
     */
    private static final Duration CACHE_DURATION = Duration.ofHours(24 * 7);

    /**
     * 根据项目代码、流程贷款阶段和合同模板类型获取项目协议
     *
     * @param projectCode          项目编码
     * @param flowLoanStage        资产方合同签署阶段
     * @param capitalLoanStage     资金方合同签署阶段
     * @param contractTemplateType 合同模板类型
     * @return 项目协议
     */
    @Override
    public ProjectAgreement getByStageAndType(String projectCode, String flowLoanStage, String capitalLoanStage, String contractTemplateType) {
        if (StringUtils.isBlank(projectCode) || StringUtils.isBlank(contractTemplateType)) {
            logger.info("项目编码或合同模板类型为空，无法查询协议模板配置");
            return null;
        }

        // 构建缓存key
        String cacheKey = RedisKeyConstants.PROJECT_AGREEMENT + projectCode + ":"
                + (ObjectUtil.isNotEmpty(flowLoanStage) ? flowLoanStage : "flowLoanStage") + ":"
                + (ObjectUtil.isNotEmpty(capitalLoanStage) ? capitalLoanStage : "capitalLoanStage") + ":"
                + contractTemplateType;

        try {
            // 1. 先从缓存中获取
            Object cachedData = redisUtils.get(cacheKey);
            if (cachedData instanceof ProjectAgreement) {
                return (ProjectAgreement) cachedData;
            }

            // 2. 缓存中没有，从数据库查询
            ProjectAgreement projectAgreement = projectAgreementMapper.selectByStageAndType(projectCode, flowLoanStage, capitalLoanStage, contractTemplateType);

            // 3. 查询结果放入缓存
            if (projectAgreement != null) {
                redisUtils.set(cacheKey, projectAgreement, CACHE_DURATION.getSeconds(), TimeUnit.SECONDS);
            }

            return projectAgreement;

        } catch (Exception e) {
            logger.error("根据项目编码、阶段和模板类型查询协议模板配置异常，projectCode: {}, flowLoanStage: {}, capitalLoanStage: {}, contractTemplateType: {}",
                    projectCode, flowLoanStage, capitalLoanStage, contractTemplateType, e);
            throw new RuntimeException("查询协议模板配置失败", e);
        }
    }

    /**
     * 根据项目代码和是否退回流程获取项目协议列表
     *
     * @param projectCodes      项目编码列表
     * @param isReturnToFlow    是否回传流量方
     * @param isReturnToCapital 是否回传资金方
     * @return 项目协议列表
     */
    @Override
    public List<ProjectAgreement> getByReturnStatus(List<String> projectCodes, ActiveInactive isReturnToFlow, ActiveInactive isReturnToCapital) {
        if (CollUtil.isEmpty(projectCodes)) {
            logger.info("项目编码列表为空，无法查询协议模板配置");
            return new ArrayList<>();
        }

        try {
            String returnToFlow = isReturnToFlow != null ? isReturnToFlow.getCode() : null;
            String returnToCapital = isReturnToCapital != null ? isReturnToCapital.getCode() : null;

            List<ProjectAgreement> projectAgreements = projectAgreementMapper.selectByReturnStatus(projectCodes, returnToFlow, returnToCapital);
            return projectAgreements != null ? projectAgreements : new ArrayList<>();
        } catch (Exception e) {
            logger.error("根据项目编码列表和退回状态查询协议模板配置异常，projectCodes: {}, isReturnToFlow: {}, isReturnToCapital: {}",
                    projectCodes, isReturnToFlow, isReturnToCapital, e);
            throw new RuntimeException("查询协议模板配置失败", e);
        }
    }

    /**
     * 根据项目代码和流程贷款阶段获取项目协议列表
     *
     * @param projectCode      项目编码
     * @param flowLoanStage    资产方合同签署阶段
     * @param capitalLoanStage 资金方合同签署阶段
     * @return 项目协议列表
     */
    @Override
    public List<ProjectAgreement> getByStage(String projectCode, String flowLoanStage, String capitalLoanStage) {
        if (StringUtils.isBlank(projectCode)) {
            logger.info("项目编码为空，无法查询协议模板配置");
            return new ArrayList<>();
        }

        try {
            List<ProjectAgreement> projectAgreements = projectAgreementMapper.selectByStage(projectCode, flowLoanStage, capitalLoanStage);
            return projectAgreements != null ? projectAgreements : new ArrayList<>();
        } catch (Exception e) {
            logger.error("根据项目编码和阶段查询协议模板配置异常，projectCode: {}, flowLoanStage: {}, capitalLoanStage: {}",
                    projectCode, flowLoanStage, capitalLoanStage, e);
            throw new RuntimeException("查询协议模板配置失败", e);
        }
    }


    @Override
    public PageResult<ProjectAgreement> queryAllPage(ProjectAgreementQueryCriteria criteria) {
        logger.info("分页条件查询投诉参数:{}", JsonUtil.toJsonString(criteria));
        Page<ProjectAgreement> page = new Page<>(criteria.getPage(), criteria.getSize());
        LambdaQueryWrapper<ProjectAgreement> wrapper = new LambdaQueryWrapper<>();
        if(StringUtil.isNotBlank(criteria.getProjectCode())){
            wrapper.eq(ProjectAgreement::getProjectCode,criteria.getProjectCode());
        }
        if(ObjectUtil.isNotEmpty(criteria.getContractTemplateType())){
            wrapper.eq(ProjectAgreement::getContractTemplateType,criteria.getContractTemplateType().name());
        }
        if(StringUtil.isNotBlank(criteria.getTemplateOwnerName())){
            wrapper.eq(ProjectAgreement::getTemplateOwnerName,criteria.getTemplateOwnerName());
        }
        if(ObjectUtil.isNotEmpty(criteria.getEnabled())){
            wrapper.eq(ProjectAgreement::getEnabled,criteria.getEnabled().name());
        }
        wrapper.eq(ProjectAgreement::getDeleted,DeletedEnum.N.name());
        Page<ProjectAgreement> pageList = projectAgreementMapper.selectPage(page, wrapper);
        return PageUtil.toPage(pageList.getRecords(),pageList.getTotal());
    }

    @Override
    public ProjectAgreement info(String id) {
        return projectAgreementMapper.selectById(id);
    }

    @Override
    public void create(ProjectAgreementDto resources) {
        ProjectAgreement agreement =new ProjectAgreement();
        BeanUtil.copyProperties(resources,agreement);
        String id = "PA" + System.currentTimeMillis() + String.valueOf(Math.random()).substring(2,5);
        agreement.setId(id);
        agreement.setCreatedBy(String.valueOf(SecurityUtils.getCurrentUserId()));
        agreement.setCreatedTime(LocalDateTime.now());
        agreement.setEnabled(AbleStatusExt.INIT);
        agreement.setDeleted(DeletedEnum.N.name());
        agreement.setContractTemplateDesc(resources.getContractTemplateType().getDesc());
        projectAgreementMapper.insert(agreement);
    }

    @Override
    public void update(ProjectAgreementDto resources) {
        ProjectAgreement agreement = projectAgreementMapper.selectById(resources.getId());
        agreement.copy(resources);
        agreement.setUpdatedBy(String.valueOf(SecurityUtils.getCurrentUserId()));
        agreement.setUpdatedTime(LocalDateTime.now());
        projectAgreementMapper.updateById(agreement);
    }

    @Override
    public void enable(ProjectAgreementDto resources) {

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAll(List<String> ids) {
        if(CollectionUtil.isEmpty(ids)){
            throw new RuntimeException("请选择数据!");
        }
        LambdaUpdateWrapper<ProjectAgreement> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjectAgreement::getDeleted,DeletedEnum.Y.name())
                .in(ProjectAgreement::getId,ids);
        projectAgreementMapper.update(wrapper);

    }

    /**
     * 清除项目协议缓存
     *
     * @param projectCode 项目编码
     */
    @Override
    public void clearProjectAgreementCache(String projectCode) {
        if (com.jinghang.cash.utils.StringUtils.isBlank(projectCode)) {
            logger.info("项目编码为空，无法清除缓存");
            return;
        }

        String cacheKey = RedisKeyConstants.PROJECT_INFO + projectCode;
        try {
            redisUtils.del(cacheKey);
        } catch (Exception e) {
            logger.info("清除项目信息缓存异常，projectCode: {}", projectCode, e);
        }
    }
}
