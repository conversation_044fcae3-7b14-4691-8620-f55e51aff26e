package com.jinghang.cash.modules.project.service;

import com.jinghang.cash.api.dto.ProjectInfoDto;
import com.jinghang.cash.modules.project.domain.ProjectInfo;
import com.jinghang.cash.modules.project.domain.dto.ProjectInfoQueryCriteria;
import com.jinghang.cash.modules.project.service.domain.vo.ProjectInfoVo;
import com.jinghang.cash.utils.PageResult;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 项目信息服务接口
 *
 * @Author: Lior
 * @CreateTime: 2025/8/20 10:16
 */
public interface ProjectInfoService {

    /**
     * 根据项目编码查询项目完整信息（带缓存）
     *
     * @param projectCode 项目编码
     * @return 项目完整信息VO
     */
    ProjectInfoDto queryProjectInfo(String projectCode);

    /**
     * 查询所有生效项目信息
     *
     * @return 所有生效项目信息列表
     */
    List<ProjectInfoDto> queryAllEnabledProjects();

    /**
     *  项目信息详情
     * @param id
     * @return
     */
    ProjectInfoVo getProjectInfo(String id);

    /**
     * 清除项目信息缓存
     *
     * @param projectCode 项目编码
     */
    void clearProjectInfoCache(String projectCode);


    /**
     * 查询数据分页
     * @param criteria 条件
     * @return PageResult
     */
    PageResult<ProjectInfoVo> queryAllPage(ProjectInfoQueryCriteria criteria);

    /**
     *  临时项目列表
     * @param criteria
     * @return
     */
    PageResult<ProjectInfoVo> queryTemPage(ProjectInfoQueryCriteria criteria);


    /**
     * 查询所有数据不分页
     * @param criteria 条件参数
     * @return List<ProjectInfoDto>
     */
    List<ProjectInfo> queryAll(ProjectInfoQueryCriteria criteria);

    /**
     * 创建
     * @param resources /
     */
    Map<String,String> create(ProjectInfoDto resources);

    /**
     * 编辑
     * @param resources /
     */
    void update(ProjectInfoDto resources);

    void enable(ProjectInfoDto resources);


    /**
     * 多选删除
     * @param ids /
     */
    void deleteAll(List<String> ids);

    /**
     * 导出数据
     * @param all 待导出的数据
     * @param response /
     * @throws IOException /
     */
    void download(List<ProjectInfo> all, HttpServletResponse response) throws IOException;

}
