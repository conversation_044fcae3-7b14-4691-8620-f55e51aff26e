package com.jinghang.cash.modules.project.service;

import com.jinghang.cash.api.dto.ProjectAgreementDto;
import com.jinghang.cash.api.enums.ActiveInactive;
import com.jinghang.cash.modules.project.domain.ProjectAgreement;
import com.jinghang.cash.modules.project.domain.dto.ProjectAgreementQueryCriteria;
import com.jinghang.cash.utils.PageResult;

import java.util.List;

/**
 * 协议模板配置服务接口
 *
 * @Author: Lior
 * @CreateTime: 2025/8/25 09:45
 */
public interface ProjectAgreementService {

    /**
     * 根据项目代码、流程贷款阶段和合同模板类型获取项目协议
     *
     * @param projectCode 项目编码
     * @param flowLoanStage 资产方合同签署阶段
     * @param capitalLoanStage 资金方合同签署阶段
     * @param contractTemplateType 合同模板类型
     * @return 项目协议
     */
    ProjectAgreement getByStageAndType(String projectCode, String flowLoanStage, String capitalLoanStage, String contractTemplateType);

    /**
     * 根据项目代码和是否退回流程获取项目协议列表
     *
     * @param projectCodes 项目编码列表
     * @param isReturnToFlow 是否回传流量方
     * @param isReturnToCapital 是否回传资金方
     * @return 项目协议列表
     */
    List<ProjectAgreement> getByReturnStatus(List<String> projectCodes, ActiveInactive isReturnToFlow, ActiveInactive isReturnToCapital);

    /**
     * 根据项目代码和流程贷款阶段获取项目协议列表
     *
     * @param projectCode 项目编码
     * @param flowLoanStage 资产方合同签署阶段
     * @param capitalLoanStage 资金方合同签署阶段
     * @return 项目协议列表
     */
    List<ProjectAgreement> getByStage(String projectCode, String flowLoanStage, String capitalLoanStage);



    /**
     * 查询数据分页
     * @param criteria 条件
     * @return PageResult
     */
    PageResult<ProjectAgreement> queryAllPage(ProjectAgreementQueryCriteria criteria);

    /**
     * 详情
     */
    ProjectAgreement info(String id);

    /**
     * 创建
     * @param resources /
     */
    void create(ProjectAgreementDto resources);

    /**
     * 编辑
     * @param resources /
     */
    void update(ProjectAgreementDto resources);

    void enable(ProjectAgreementDto dto);

    /**
     * 多选删除
     * @param ids /
     */
    void deleteAll(List<String> ids);

    /**
     * 清除项目协议缓存
     *
     * @param projectCode 项目编码
     */
    void clearProjectAgreementCache(String projectCode);




}
