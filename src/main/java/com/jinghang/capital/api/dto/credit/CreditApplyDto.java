package com.jinghang.capital.api.dto.credit;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.FlowChannel;
import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.jinghang.capital.api.dto.LoanPurpose;
import com.jinghang.capital.api.dto.file.FileInfoDto;
import com.jinghang.capital.api.dto.Product;

import java.math.BigDecimal;
import java.util.List;

public class CreditApplyDto<E> {

    /**
     * 外部授信id
     */
    private String sysId;
    /**
     * 产品
     */
    private Product product;
    /**
     * 渠道
     */
    private FlowChannel flowChannel;
    /**
     * 银行
     */
    private BankChannel bankChannel;

    /**
     * 融担公司
     */
    private GuaranteeCompany guaranteeCompany;
    /**
     * 消费明细
     */
    private String productItem;
    /**
     * 申请授信金额
     */
    private BigDecimal creditAmt;

    /**
     * 借款金额
     */
    private BigDecimal loanAmt;

    private LoanPurpose loanPurpose;
    /**
     * 申请期数
     */
    private Integer periods;
    /**
     * 对客利率
     */
    private BigDecimal customRate;

    /**
     * 授信阶段合同编号
     */
    private String creditApplyContractNo;


    /**
     * 辅助模式
     */
    private String assistMode;

    /**
     * 商户信息
     */
    private MerchantInfoDto merchantInfo;
    /**
     * 用户信息
     */
    private UserInfoDto userInfo;
    /**
     * 身份证信息
     */
    private IdCardInfoDto idCardInfo;
    /**
     * 银行卡信息
     */
    private BankCardInfoDto bankCardInfo;
    /**
     * 协议文件
     */
    private List<FileInfoDto> fileInfoDtoList;

    /**
     * 联系人
     */
    private List<UserContactInfoDto> userContactInfoDtoList;
    /**
     * 额外信息
     */
    private E extInfo;

    /**
     * 关联的项目唯一编码
     */
    private String projectCode;

    public List<UserContactInfoDto> getUserContactInfoDtoList() {
        return userContactInfoDtoList;
    }

    public void setUserContactInfoDtoList(List<UserContactInfoDto> userContactInfoDtoList) {
        this.userContactInfoDtoList = userContactInfoDtoList;
    }

    public String getSysId() {
        return sysId;
    }

    public void setSysId(String sysId) {
        this.sysId = sysId;
    }

    public Product getProduct() {
        return product;
    }

    public void setProduct(Product product) {
        this.product = product;
    }

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public GuaranteeCompany getGuaranteeCompany() {
        return guaranteeCompany;
    }

    public void setGuaranteeCompany(GuaranteeCompany guaranteeCompany) {
        this.guaranteeCompany = guaranteeCompany;
    }

    public String getProductItem() {
        return productItem;
    }

    public void setProductItem(String productItem) {
        this.productItem = productItem;
    }

    public BigDecimal getCreditAmt() {
        return creditAmt;
    }

    public void setCreditAmt(BigDecimal creditAmt) {
        this.creditAmt = creditAmt;
    }

    public BigDecimal getLoanAmt() {
        return loanAmt;
    }

    public void setLoanAmt(BigDecimal loanAmt) {
        this.loanAmt = loanAmt;
    }

    public LoanPurpose getLoanPurpose() {
        return loanPurpose;
    }

    public void setLoanPurpose(LoanPurpose loanPurpose) {
        this.loanPurpose = loanPurpose;
    }

    public Integer getPeriods() {
        return periods;
    }

    public void setPeriods(Integer periods) {
        this.periods = periods;
    }

    public BigDecimal getCustomRate() {
        return customRate;
    }

    public void setCustomRate(BigDecimal customRate) {
        this.customRate = customRate;
    }

    public String getAssistMode() {
        return assistMode;
    }

    public void setAssistMode(String assistMode) {
        this.assistMode = assistMode;
    }

    public MerchantInfoDto getMerchantInfo() {
        return merchantInfo;
    }

    public void setMerchantInfo(MerchantInfoDto merchantInfo) {
        this.merchantInfo = merchantInfo;
    }

    public UserInfoDto getUserInfo() {
        return userInfo;
    }

    public void setUserInfo(UserInfoDto userInfo) {
        this.userInfo = userInfo;
    }


    public String getCreditApplyContractNo() {
        return creditApplyContractNo;
    }

    public void setCreditApplyContractNo(String creditApplyContractNo) {
        this.creditApplyContractNo = creditApplyContractNo;
    }

    public IdCardInfoDto getIdCardInfo() {
        return idCardInfo;
    }

    public void setIdCardInfo(IdCardInfoDto idCardInfo) {
        this.idCardInfo = idCardInfo;
    }

    public BankCardInfoDto getBankCardInfo() {
        return bankCardInfo;
    }

    public void setBankCardInfo(BankCardInfoDto bankCardInfo) {
        this.bankCardInfo = bankCardInfo;
    }

    public List<FileInfoDto> getFileInfoDtoList() {
        return fileInfoDtoList;
    }

    public void setFileInfoDtoList(List<FileInfoDto> fileInfoDtoList) {
        this.fileInfoDtoList = fileInfoDtoList;
    }

    public E getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(E extInfo) {
        this.extInfo = extInfo;
    }

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(FlowChannel flowChannel) {
        this.flowChannel = flowChannel;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }
}
