package com.jinghang.capital.api.dto;

/**
 * 融担公司
 */
public enum GuaranteeCompany {


    CJRD("超捷融担", "超捷融担", "超捷融担"),
    HYRD("昊悦融担", "昊悦融担", "昊悦融担");


    private final String shortName;

    private final String fullName;
    /**
     * 担保履行地
     */
    private final String address;


    GuaranteeCompany(String shortName, String fullName, String address) {
        this.shortName = shortName;
        this.fullName = fullName;
        this.address = address;
    }

    public String getShortName() {
        return shortName;
    }

    public String getFullName() {
        return fullName;
    }

    public String getAddress() {
        return address;
    }

    public static GuaranteeCompany of(final String name) {
        for (GuaranteeCompany company : GuaranteeCompany.values()) {
            if (company.name().equals(name)) {
                return company;
            }
        }
        return null;
    }
}
