package com.jinghang.capital.api.dto.recc;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @title: 对账文件
 * @desc:
 * @date 2025/8/15 16:22
 */
public class ReccFileDto {

    /**
     * 资方
     */
    private String channel;

    /**
     * 对账文件类型
     */
    private String reccType;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件日期
     */
    private LocalDate fileDate;

    /**
     * 来源文件bucket
     */
    private String ossBucket;

    /**
     * 来源文件key
     */
    private String ossKey;

    /**
     * 来源文件oss
     */
    private String ossUrl;

    /**
     * 对账日期
     */
    private LocalDate reccDate;

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getReccType() {
        return reccType;
    }

    public void setReccType(String reccType) {
        this.reccType = reccType;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public LocalDate getFileDate() {
        return fileDate;
    }

    public void setFileDate(LocalDate fileDate) {
        this.fileDate = fileDate;
    }

    public String getOssBucket() {
        return ossBucket;
    }

    public void setOssBucket(String ossBucket) {
        this.ossBucket = ossBucket;
    }

    public String getOssKey() {
        return ossKey;
    }

    public void setOssKey(String ossKey) {
        this.ossKey = ossKey;
    }

    public String getOssUrl() {
        return ossUrl;
    }

    public void setOssUrl(String ossUrl) {
        this.ossUrl = ossUrl;
    }

    public LocalDate getReccDate() {
        return reccDate;
    }

    public void setReccDate(LocalDate reccDate) {
        this.reccDate = reccDate;
    }
}
