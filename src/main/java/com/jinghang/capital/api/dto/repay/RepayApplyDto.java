package com.jinghang.capital.api.dto.repay;

import java.math.BigDecimal;
import java.time.LocalDate;

public class RepayApplyDto {

    private String outerLoanId;
    private String loanId;
    private String outerRepayId;
    private BigDecimal amount;
    private Integer period;
    private RepayPurpose repayPurpose;

    private RepayMode repayMode;

    private RepayType repayType;

    /**
     * 还款分类
     * 代偿前，代偿后
     */
    private RepayCategory repayCategory;

    private BigDecimal principal;
    private BigDecimal interest;
    private BigDecimal guaranteeFee;
    private BigDecimal overdueFee;
    /**
     * 平台罚息（对客罚息-对资罚息）
     */
    private BigDecimal platformOverdueFee;

    /**
     * 咨询费
     */
    private BigDecimal consultFee;

    /**
     * 违约金
     */
    private BigDecimal breachFee;

    /**
     * 公共账户类型
     */
    private String commonWithholdType;
    /**
     * 公共支付账号
     */
    private String withholdTypeMemberId;


    /**
     * 扣款协议号 - 线上扣款
     */
    private String agreementNo;
    /**
     * 还款银行编码 - 线上扣款
     */
    private String repayBankCode;

    /**
     * 还款银行名称 - 线上扣款
     */
    private String repayBankName;
    /**
     * 还款卡号 - 线上扣款
     */
    private String repayAcctNo;
    /**
     * 还款方用户名 - 线上扣款
     */
    private String repayRelUser;
    /**
     * 还款方手机号 - 线上扣款
     */
    private String repayRelPhone;
    /**
     * 还款方身份证号 - 线上扣款
     */
    private String repayRelCard;

    /**
     * 代扣流水号
     */
    private String payOrderNo;

    /**
     * 还款优惠劵金额(360活动减免金额-对客咨询费)
     */
    private BigDecimal reduceAmount;


    /**
     * 资方分账金额
     */
    private BigDecimal shareBankAmt;
    /**
     * 流量分账金额
     */
    private BigDecimal shareFlowAmt;
    /**
     * 融担分账金额
     */
    private BigDecimal shareGuaranteeAmt;
    /**
     * 剩余分账金额
     */
    private BigDecimal shareRestAmt;

    /**
     * 部分还款类型
     */
    private PartRepayType partRepayType;

    /**
     * 扣款渠道id（宝付，通联，易宝）
     */
    private String chargeChannelId;
    //母订单流水号
    private String parentSerialNum;
    private RepayBank repayBank;

    /**
     * 是否逾期
     */
    private String isOverdue;

    public String getIsOverdue() {
        return isOverdue;
    }

    public void setIsOverdue(String isOverdue) {
        this.isOverdue = isOverdue;
    }

    public String getParentSerialNum() {
        return parentSerialNum;
    }

    public void setParentSerialNum(String parentSerialNum) {
        this.parentSerialNum = parentSerialNum;
    }

    public RepayBank getRepayBank() {
        return repayBank;
    }

    public void setRepayBank(RepayBank repayBank) {
        this.repayBank = repayBank;
    }

    public String getPayOrderNo() {
        return payOrderNo;
    }

    public void setPayOrderNo(String payOrderNo) {
        this.payOrderNo = payOrderNo;
    }

    /**
     * 转账时间（只有线下还款时需要传）
     */
    private LocalDate transferDate;

    public String getOuterLoanId() {
        return outerLoanId;
    }

    public void setOuterLoanId(String outerLoanId) {
        this.outerLoanId = outerLoanId;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public String getOuterRepayId() {
        return outerRepayId;
    }

    public void setOuterRepayId(String outerRepayId) {
        this.outerRepayId = outerRepayId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public RepayPurpose getRepayPurpose() {
        return repayPurpose;
    }

    public void setRepayPurpose(RepayPurpose repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public RepayMode getRepayMode() {
        return repayMode;
    }

    public void setRepayMode(RepayMode repayMode) {
        this.repayMode = repayMode;
    }

    public RepayType getRepayType() {
        return repayType;
    }

    public void setRepayType(RepayType repayType) {
        this.repayType = repayType;
    }

    public BigDecimal getPrincipal() {
        return principal;
    }

    public void setPrincipal(BigDecimal principal) {
        this.principal = principal;
    }

    public BigDecimal getInterest() {
        return interest;
    }

    public void setInterest(BigDecimal interest) {
        this.interest = interest;
    }

    public BigDecimal getGuaranteeFee() {
        return guaranteeFee;
    }

    public void setGuaranteeFee(BigDecimal guaranteeFee) {
        this.guaranteeFee = guaranteeFee;
    }

    public BigDecimal getOverdueFee() {
        return overdueFee;
    }

    public void setOverdueFee(BigDecimal overdueFee) {
        this.overdueFee = overdueFee;
    }

    public BigDecimal getPlatformOverdueFee() {
        return platformOverdueFee;
    }

    public void setPlatformOverdueFee(BigDecimal platformOverdueFee) {
        this.platformOverdueFee = platformOverdueFee;
    }

    public BigDecimal getConsultFee() {
        return consultFee;
    }

    public void setConsultFee(BigDecimal consultFee) {
        this.consultFee = consultFee;
    }

    public BigDecimal getBreachFee() {
        return breachFee;
    }

    public void setBreachFee(BigDecimal breachFee) {
        this.breachFee = breachFee;
    }

    public String getCommonWithholdType() {
        return commonWithholdType;
    }

    public void setCommonWithholdType(String commonWithholdType) {
        this.commonWithholdType = commonWithholdType;
    }

    public String getWithholdTypeMemberId() {
        return withholdTypeMemberId;
    }

    public void setWithholdTypeMemberId(String withholdTypeMemberId) {
        this.withholdTypeMemberId = withholdTypeMemberId;
    }

    public String getAgreementNo() {
        return agreementNo;
    }

    public void setAgreementNo(String agreementNo) {
        this.agreementNo = agreementNo;
    }

    public String getRepayBankCode() {
        return repayBankCode;
    }

    public void setRepayBankCode(String repayBankCode) {
        this.repayBankCode = repayBankCode;
    }

    public String getRepayBankName() {
        return repayBankName;
    }

    public void setRepayBankName(String repayBankName) {
        this.repayBankName = repayBankName;
    }

    public String getRepayAcctNo() {
        return repayAcctNo;
    }

    public void setRepayAcctNo(String repayAcctNo) {
        this.repayAcctNo = repayAcctNo;
    }

    public String getRepayRelUser() {
        return repayRelUser;
    }

    public void setRepayRelUser(String repayRelUser) {
        this.repayRelUser = repayRelUser;
    }

    public String getRepayRelPhone() {
        return repayRelPhone;
    }

    public void setRepayRelPhone(String repayRelPhone) {
        this.repayRelPhone = repayRelPhone;
    }

    public String getRepayRelCard() {
        return repayRelCard;
    }

    public void setRepayRelCard(String repayRelCard) {
        this.repayRelCard = repayRelCard;
    }

    public BigDecimal getShareBankAmt() {
        return shareBankAmt;
    }

    public void setShareBankAmt(BigDecimal shareBankAmt) {
        this.shareBankAmt = shareBankAmt;
    }

    public BigDecimal getShareFlowAmt() {
        return shareFlowAmt;
    }

    public void setShareFlowAmt(BigDecimal shareFlowAmt) {
        this.shareFlowAmt = shareFlowAmt;
    }

    public BigDecimal getShareGuaranteeAmt() {
        return shareGuaranteeAmt;
    }

    public void setShareGuaranteeAmt(BigDecimal shareGuaranteeAmt) {
        this.shareGuaranteeAmt = shareGuaranteeAmt;
    }

    public BigDecimal getShareRestAmt() {
        return shareRestAmt;
    }

    public void setShareRestAmt(BigDecimal shareRestAmt) {
        this.shareRestAmt = shareRestAmt;
    }

    public String getChargeChannelId() {
        return chargeChannelId;
    }

    public void setChargeChannelId(String chargeChannelId) {
        this.chargeChannelId = chargeChannelId;
    }

    public LocalDate getTransferDate() {
        return transferDate;
    }

    public void setTransferDate(LocalDate transferDate) {
        this.transferDate = transferDate;
    }

    public BigDecimal getReduceAmount() {
        return reduceAmount;
    }

    public void setReduceAmount(BigDecimal reduceAmount) {
        this.reduceAmount = reduceAmount;
    }

    public PartRepayType getPartRepayType() {
        return partRepayType;
    }

    public void setPartRepayType(PartRepayType partRepayType) {
        this.partRepayType = partRepayType;
    }


    public RepayCategory getRepayCategory() {
        return repayCategory;
    }

    public void setRepayCategory(RepayCategory repayCategory) {
        this.repayCategory = repayCategory;
    }
}
