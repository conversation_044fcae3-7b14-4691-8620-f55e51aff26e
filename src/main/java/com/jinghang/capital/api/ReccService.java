package com.jinghang.capital.api;

import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.recc.*;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * 对账服务
 */
public interface ReccService {

    /**
     * 处理对账逻辑
     *
     * @param reccApply 对账申请
     * @return
     */
    @PostMapping("process")
    RestResult<ReccResultDto> process(@RequestBody ReccApplyDto reccApply);

    /**
     * 查询对账结果
     *
     * @param reccApply 对账查询申请
     * @return
     */
    @PostMapping("query")
    RestResult<ReccResultDto> query(@RequestBody ReccApplyDto reccApply);

    /**
     * 对账文件下载
     *
     * @param dto 对账文件下载
     * @return
     */
    @PostMapping("download")
    RestResult<ReccResultDto> download(@RequestBody ReccDownloadDto dto);


    /**
     * 对账文件申请
     *
     * @param dto 对账文件申请
     * @return
     */
    @PostMapping("fileApply")
    RestResult<ReccFileListDto> fileApply(@RequestBody ReccFileApplyDto dto);

}
