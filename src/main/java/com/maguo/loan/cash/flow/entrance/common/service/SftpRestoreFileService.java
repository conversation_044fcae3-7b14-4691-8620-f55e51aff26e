package com.maguo.loan.cash.flow.entrance.common.service;

import com.alibaba.fastjson.JSON;
import com.aliyun.oss.OSSException;
import com.jinghang.cash.api.dto.ProjectAgreementDto;
import com.jinghang.cash.api.enums.TemplateOwner;
import com.jinghang.common.util.CollectionUtil;
import com.maguo.loan.cash.flow.entity.AgreementSignatureRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.SftpRestoreRecord;
import com.maguo.loan.cash.flow.entity.UserFile;
import com.maguo.loan.cash.flow.entrance.common.dto.request.SftpRestoreFileReqDTO;
import com.maguo.loan.cash.flow.enums.FileType;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.job.agreement.LoanAgreementJob;
import com.maguo.loan.cash.flow.job.fql.FqlLoanAgreementJob;
import com.maguo.loan.cash.flow.job.ppd.PPDSignJob;
import com.maguo.loan.cash.flow.remote.manage.ProjectAgreementFeign;
import com.maguo.loan.cash.flow.repository.AgreementSignatureRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.SftpRestoreRecordRepository;
import com.maguo.loan.cash.flow.repository.UserFileRepository;
import com.maguo.loan.cash.flow.service.UserFileService;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

/**
 * sftp 文件恢复服务
 *
 * <AUTHOR>
 */
@Service
public class SftpRestoreFileService {

    private static final Logger logger = LoggerFactory.getLogger(SftpRestoreFileService.class);

    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private LoanRepository loanRepository;
    @Autowired
    private UserFileService userFileService;
    @Autowired
    private UserFileRepository userFileRepository;
    @Autowired
    private PPDSignJob ppdSignJob;
    @Autowired
    private LoanAgreementJob loanAgreementJob;
    @Autowired
    private AgreementSignatureRecordRepository agreementSignatureRecordRepository;
    @Autowired
    private SftpRestoreRecordRepository sftpRestoreRecordRepository;
    @Autowired
    private ProjectAgreementFeign projectAgreementFeign;
    @Autowired
    private FqlLoanAgreementJob fqlLoanAgreementJob;

    private static final String FILE_NOT_EXISTS = "文件记录不存在";

    public String restoreFile(SftpRestoreFileReqDTO request) {
        logger.info("SftpRestoreFileService.restoreFile start, params:{}", JSON.toJSONString(request));
        String sftpPath = "";
        try {
            String outerOrderId = request.getOuterOrderId();
            FileType fileType = FileType.valueOf(request.getFileType());
            List<SftpRestoreRecord> byOuterOrderIdAndApplyTimeAfter = sftpRestoreRecordRepository.findByOuterOrderIdAndFileTypeAndApplyTimeAfterOrderByCreatedTimeDesc(outerOrderId, fileType, LocalDateTime.now().minusDays(7));
            if (CollectionUtil.isNotEmpty(byOuterOrderIdAndApplyTimeAfter)) {
                String cachedPath = byOuterOrderIdAndApplyTimeAfter.get(0).getSftpPath();
                logger.info("已存在7天内恢复记录，直接返回缓存路径: {}", cachedPath);
                return cachedPath;
            }
            Order order = orderRepository.findByOuterOrderId(outerOrderId);
            if (ObjectUtils.isEmpty(order)) {
                logger.info("未找到订单，outerOrderId: {}", outerOrderId);
                return "该订单不存在！";
            }
            FlowChannel flowChannel = order.getFlowChannel();
            Loan loan = loanRepository.findByOrderId(order.getId());
            if (ObjectUtils.isEmpty(loan)) {
                logger.info("未找到借款信息，orderId: {}", order.getId());
                return "该订单不存在！";
            }
            SftpRestoreRecord sftpRestoreRecord = new SftpRestoreRecord();
            sftpRestoreRecord.setFileType(fileType);
            sftpRestoreRecord.setFlowChannel(flowChannel);
            sftpRestoreRecord.setApplyTime(LocalDateTime.now());
            sftpRestoreRecord.setOuterOrderId(outerOrderId);
            ProjectAgreementDto agreementDto = getProjectAgreementDto(loan, fileType.name());//获取合同模板配置信息
            if (Objects.isNull(agreementDto)) {
                logger.info("未找到该文件的命名配置，fileType: {}", fileType);
                return "未找到该文件的命名配置";
            }
            String templateOwner = agreementDto.getTemplateOwner();
            UserFile userFile = null;
            AgreementSignatureRecord recordOpt = null;
            // 统一查询文件记录，避免重复代码
            if (Objects.equals(templateOwner, TemplateOwner.CAPITAL.name())) {
                userFile = userFileRepository.findTopByLoanNoAndFileTypeOrderByCreatedTimeDesc(loan.getId(), fileType);
                if (ObjectUtils.isEmpty(userFile)) {
                    logger.info("未找到用户文件，loanId: {}, fileType: {}", loan.getId(), fileType);
                    return FILE_NOT_EXISTS;
                }
            } else { // 流量方
                recordOpt = agreementSignatureRecordRepository.findLatestAgreementSignatureRecordByOrderNoAndFileType(outerOrderId, fileType.name()).orElse(null);
                if (Objects.isNull(recordOpt)) {
                    logger.info("未找到协议签署记录，outerOrderId: {}, fileType: {}", outerOrderId, fileType.name());
                    return FILE_NOT_EXISTS;
                }
            }
            if (Objects.equals(flowChannel,FlowChannel.PPCJDL)) {//恢复合同-拍拍
                if (Objects.equals(templateOwner, TemplateOwner.CAPITAL.name())) {//资金方文件上传
                    logger.info("拍拍贷-资金方文件上传...");
                    sftpPath = ppdSignJob.uploadCapitalFileToSftp(loan,userFile,agreementDto.getCapitalContractName());
                } else {//流量方文件上传
                    logger.info("拍拍贷-流量方文件上传...");
                    sftpPath = ppdSignJob.uploadFlowFileToSftp(loan,recordOpt.getCommonOssUrl(),agreementDto.getFlowContractName());
                }
            }
            if (Objects.equals(flowChannel,FlowChannel.LVXIN)) {//恢复合同-绿信
                if (Objects.equals(templateOwner, TemplateOwner.CAPITAL.name())) {//资金方文件上传
                    logger.info("绿信-资金方文件上传...");
                    sftpPath = loanAgreementJob.uploadCoreAgreementFileToSftp(loan, userFile, order, agreementDto.getCapitalContractName());
                } else {//流量方文件上传
                    logger.info("绿信-流量方文件上传...");
                    sftpPath = loanAgreementJob.uploadFlowAgreementFileToSftp(loan, recordOpt.getCommonOssUrl(), fileType, order, agreementDto.getFlowContractName());
                }
            }
            if (Objects.equals(flowChannel,FlowChannel.FQLQY001)) {//恢复合同-分期乐
                String bizDateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                if (Objects.equals(templateOwner, TemplateOwner.CAPITAL.name())) {//资金方文件上传
                    logger.info("分期乐-资金方文件上传...");
                    sftpPath = fqlLoanAgreementJob.uploadSignFileToSftp(loan,order,userFile,agreementDto.getCapitalContractName(),bizDateStr);
                } else {//流量方文件上传
                    logger.info("分期乐-流量方文件上传...");
                    //sftpPath = fqlLoanAgreementJob.uploadSignFileToSftp(loan,order,userFile,agreementDto.getCapitalContractName(),bizDateStr);
                }
            }
            if (Objects.equals(fileType,FileType.CREDIT_SETTLE_VOUCHER_FILE)) {//恢复结清证明
                if (!Objects.equals(order.getOrderState(),OrderState.CLEAR)) {
                    logger.info("恢复结清证明时，该笔订单尚未结清！");
                    return "该笔订单尚未结清！";
                }
                UserFile voucherFile = userFileRepository.findTopByLoanNoAndFileTypeOrderByCreatedTimeDesc(loan.getId(), fileType);
                if (ObjectUtils.isEmpty(voucherFile)) {
                    return FILE_NOT_EXISTS;
                }
                logger.info("恢复结清证明文件上传...");
                sftpPath = userFileService.uploadVoucherFileToSftp(voucherFile.getOssKey(), voucherFile.getOssBucket(), loan, voucherFile.getFileType(), order);
            }
            sftpRestoreRecord.setSftpPath(sftpPath);
            sftpRestoreRecordRepository.save(sftpRestoreRecord);
        } catch (IllegalArgumentException e) {
            logger.error("sftp恢复文件失败", e);
            return "文件类型不正确";
        }catch (OSSException e){
            logger.error("sftp恢复文件失败", e);
            return "OSS文件不存在";
        }catch (Exception e) {
            logger.error("sftp恢复文件失败", e);
            return "error";
        }
        logger.info("SftpRestoreFileService.restoreFile end.");
        return sftpPath;
    }

    /**
     * 获取合同模板配置信息
     * @param loan 借据信息
     * @param fileType 文件类型
     * @return 配置信息
     */
    public ProjectAgreementDto getProjectAgreementDto(Loan loan, String fileType) {
        logger.info("根据项目唯一编码" + loan.getProjectCode() + "和文件类型" + fileType + "查询合同模板配置信息");
        //根据项目唯一编码和文件类型查询合同模板配置信息
        return projectAgreementFeign.getByStageAndType(loan.getProjectCode(), null, null, fileType);
    }

}
