<template>
  <div>
    <el-form ref="ruleForm" :model="form" :rules="rules" label-width="120px" inline size="mini">
      <el-form-item label="订单号：" label-width="80px">{{ plan.loanId || '-' }}</el-form-item>
      <el-form-item label="用户姓名：" label-width="90px">{{ plan.userName || '-' }}</el-form-item>
      <el-form-item label="用户身份证号:">{{ plan.certNo || '-' }}</el-form-item>

      <el-row style="font-weight: bold;">还款计划</el-row>
      <el-table :data="plan.repayPlans" border max-height="300" style="width: 100%;margin: 20px 0;">
        <el-table-column prop="period" label="期数" width="50"></el-table-column>
        <el-table-column prop="period" label="还款状态" width="100">
          <template slot-scope="scope">
          <dict-tag
            :value="scope.row.repayStatus || 'NORMAL'"
            :options="dict.repayStatus"
          />
        </template>
        </el-table-column>

        <el-table-column prop="dueDate" label="应还时间" width="100"></el-table-column>
        <el-table-column prop="totalAmount" label="应还总金额" width="100"></el-table-column>
        <el-table-column prop="principal" label="应还本金" width="100"></el-table-column>
        <el-table-column prop="interest" label="应还利息" width="100"></el-table-column>
        <el-table-column prop="zhibao" label="应还担保费" width="100"></el-table-column>
        <el-table-column prop="consultFee" label="应还咨询费" width="100"></el-table-column>
        <el-table-column prop="penalty" label="应还罚息" width="100"></el-table-column>
        <el-table-column prop="paidTime" label="实还时间" width="100"></el-table-column>
        <el-table-column prop="actAmount" label="已还总金额" width="100"></el-table-column>
        <el-table-column prop="paidPrincipal" label="已还本金" width="100"></el-table-column>
        <el-table-column prop="paidInterest" label="已还利息" width="100"></el-table-column>
        <el-table-column prop="paidZhibao" label="已还担保费" width="100"></el-table-column>
        <el-table-column prop="paidConsultFee" label="已还咨询费" width="100"></el-table-column>
        <el-table-column prop="paidPenalty" label="已还罚息" width="100"></el-table-column>
        <el-table-column prop="deductionInterest" label="减免利息" width="100"></el-table-column>
        <el-table-column prop="deductionGuaranteeAmt" label="减免担保费" width="100"></el-table-column>
        <el-table-column prop="deductionConsultFee" label="减免咨询费" width="100"></el-table-column>
        <el-table-column prop="deductionOverdueInterest" label="减免罚息" width="100"></el-table-column>
      </el-table>
      <!-- 当还款计划未还期，进行减免操作 -->
      <el-card v-if="currentPlan.period">
        <el-row v-if="type === 'over'">
          <el-row><el-form-item label="还款期数">{{ currentPlan.period || '-' }}</el-form-item></el-row>
          <el-row><el-form-item label="计划金额">{{ currentPlan.totalAmount || '-' }}</el-form-item></el-row>
        </el-row>
        <el-row v-if="type === 'reduce'">
          <el-row>
            <el-form-item label="减免期数" prop="repayPurpose">
              <el-radio-group v-model="form.repayPurpose">
                <el-radio :disabled="!plan.isOverdue" label="CURRENT">当期</el-radio>
                <el-radio :disabled="plan.isOverdue" label="CLEAR">结清</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-row>
          <el-row v-if="form.repayPurpose === 'CLEAR'">
            <el-form-item label="计划金额">{{ clearPlan.totalAmt || '-' }}</el-form-item>
          </el-row>
          <el-row v-if="form.repayPurpose === 'CURRENT'">
            <el-form-item label="计划金额">{{ currentPlan.totalAmount || '-' }}</el-form-item>
            <el-form-item label="减免期数">{{ currentPlan.period || '-' }}</el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="减免金额" prop="reduceAmount">
              <el-input v-model="form.reduceAmount" type="number" style="width: 300px;" placeholder="请输入"></el-input>
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item v-if="form.repayPurpose === 'CURRENT'" label="减免后应还金额">{{ afterReduce }}</el-form-item>
            <el-form-item v-if="form.repayPurpose === 'CLEAR'"  label="减免后应还金额">{{ afterClearReduce }}</el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="备注">
              <el-input v-model="form.remark" style="width: 300px;" type="textarea" placeholder="请输入"></el-input>
            </el-form-item>
          </el-row>
        </el-row>
        <el-form-item label=" ">
          <el-button type="primary" @click="sureApply">确认申请</el-button>
          <el-button v-if="type === 'reduce'" @click="resetForm">重 置</el-button>
        </el-form-item>
      </el-card>
    </el-form>
  </div>
</template>
<script>
import { clearTrail } from '@/api/ppdai'
export default {
  name: "PlanDetail",
  props: {
    plan: {
      type: Object,
      default: {}
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      form: {},
      rules: {
        repayPurpose: [
          { required: true, message: '请选择减免期数', trigger: 'change' }
        ],
        reduceAmount: [
          { required: true, message: '请输入减免金额', trigger: 'blur' },
          { validator: (rule, value, callback) => {
            if (!value || Number(value) <= 0) {
              callback(new Error('请输入正确的减免金额'))
            } else {
              if (this.plan.isOverdue) { // 逾期，当期减免
                const canReduceAmount = (Number(this.currentPlan.totalAmount) * 100 - Number(this.currentPlan.principal) * 100) / 100
                if (Number(value) > canReduceAmount) {
                  this.form.reduceAmount = canReduceAmount
                  callback(new Error(`最多可减${canReduceAmount}`))
                } else {
                  callback()
                }
              } else { // 未逾期 结清减免
                if (this.clearPlan.totalAmt && Number(value) > this.clearPlan.totalAmt) {
                  this.form.reduceAmount = this.clearPlan.totalAmt
                  callback(new Error('请输入正确的减免金额'))
                } else {
                  callback()
                }
              }
            }
          }, trigger: 'blur' },
        ]
      },
      currentPlan: {},
      clearPlan: {}
    };
  },
  mounted () {
    if (this.plan.repayPlans && this.plan.repayPlans.length > 0) {
      switch (this.type) {
        case 'over': // 逾期线下还款计划，显示第一期数据
          this.currentPlan = this.plan.repayPlans[0] || {}
          break
        case 'reduce': // 正常还款计划，展示未还一期数据
          this.currentPlan = this.plan.repayPlans.find(item => ['NORMAL', 'OVER'].includes(item.repayStatus)) || {}
          if (this.plan.isOverdue) { // 是否逾期，当期不可减免,逾期只可减免当期，无逾期可选择结清
            this.form.repayPurpose = 'CURRENT'
          } else {
            this.form.repayPurpose = 'CLEAR'
            this.clearTrail() // 结清试算，更新申请数据
          }
        break
      }
    }
  },
  computed: {
    // 减免后金额，当期减免计算方式：剩余待还金额 - 减免金额 = 减免后应还金额
    afterReduce () {
      const totalAmount100 = (Number(this.currentPlan.totalAmount) * 100) || 0 // 应还总金额
      if (this.form.reduceAmount && this.currentPlan.totalAmount) {
        const reduce100 = (Number(this.form.reduceAmount) * 100) || 0
        return (totalAmount100.toFixed() - reduce100.toFixed()) / 100 // 减免后金额
      } else {
        return (totalAmount100 / 100) || 0
      }
    },
    afterClearReduce () {
      if (this.form.reduceAmount && this.clearPlan.totalAmt) {
        const principal100 = Number(this.clearPlan.totalAmt) * 100
        const reduce100 = Number(this.form.reduceAmount) * 100
        return (principal100.toFixed() - reduce100.toFixed()) / 100
      } else {
        return this.clearPlan.totalAmount || 0
      }
    }
  },
  dicts: ['repayStatus'],
  methods: {
    // 结清试算
    clearTrail (period) {
      const params = {
        loanId: this.plan.loanId,
        repayPurpose: 'CLEAR',
        period: period
      }
      try {
        clearTrail(params).then(res => {
          this.clearPlan = res.data
        })
      } catch (err) {
        console.log(1111, err)
      }
    },
    // 确认申请
    sureApply () {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let params = Object.assign({}, this.form)
          params.period = this.currentPlan.period

          this.$emit('sureApply', params)
        } else {
          return false;
        }
      });
    },
    // 重置
    resetForm () {
      // 暂时移除验证规则
      this.$refs.ruleForm.resetFields();
      this.form = {}
    }
  }
};
</script>
