<template>
  <div class="app-container">
    <el-tabs
      v-model="activeName"
      v-loading="loading"
      type="border-card"
      @tab-click="handleClick"
    >
      <el-tab-pane
        label="减免申请"
        name="reduce"
      >
        <plan
          v-if="RepayPlan.loanId"
          ref="planChild"
          type="reduce"
          :plan="RepayPlan"
          @sureApply="reduceApply"
        />
      </el-tab-pane>
      <el-tab-pane
        label="线下还款"
        name="over"
      >
        <plan
          v-if="OverPlan.loanId"
          type="over"
          :plan="OverPlan"
          @sureApply="offlineRepay"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import {
  queryRepayPlan, // 减免还款计划
  repayOverPlanQuery, // 线下还款计划
  reduceApply, // 减免申请
  offlineRepay // 线下还款
} from "@/api/ppdai";
import Plan from './components/plan.vue'

export default {
  name: "PpdaiApply",
  components: { Plan },
  data() {
    return {
      loading: false,
      loanId: "", // 放款id
      activeName: 'reduce',
      // 查询参数
      queryParams: {
        type: "mobile",
        typeText: "",
        pageNum: 1,
        pageSize: 10,
        flowChannel: undefined,
        startTime: undefined,
        endTime: undefined
      },
      loading: false,
      RepayPlan: {}, // 减免还款计划数据
      OverPlan: {}, // 线下还款计划数据
      list: [],
      total: 0,
      visible: false,
      fileList: [],
      dataVal: []
    };
  },
  dicts: ['flowChannel'],
  created() {
    if (this.$route.query.loanId) {
      this.loanId = this.$route.query.loanId;
      this.reduceRepayPlan()
    }
  },
  methods: {
    // 切换tab
    handleClick(tab, event) {
      const tabName = tab.name
      this[`${tabName}RepayPlan`]()
    },
    // 获取回款列表
    reduceRepayPlan() {
      this.loading = true;
      if (!this.loanId) {
        this.loading = false
        return this.$message.warning("暂无记录")
      }
      let params = {
        loanId: this.loanId
      }
      queryRepayPlan(params).then(res => {
        let isOverdue = false
        if (res.data.repayPlans && res.data.repayPlans.length > 0) {
          isOverdue = res.data.repayPlans.some(item => ['OVER', 'PART_REPAID'].includes(item.repayStatus))
        }
        this.RepayPlan = {
          ...res.data,
          isOverdue
        }
        this.loading = false;
      })
    },
    // 获取线下还款计划
    overRepayPlan() {
      this.loading = true;
      if (!this.loanId) {
        this.loading = false
        return this.$message.warning("暂无记录")
      }
      let params = {
        loanId: this.loanId
      }
      try {
        repayOverPlanQuery(params).then(res => {
          this.OverPlan = res.data;
          this.loading = false;
        })
      } catch (error) {
        // console.log(error)
        this.loading = false;
        this.$message.error('查询失败,请稍后再试!')
      }
    },
    // 减免申请
    reduceApply (data) {
      let params = {
        loanId: this.loanId,
        ...data
      }

      try {
        reduceApply(params).then(res => {
          this.$message.success('申请成功!')
          this.reduceRepayPlan()
          this.$refs.planChild.resetForm()
        })
      } catch (error) {
        // console.log(error)
        this.$message.error('申请失败,请稍后再试!')
      }
    },
    // 线下还款申请
    offlineRepay (data) {
      let params = {
        loanId: this.loanId,
        ...data
      }
      try {
        offlineRepay(params).then(res => {
          this.$message.success('申请成功!')
          this.overRepayPlan()
        })
      } catch (error) {
        // console.log(error)
        this.$message.error('申请失败,请稍后再试!')
      }
    }

  }
};
</script>

<style scoped>
.item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: #e5e5e5 1px solid;
  padding-bottom: 10px;
  margin-bottom: 10px;
}

.item span {
  font-size: 16px;
}
</style>
