package com.jinghang.capital.batch.job.cybkfl;

import com.jinghang.capital.batch.FinBatchApplication;
import org.springframework.boot.test.context.SpringBootTest;

/**
 *
 */
@SpringBootTest(classes = FinBatchApplication.class, properties = "eureka.client.register-with-eureka = false")
class CybkFlReccJobTest {

    //@Autowired
    //private CybkFlReccFilePullJob reccFilePullJob;
    //
    //@Autowired
    //private CybkFlReccJob reccJob;
    //
    //@Test
    //void reccPull() throws Exception {
    //    String param = "{\"fileDate\": \"2038-10-29\", \"fileType\": \"REPAY\", \"isRewriting\": true}";
    //    reccFilePullJob.execute(param);
    //}
    //
    //@Test
    //void recc() throws Exception {
    //    String param = "{\"reccDate\": \"2038-04-20\", \"reccType\": \"LOAN\"}";
    //    reccJob.execute(param);
    //}

}
