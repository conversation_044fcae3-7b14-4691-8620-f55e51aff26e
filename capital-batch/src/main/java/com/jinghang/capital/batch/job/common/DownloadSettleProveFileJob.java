package com.jinghang.capital.batch.job.common;


import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.FileType;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.file.FileDailyProcessDto;
import com.jinghang.capital.batch.remote.FinFileService;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import java.time.LocalDate;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 结清证明文件  请求开具、查询开具结果并下载文件
 * 查询至结清日期为止（包含结清日期） 三天内所有结清借据
 */
@Component
@JobHandler("downloadSettleProveFileJob")
public class DownloadSettleProveFileJob extends IJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(DownloadSettleProveFileJob.class);

    @Autowired
    private FinFileService finLoanFileService;

    private static List<BankChannel> bankChannels = List.of();

    private static final int PREVIOUS_DAY = 1;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        try {
            // {"settleDate": "2024-08-06", "bankChannel": ""}
            LocalDate settleDate = LocalDate.now().minusDays(PREVIOUS_DAY);
            if (!StringUtil.isEmpty(param)) {
                DownloadSettleProveFileJobParam fileParam = JsonUtil.convertToObject(param, DownloadSettleProveFileJobParam.class);
                if (null != fileParam.getSettleDate()) {
                    settleDate = fileParam.getSettleDate();
                }
                if (StringUtil.isNotEmpty(fileParam.getBankChannel())) {
                    if (bankChannels.contains(BankChannel.valueOf(fileParam.getBankChannel()))) {
                        bankChannels = List.of(BankChannel.valueOf(fileParam.getBankChannel()));
                    }
                }
            }

            for (BankChannel bankChannel : bankChannels) {
                logger.info("结清证明文件开具 settleDate: {}", settleDate);
                FileDailyProcessDto processDto = new FileDailyProcessDto();
                processDto.setBankChannel(bankChannel);
                processDto.setProcessDate(settleDate);
                processDto.setType(FileType.CREDIT_SETTLE_VOUCHER_FILE);
                logger.info("结清证明文件开具 开始：{}", JsonUtil.toJsonString(processDto));
                RestResult<Void> restResult = finLoanFileService.dailyProcess(processDto);
                logger.info("结清证明文件开具结果：{}", JsonUtil.toJsonString(restResult));
            }
        } catch (Exception e) {
            logger.error("downloadSettleProveFileJob 执行异常", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}
