package com.jinghang.capital.batch.entity.hxbk;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jinghang.capital.batch.entity.BaseEntity;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @作者 Mr.sandman
 * @时间 2025/07/09 14:37
 */
@TableName("hxbk_recc_loan")
public class HXBKReccLoan extends BaseEntity implements Serializable {

    //
    private String reccId;

    // 系统loanID
    private String sysId;

    // 资方渠道
    private String channel;

    // 合同编号
    private String contractNo;

    // 客户真实姓名
    private String name;

    // 客户证件号码(18位，X大写)
    private String certNo;

    // 贷款用途
    private String loanUse;

    // 申请时间(yyyy-MM-dd HH:mm:ss)
    private String applyDate;

    // 放款日期(yyyy-MM-dd HH:mm:ss)
    private String encashDate;

    // 放款金额(单位分)
    private BigDecimal encashAmt;

    // 起息日(yyyyMMdd)
    private String startDate;

    // 到期日(yyyyMMdd)
    private String endDate;

    // 账单日(1-28)
    private String billDate;

    // 贷款期次数
    private Integer totalTerms;

    // 还款方式(1等额本息/2等额本金/3按期付息/6到期还本)
    private String repayMode;

    // 宽限期天数
    private Integer graceDay;

    // 日利率(保留6位小数)
    private BigDecimal dayRate;

    // 年利率(保留6位小数)
    private BigDecimal yearRate;

    // 优惠券利率(保留6位小数)
    private BigDecimal couponRate;

    // 授信编号
    private String creditNo;

    // 贷款申请单号
    private String applyNo;

    // 业务类型(H101=哈罗出行短期)
    private String otherData;

    // 业务类型
    private String bsnType;

    // 对账状态
    private String reccStatus;

    public String getReccId() {
        return reccId;
    }

    public void setReccId(String reccId) {
        this.reccId = reccId;
    }

    public String getSysId() {
        return sysId;
    }

    public void setSysId(String sysId) {
        this.sysId = sysId;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public String getLoanUse() {
        return loanUse;
    }

    public void setLoanUse(String loanUse) {
        this.loanUse = loanUse;
    }

    public String getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(String applyDate) {
        this.applyDate = applyDate;
    }

    public String getEncashDate() {
        return encashDate;
    }

    public void setEncashDate(String encashDate) {
        this.encashDate = encashDate;
    }

    public BigDecimal getEncashAmt() {
        return encashAmt;
    }

    public void setEncashAmt(BigDecimal encashAmt) {
        this.encashAmt = encashAmt;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getBillDate() {
        return billDate;
    }

    public void setBillDate(String billDate) {
        this.billDate = billDate;
    }

    public Integer getTotalTerms() {
        return totalTerms;
    }

    public void setTotalTerms(Integer totalTerms) {
        this.totalTerms = totalTerms;
    }

    public String getRepayMode() {
        return repayMode;
    }

    public void setRepayMode(String repayMode) {
        this.repayMode = repayMode;
    }

    public Integer getGraceDay() {
        return graceDay;
    }

    public void setGraceDay(Integer graceDay) {
        this.graceDay = graceDay;
    }

    public BigDecimal getDayRate() {
        return dayRate;
    }

    public void setDayRate(BigDecimal dayRate) {
        this.dayRate = dayRate;
    }

    public BigDecimal getYearRate() {
        return yearRate;
    }

    public void setYearRate(BigDecimal yearRate) {
        this.yearRate = yearRate;
    }

    public BigDecimal getCouponRate() {
        return couponRate;
    }

    public void setCouponRate(BigDecimal couponRate) {
        this.couponRate = couponRate;
    }

    public String getCreditNo() {
        return creditNo;
    }

    public void setCreditNo(String creditNo) {
        this.creditNo = creditNo;
    }

    public String getApplyNo() {
        return applyNo;
    }

    public void setApplyNo(String applyNo) {
        this.applyNo = applyNo;
    }

    public String getOtherData() {
        return otherData;
    }

    public void setOtherData(String otherData) {
        this.otherData = otherData;
    }

    public String getBsnType() {
        return bsnType;
    }

    public void setBsnType(String bsnType) {
        this.bsnType = bsnType;
    }

    public String getReccStatus() {
        return reccStatus;
    }

    public void setReccStatus(String reccStatus) {
        this.reccStatus = reccStatus;
    }
}
