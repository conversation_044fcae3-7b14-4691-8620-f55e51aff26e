package com.jinghang.capital.batch.job.common;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDate;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class SubstituteMarkParam {

    /**
     * 代还日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate substituteDay;

    /**
     * 资方渠道
     */
    private String bankChannel;

    /**
     * 融担公司
     */
    private String guaranteeCompany;
}
