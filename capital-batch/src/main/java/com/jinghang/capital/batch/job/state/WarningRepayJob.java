package com.jinghang.capital.batch.job.state;

import com.jinghang.capital.batch.entity.BankRepayRecord;
import com.jinghang.capital.batch.entity.CustomerRepayRecord;
import com.jinghang.capital.batch.job.state.Dto.RepaidSyncDto;
import com.jinghang.capital.batch.job.state.Dto.RepayRecordDto;
import com.jinghang.capital.batch.mapper.CustomerRepayRecordMapper;
import com.jinghang.capital.batch.service.WarningService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.TreeSet;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 360 支付宝线下还款预警
 */
@Component
@JobHandler("warningRepayJob")
public class WarningRepayJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(WarningRepayJob.class);

    @Resource(name = "warningStateService")
    private WarningService warningService;

    @Autowired
    private CustomerRepayRecordMapper customerRepayRecordMapper;

    private static final long THREE = 3;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        logger.info("360-支付宝线下还款预警开始");
        LocalDateTime start = LocalDateTime.now().minusDays(THREE);
        LocalDateTime end = LocalDateTime.now();
        logger.info("360-还款查询开始时间为:{},结束时间为:{}", start, end);
        StringBuilder sb = new StringBuilder();

        //360还款记录 成功 线下 支付宝
        List<RepayRecordDto> repayRecords = customerRepayRecordMapper.findRepayRecordList(start, end);
        logger.info("日期:{}, 支付宝线下360对客还款条数: {}", end, repayRecords.size());

        if (!repayRecords.isEmpty()) {
            List<RepaidSyncDto> repaidSyncList = customerRepayRecordMapper.findRepaidSyncList(repayRecords.stream().map(RepayRecordDto::getId).toList());
            logger.info("日期:{}, 支付宝线下360同步表还款条数: {}", end, repaidSyncList.size());

            if (repayRecords.size() != repaidSyncList.size()) {
                //同步表无查询到的还款记录
                sb.append(message(start, end, "360_reapy_record", Math.abs(repayRecords.size() - repaidSyncList.size()))).append("\n").append("\n");
                warningService.warn(sb.toString());
                return ReturnT.SUCCESS;
            } else {
                List<String> rsId = repaidSyncList.stream().map(RepaidSyncDto::getId).toList();
                //查询对客
                List<CustomerRepayRecord> customerRecordList1 = customerRepayRecordMapper.findCustomerRecordList(rsId);

                if (customerRecordList1.isEmpty()) {
                    //对客表无查询到的还款记录
                    sb.append(message(start, end, "360_repaid_sync", Math.abs(repayRecords.size() - repaidSyncList.size()))).append("\n").append("\n");
                    warningService.warn(sb.toString());
                    return ReturnT.SUCCESS;
                }

                //同一笔去重
                List<CustomerRepayRecord> customerRepayRecords = customerRecordList1.stream().collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(
                                Comparator.comparing(c -> c.getOuterRepayId() + c.getPeriod()))), ArrayList::new));
                logger.info("日期:{}, 支付宝线下还款对客还款条数: {}", end, customerRepayRecords.size());

                List<String> crrIdList = customerRepayRecords.stream().map(CustomerRepayRecord::getId).toList();
                List<BankRepayRecord> bankRepayRecord1 = customerRepayRecordMapper.findBankRepayRecord(crrIdList);

                List<BankRepayRecord> bankRepayRecords = bankRepayRecord1.stream().collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(
                                Comparator.comparing(b -> b.getSysId() + b.getPeriod()))), ArrayList::new));
                logger.info("日期:{}, 支付宝线下还款对资还款条数: {}", end, bankRepayRecords.size());

                if (customerRepayRecords.size() != bankRepayRecords.size()) {
                    //对客笔对资多的情况, 取多出来的数据
                    List<String> crrLoanList = customerRepayRecords.stream().map(CustomerRepayRecord::getLoanId).toList();
                    List<String> loanIds = crrLoanList.stream().filter(item -> !crrLoanList.contains(item)).toList();

                    //取对客成功与对资成功差集
                    logger.info("日期:{}, 支付宝线下还款对客成功与对资成功差集还款条数: {}", end, loanIds.size());
                    //去重后查询, 根据loanId查询对资已代偿的
                    List<String> loanIdList = loanIds.stream().distinct().toList();

                    List<BankRepayRecord> bankClaimRepayRecord = customerRepayRecordMapper.findClaimBankRepayRecordList(loanIdList);

                    List<BankRepayRecord> bankClaimRepayRecordList = bankClaimRepayRecord.stream().collect(Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(
                                    Comparator.comparing(BankRepayRecord::getLoanId))), ArrayList::new));

                    logger.info("日期:{}, 支付宝线下还款对资代偿结清还款条数: {}", end, bankClaimRepayRecordList.size());

                    //判断对客总数,对资正常和代偿总数是否一致
                    if (customerRepayRecords.size() != (bankRepayRecords.size() + bankClaimRepayRecordList.size())) {
                        int sy = Math.abs(customerRepayRecords.size() - (bankRepayRecords.size() + bankClaimRepayRecord.size()));
                        sb.append(message(start, end, "customer_repay_record", sy)).append("\n").append("\n");
                        warningService.warn(sb.toString());
                    }
                }
            }
        }
        logger.info("360-支付宝线下还款预警结束");
        return ReturnT.SUCCESS;
    }

    public String message(LocalDateTime start, LocalDateTime end, String table, Integer number) {
        StringBuilder string = new StringBuilder();
        string.append(formatLocalDateTime(start)).append(" ~ ").append(formatLocalDateTime(end))
                .append("\n")
                .append("fin-customer-entrance.").append(table).append("支付宝线下还款异常的条数:").append(number);
        return string.toString();
    }

    public static String formatLocalDateTime(LocalDateTime localDateTime) {
        return localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
}
