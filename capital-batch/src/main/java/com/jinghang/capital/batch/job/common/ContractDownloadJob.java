package com.jinghang.capital.batch.job.common;


import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.FileType;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.file.FileDailyProcessDto;
import com.jinghang.capital.batch.remote.FinFileService;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import java.time.LocalDate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@JobHandler("contractDownloadJob")
public class ContractDownloadJob extends IJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(ContractDownloadJob.class);

    private FinFileService finLoanFileService;

    private static final int PREVIOUS_DAY = 1;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        try {
            // {"fileDate": "2024-08-06", "bankChannel": "", "relateId":"", "fileType":""}
            LocalDate fileDate = LocalDate.now().minusDays(PREVIOUS_DAY);
            String relateId = null;
            String bankChannel = null;
            String fileType = null;

            if (param == null || StringUtil.isEmpty(param)) {
                logger.error("xxl-job download contract error, param is null or empty");
                throw new IllegalArgumentException("param is null or empty");
            }

            ContractDownloadJobParam fileParam = JsonUtil.convertToObject(param, ContractDownloadJobParam.class);
            if (null != fileParam.getFileDate()) {
                fileDate = fileParam.getFileDate();
            }

            if (StringUtil.isEmpty(fileParam.getBankChannel())) {
                logger.error("xxl-job download contract error, bankChannel is null");
                throw new IllegalArgumentException("bankChannel is null");
            }

            bankChannel = fileParam.getBankChannel();
            try {
                BankChannel.valueOf(bankChannel);
            } catch (IllegalArgumentException e) {
                logger.error("xxl-job download contract error, invalid bankChannel: {}", bankChannel);
                throw new IllegalArgumentException("invalid bankChannel: " + bankChannel, e);
            }

            if (StringUtil.isEmpty(fileParam.getRelateId())) {
                logger.error("xxl-job download contract error, relateId is null");
                throw new IllegalArgumentException("relateId is null");
            }
            relateId = fileParam.getRelateId();

            if (StringUtil.isEmpty(fileParam.getFileType())) {
                logger.error("xxl-job download contract error, fileType is null");
                throw new IllegalArgumentException("fileType is null");
            }

            fileType = fileParam.getFileType();
            try {
                FileType.valueOf(fileType);
            } catch (IllegalArgumentException e) {
                logger.error("xxl-job download contract error, invalid fileType: {}", fileType);
                throw new IllegalArgumentException("invalid fileType: " + fileType, e);
            }

            logger.info("download contract fileDate: {}", fileDate);
            FileDailyProcessDto processDto = new FileDailyProcessDto();
            processDto.setBankChannel(BankChannel.valueOf(bankChannel));
            processDto.setProcessDate(fileDate);
            processDto.setLoanId(relateId);
            if (fileType != null) {
                processDto.setType(FileType.valueOf(fileType));
            }

            logger.info("download contract start: {}", JsonUtil.toJsonString(processDto));
            RestResult<Void> restResult = finLoanFileService.dailyProcess(processDto);
            logger.info("download contract end: {}", JsonUtil.toJsonString(restResult));
        } catch (Exception e) {
            logger.error("contractDownloadJob execute error", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    @Autowired
    public void setFinLoanFileService(FinFileService finLoanFileService) {
        this.finLoanFileService = finLoanFileService;
    }
}
