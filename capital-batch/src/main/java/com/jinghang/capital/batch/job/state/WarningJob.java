package com.jinghang.capital.batch.job.state;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghang.capital.batch.entity.BankRepayRecord;
import com.jinghang.capital.batch.entity.Credit;
import com.jinghang.capital.batch.entity.CustomerRepayRecord;
import com.jinghang.capital.batch.entity.Loan;
import com.jinghang.capital.batch.mapper.BankRepayRecordMapper;
import com.jinghang.capital.batch.mapper.CreditMapper;
import com.jinghang.capital.batch.mapper.CustomerRepayRecordMapper;
import com.jinghang.capital.batch.mapper.LoanMapper;
import com.jinghang.capital.batch.service.WarningService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@JobHandler("warningJob")
public class WarningJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(WarningJob.class);

    @Autowired
    private LoanMapper loanMapper;

    @Autowired
    private CreditMapper creditMapper;

    @Resource(name = "warningStateService")
    private WarningService warningService;

    @Autowired
    private BankRepayRecordMapper bankRepayRecordMapper;

    @Autowired
    private CustomerRepayRecordMapper customerRepayRecordMapper;

    @Value("${warningJob.minusHours}")
    private Long subtractionHours;

    @Value("${warningJob.size}")
    private Integer size;

    /**
     * 还款预警阈值
     */
    @Value("${warningJob.repay.threshold:100}")
    private int repayThreshold;

    /**
     * 达到阈值时@指定人
     */
    @Value("${warningJob.repay.at:}")
    private String warningAt;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        logger.info("warningJob start");
        LocalDateTime end = LocalDateTime.now().minusHours(1L);
        LocalDateTime start = end.minusHours(subtractionHours);
        logger.info("查询开始时间为:{},结束时间为:{}", start, end);
        StringBuilder sb = new StringBuilder();

        //是否@相关人
        boolean isAt = false;

        /*
        授信
         */
        LambdaQueryWrapper<Credit> creditQueryWrapper = new LambdaQueryWrapper<>();
        creditQueryWrapper.between(Credit::getApplyTime, start, end)
                .in(Credit::getCreditStatus, "PROCESSING", "INIT");
        List<Credit> credits = creditMapper.selectList(creditQueryWrapper);

        if (credits.size() >= size) {

            String result = credits.stream()
                    .map(Credit::getId)  // 提取name字段
                    .collect(Collectors.joining(", "));
            sb.append(message(start, end, "credit", credits.size(), result)).append("\n").append("\n");

            if (credits.size() >= repayThreshold) {
                isAt = true;
            }
        }
        /*
        对资还款
         */
        LambdaQueryWrapper<BankRepayRecord> bankRepayRecordQueryWrapper = new LambdaQueryWrapper<>();
        bankRepayRecordQueryWrapper
                .between(BankRepayRecord::getRepayTime, start, end)
                .in(BankRepayRecord::getRepayStatus, "PROCESSING", "INIT");
        List<BankRepayRecord> bankRepayRecords = bankRepayRecordMapper.selectList(bankRepayRecordQueryWrapper);
        if (bankRepayRecords.size() >= size) {
            String result = bankRepayRecords.stream()
                    .map(BankRepayRecord::getId)  // 提取name字段
                    .collect(Collectors.joining(", "));
            sb.append(message(start, end, "bank_repay_record", bankRepayRecords.size(), result)).append("\n").append("\n");

            if (bankRepayRecords.size() >= repayThreshold) {
                isAt = true;
            }
        }
        /*
        对客还款
         */
        LambdaQueryWrapper<CustomerRepayRecord> customerRepayRecordQueryWrapper = new LambdaQueryWrapper<>();
        customerRepayRecordQueryWrapper
                .between(CustomerRepayRecord::getRepayTime, start, end)
                .in(CustomerRepayRecord::getRepayStatus, "PROCESSING", "INIT");
        List<CustomerRepayRecord> customerRepayRecords = customerRepayRecordMapper.selectList(customerRepayRecordQueryWrapper);
        if (customerRepayRecords.size() >= size) {
            String result = customerRepayRecords.stream()
                    .map(CustomerRepayRecord::getId)  // 提取name字段
                    .collect(Collectors.joining(", "));
            sb.append(message(start, end, "customer_repay_record", customerRepayRecords.size(), result)).append("\n").append("\n");

            if (customerRepayRecords.size() >= repayThreshold) {
                isAt = true;
            }
        }
        /*
        放款
         */
        LambdaQueryWrapper<Loan> loanQueryWrapper = new LambdaQueryWrapper<>();
        loanQueryWrapper.between(Loan::getCreatedTime, start, end)
                .in(Loan::getLoanStatus, "PROCESSING", "INIT");
        List<Loan> loans = loanMapper.selectList(loanQueryWrapper);

        if (loans.size() >= size) {
            String result = loans.stream()
                    .map(Loan::getId)  // 提取name字段
                    .collect(Collectors.joining(", "));
            sb.append(message(start, end, "loan", loans.size(), result));

            if (loans.size() >= repayThreshold) {
                isAt = true;
            }
        }

        if (!sb.isEmpty()) {
            if (isAt) {
                warningService.warn(sb.toString(), warningAt.split(","));
            } else {
                warningService.warn(sb.toString());
            }
        }
        logger.info("warningJob end");
        return ReturnT.SUCCESS;
    }

    public String message(LocalDateTime start, LocalDateTime end, String table, Integer number, String result) {
        StringBuilder string = new StringBuilder();
        string.append(formatLocalDateTime(start)).append(" ~ ").append(formatLocalDateTime(end))
                .append("\n")
                .append("loan-cash-capital.").append(table).append("不是最终态条数为:").append(number).append(";单号：").append(result);
        return string.toString();
    }

    public static String formatLocalDateTime(LocalDateTime localDateTime) {
        return localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
}
