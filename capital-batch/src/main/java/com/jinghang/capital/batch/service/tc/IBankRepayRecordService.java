package com.jinghang.capital.batch.service.tc;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jinghang.capital.batch.entity.BankRepayRecord;
import java.util.List;

/**
 * <p>
 * 还款记录表; 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16
 */
public interface IBankRepayRecordService extends IService<BankRepayRecord> {

    BankRepayRecord getByLoanIdAndPeriodAndStatus(String loanId, Integer period);

    /**
     * 查询借据还款成功记录
     *
     * @param loanId
     * @return
     */
    List<BankRepayRecord> queryByLoanId(String loanId);

    /**
     * 查询结清还款数据
     *
     * @param loanId
     * @return
     */
    BankRepayRecord queryByLoanIdAndRepayPurpose(String loanId);
}
