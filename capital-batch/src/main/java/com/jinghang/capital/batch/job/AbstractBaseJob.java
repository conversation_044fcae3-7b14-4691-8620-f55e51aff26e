package com.jinghang.capital.batch.job;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2023-05-16 21:10:50
 */

public abstract class AbstractBaseJob extends IJobHandler {
    protected static final Logger logger = LoggerFactory.getLogger(AbstractBaseJob.class);

    @Override
    public ReturnT<String> execute(String param) throws Exception {

        ReccJobParamEntity jobEntity;
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            jobEntity = objectMapper.readValue(param, ReccJobParamEntity.class);
        } catch (Exception e) {
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }
        if (jobEntity == null) {
            jobEntity = new ReccJobParamEntity();
        }
        if (jobEntity.getFileDate() == null) {
            // 文件日期: yyyy-MM-dd
            jobEntity.setFileDate(LocalDate.now().minusDays(1L).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        }
        return doExecute(jobEntity);
    }

    public abstract ReturnT<String> doExecute(ReccJobParamEntity jobEntity) throws Exception;

}
