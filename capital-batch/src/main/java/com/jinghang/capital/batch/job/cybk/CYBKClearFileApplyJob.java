package com.jinghang.capital.batch.job.cybk;


import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.FileType;
import com.jinghang.capital.api.dto.file.FileDailyProcessDto;
import com.jinghang.capital.batch.job.cybk.recc.CYBKReccParam;
import com.jinghang.capital.batch.remote.FinVoucherFileService;
import com.jinghang.common.util.JsonUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import java.time.LocalDate;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@JobHandler("cybkClearFileApplyJob")
public class CYBKClearFileApplyJob extends IJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(CYBKClearFileApplyJob.class);
    private static final Long DEFAULT_DAY = 1L;

    @Autowired
    private FinVoucherFileService finVoucherFileService;

    /**
     * @param param
     * @return
     * @throws Exception
     */
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        LocalDate processDay = LocalDate.now().minusDays(DEFAULT_DAY);
        try {
            if (Objects.nonNull(param)) {
                CYBKReccParam reccParam = JsonUtil.convertToObject(param, CYBKReccParam.class);
                LocalDate paramDay = reccParam.getReccDay();
                if (null != paramDay) {
                    processDay = paramDay;
                }
            }
        } catch (Exception ex) {
            logger.error("cybkClearFileApplyJob执行异常", ex);
            return ReturnT.FAIL;
        }
        logger.info("申请下载长银消金直连结清凭证, process job day: {}", processDay);
        return doDownloadFile(processDay);
    }

    private ReturnT<String> doDownloadFile(LocalDate processDay) {
        FileDailyProcessDto fileDailyProcessDto = new FileDailyProcessDto();
        fileDailyProcessDto.setProcessDate(processDay);
        fileDailyProcessDto.setType(FileType.CREDIT_SETTLE_VOUCHER_FILE);
        fileDailyProcessDto.setBankChannel(BankChannel.CYBK);
        finVoucherFileService.batchDownload(fileDailyProcessDto);
        return ReturnT.SUCCESS;
    }
}
