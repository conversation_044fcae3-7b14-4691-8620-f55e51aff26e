package com.jinghang.capital.batch.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jinghang.capital.batch.enums.ReccStateEnum;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 润楼青岛还款对账文件
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-21
 */
@TableName("rlqdb_recc_repay")
public class RlqdbReccRepay extends BaseEntity implements Serializable {


    @Serial
    private static final long serialVersionUID = -6932682760210511669L;

    /**
     * recc_id
     */
    private String reccId;

    private String businessNo;

    /**
     * 资金方借据编号
     */
    private String loanNo;

    /**
     * 借据
     */
    private String sysId;

    /**
     * 还款编号
     */
    private String repayReqNo;


    private LocalDate repayDay;


    private LocalDateTime repayTime;

    /**
     * 期数
     */
    private Integer period;

    /**
     * 扣款类型
     */
    private String repayType;


    /**
     * 实还本金
     */
    private BigDecimal principalAmt;


    /**
     * 实还利息
     */
    private BigDecimal interestAmt;


    /**
     * 实还罚息
     */
    private BigDecimal penaltyAmt;

    /**
     * 优惠金额
     */
    private BigDecimal reduceAmt;

    /**
     * 代收金额
     */
    private BigDecimal withholdAmt;


    /**
     * 处理状态
     */
    private String status;

    /**
     * 资产池编号
     */
    private String channel;

    /**
     * 还款结果说明
     */
    private String result;

    private String repayMode;

    /**
     * 对账状态
     */
    private ReccStateEnum reccStatus;

    public String getReccId() {
        return reccId;
    }

    public void setReccId(String reccId) {
        this.reccId = reccId;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public String getSysId() {
        return sysId;
    }

    public void setSysId(String sysId) {
        this.sysId = sysId;
    }


    public String getBusinessNo() {
        return businessNo;
    }

    public void setBusinessNo(String businessNo) {
        this.businessNo = businessNo;
    }

    public String getRepayReqNo() {
        return repayReqNo;
    }

    public void setRepayReqNo(String repayReqNo) {
        this.repayReqNo = repayReqNo;
    }

    public LocalDate getRepayDay() {
        return repayDay;
    }

    public void setRepayDay(LocalDate repayDay) {
        this.repayDay = repayDay;
    }

    public LocalDateTime getRepayTime() {
        return repayTime;
    }

    public void setRepayTime(LocalDateTime repayTime) {
        this.repayTime = repayTime;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public String getRepayType() {
        return repayType;
    }

    public void setRepayType(String repayType) {
        this.repayType = repayType;
    }

    public BigDecimal getPrincipalAmt() {
        return principalAmt;
    }

    public void setPrincipalAmt(BigDecimal principalAmt) {
        this.principalAmt = principalAmt;
    }

    public BigDecimal getInterestAmt() {
        return interestAmt;
    }

    public void setInterestAmt(BigDecimal interestAmt) {
        this.interestAmt = interestAmt;
    }

    public BigDecimal getPenaltyAmt() {
        return penaltyAmt;
    }

    public void setPenaltyAmt(BigDecimal penaltyAmt) {
        this.penaltyAmt = penaltyAmt;
    }

    public BigDecimal getReduceAmt() {
        return reduceAmt;
    }

    public void setReduceAmt(BigDecimal reduceAmt) {
        this.reduceAmt = reduceAmt;
    }

    public BigDecimal getWithholdAmt() {
        return withholdAmt;
    }

    public void setWithholdAmt(BigDecimal withholdAmt) {
        this.withholdAmt = withholdAmt;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getRepayMode() {
        return repayMode;
    }

    public void setRepayMode(String repayMode) {
        this.repayMode = repayMode;
    }

    public ReccStateEnum getReccStatus() {
        return reccStatus;
    }

    public void setReccStatus(ReccStateEnum reccStatus) {
        this.reccStatus = reccStatus;
    }
}
