package com.jinghang.capital.batch.domain.cybk;

import com.aliyun.oss.common.comm.ResponseMessage;
import com.aliyun.oss.model.PutObjectResult;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.SftpException;
import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.batch.entity.CYBKReconcileFile;
import com.jinghang.capital.batch.enums.ReccStateEnum;
import com.jinghang.capital.batch.job.ReccJobParamEntity;
import com.jinghang.capital.batch.service.FileService;
import com.jinghang.capital.batch.service.WarningService;
import com.jinghang.capital.batch.service.cybk.CYBKReconcileFileService;
import com.jinghang.capital.batch.service.tc.IBankRepayRecordService;
import com.jinghang.capital.batch.service.tc.ILoanService;
import com.jinghang.capital.batch.service.tc.IReconciliationFileService;
import com.jinghang.common.sftp.Sftp;
import com.jinghang.common.util.SftpUtil;
import jakarta.annotation.PostConstruct;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;

public abstract class AbstractCYBKReconFileHandler<T> implements CYBKReconFileHandler {

    protected static final int DEFAULT_BATCH_SIZE = 500;
    protected static final String SEPARATOR = ",";
    private static final Logger logger = LoggerFactory.getLogger(AbstractCYBKReconFileHandler.class);
    /**
     * 对账文件下载路径
     */
    private static final String SFTP_PATH_PREFIX = "%s/in/files";
    @Value("${bucketName}")
    private String bucketName;
    @Autowired
    private CYBKConfig config;
    @Autowired
    private CYBKReconcileFileService cybkReconcileFileService;

    @Autowired
    private WarningService warningService;

    @Autowired
    private IReconciliationFileService reconciliationFileService;
    private Sftp sftp;
    private Sftp antSftp;
    @Autowired
    private FileService fileService;
    @Autowired
    private ILoanService loanService;
    @Autowired
    private IBankRepayRecordService bankRepayRecordService;

    protected static void appendStr(StringBuffer sftpFileStr, String line) {
        if (!sftpFileStr.isEmpty()) {
            sftpFileStr.append(System.lineSeparator());
        }
        sftpFileStr.append(line);
    }

    public Sftp getSftp() {
        return sftp;
    }

    public void setSftp(Sftp sftp) {
        this.sftp = sftp;
    }

    public Sftp getAntSftp() {
        return antSftp;
    }

    public void setAntSftp(Sftp antSftp) {
        this.antSftp = antSftp;
    }

    @PostConstruct
    public void init() {
        this.sftp = SftpUtil.use(config.getSftpUsername(), config.getSftpPassword(), config.getSftpHost(), config.getSftpPort());
        this.antSftp = SftpUtil.use(config.getAntSftpUsername(), config.getAntSftpPassword(), config.getAntSftpHost(), config.getAntSftpPort());
    }

    @Override
    public void handle(ReccJobParamEntity jobEntity) {
        LocalDate fileDate = LocalDate.parse(jobEntity.getFileDate());
        String ossFilePath = ossFilePath(fileDate);
        // 1 记录文件信息
        CYBKReconcileFile reconcileFile = new CYBKReconcileFile();
        initReconcileFile(fileDate, ossFilePath, reconcileFile);
        cybkReconcileFileService.save(reconcileFile);

        StringBuffer sftpLocalBuffer = new StringBuffer();
        List<T> entityList = new ArrayList<>();
        // 2 下载文件 & 3 解析文件
        String sftpFilePath = sftpFilePath(fileDate);
        logger.info("开始下载长银消金直连对账文件: [{}]", sftpFilePath);
        try {
            sftp.custom(channelSftp -> {
                try {
                    entityList.addAll(getReccFileDetails(channelSftp.get(sftpFilePath), sftpLocalBuffer));
                } catch (SftpException e) {
                    throw new RuntimeException((e.id == ChannelSftp.SSH_FX_NO_SUCH_FILE) ? "文件不存在" : "sftp下载长银消金直连对账文件失败, 查看日志详情", e);
                }
            });
        } catch (Exception e) {
            warningService.warn("长银消金直连对账文件%s下载失败|解析异常:%s".formatted(sftpFilePath, e.getMessage()), msg -> logger.error(msg, e));
            throw new RuntimeException(e.getMessage(), e);
        }
        // 4 上传到oss
        PutObjectResult putResult = uploadTpOss(sftpLocalBuffer.toString(), ossFilePath);
        setOssFileInfo(reconcileFile, ossFilePath, putResult);

        if (!CollectionUtils.isEmpty(entityList) && entityList.size() > 0) {
            // 5 填充数据
            fillEntityData(entityList, reconcileFile);
            // 6 数据入库
            saveEntity(entityList);
        }

        reconcileFile.setUpdatedTime(LocalDateTime.now());
        cybkReconcileFileService.updateById(reconcileFile);

        // 给fin-entrance的对账文件
        processTszRecFile(fileDate, entityList);
    }

    protected void processTszRecFile(LocalDate fileDate, List<T> entityList) {

    }

    private void initReconcileFile(LocalDate fileDate, String ossFilePath, CYBKReconcileFile reconcileFile) {
        reconcileFile.setChannel(BankChannel.CYBK.name());
        reconcileFile.setFileDate(fileDate);
        reconcileFile.setReccState(ReccStateEnum.P.name());
        reconcileFile.setReccType(getReccType().name());
        if (ossFilePath.contains("/")) {
            reconcileFile.setFileName(ossFilePath.substring(ossFilePath.lastIndexOf("/") + 1));
        } else {
            reconcileFile.setFileName(ossFilePath);
        }
        LocalDateTime now = LocalDateTime.now();
        reconcileFile.setCreatedTime(now);
        reconcileFile.setUpdatedTime(now);
    }

    private void setOssFileInfo(CYBKReconcileFile reconcileFile, String ossFilePath, PutObjectResult putResult) {
        ResponseMessage responseMessage = putResult.getResponse();
        reconcileFile.setOssBucket(ossBucketName());
        reconcileFile.setOssKey(ossFilePath);
        if (null != responseMessage) {
            reconcileFile.setOssUrl(responseMessage.getUri());
        }
    }

    private PutObjectResult uploadTpOss(String content, String filePath) {
        PutObjectResult putObject;
        try {
            if (StringUtils.isBlank(filePath)) {
                throw new RuntimeException("文件名字不能为空");
            }
            putObject = fileService.switchOss(ossBucketName()).putObject(
                    ossBucketName(),
                    filePath.startsWith("/") ? filePath.substring(1) : filePath,
                    new ByteArrayInputStream(content.getBytes()));
        } catch (Exception e) {
            logger.error("upload cybk file to oss error. filePath: {}", filePath, e);
            throw new RuntimeException(e);
        }
        return putObject;
    }

    protected abstract String ossFilePath(LocalDate data);

    protected abstract String sftpFilePath(LocalDate data);

    protected abstract void saveEntity(List<T> entityList);

    protected abstract void fillEntityData(List<T> entityList, CYBKReconcileFile reconcileFile);

    protected abstract List<T> getReccFileDetails(InputStream inputStream, StringBuffer sftpFileStr);

    protected String ossBucketName() {
        return bucketName;
    }

    protected String getSftpPathPrefix() {
        return config.getDownloadDir() + SFTP_PATH_PREFIX.formatted(config.getLoanType());
    }

    public WarningService getWarningService() {
        return warningService;
    }

    public ILoanService getLoanService() {
        return loanService;
    }

    public IBankRepayRecordService getBankRepayRecordService() {
        return bankRepayRecordService;
    }

    public CYBKReconcileFileService getCYBKReconcileFileService() {
        return cybkReconcileFileService;
    }

    public IReconciliationFileService getReconciliationFileService() {
        return reconciliationFileService;
    }
}
