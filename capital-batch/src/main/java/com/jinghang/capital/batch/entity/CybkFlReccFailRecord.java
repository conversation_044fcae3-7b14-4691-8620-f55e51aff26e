package com.jinghang.capital.batch.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

/**
 * 长银分润对账失败记录;
 *
 * <AUTHOR> http://www.chiner.pro
 * @date : 2024-12-31
 */
@TableName("cybk_fl_recc_fail_record")
public class CybkFlReccFailRecord extends BaseEntity implements Serializable {

    /**
     * 对账文件类型
     */
    private String reccType;
    /**
     * recc_id
     */
    private String reccId;
    /**
     * 对账明细ID
     */
    private String detailId;
    /**
     * 借据ID
     */
    private String loanId;
    /**
     * 还款期数
     */
    private Integer period;

    public String getReccType() {
        return reccType;
    }

    public void setReccType(String reccType) {
        this.reccType = reccType;
    }

    public String getReccId() {
        return reccId;
    }

    public void setReccId(String reccId) {
        this.reccId = reccId;
    }

    public String getDetailId() {
        return detailId;
    }

    public void setDetailId(String detailId) {
        this.detailId = detailId;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }
}
