package com.jinghang.capital.batch.job.cybk.recc;


import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.recc.ReccApplyDto;
import com.jinghang.capital.api.dto.recc.ReccResultDto;
import com.jinghang.capital.batch.remote.FinReccService;
import com.jinghang.common.util.JsonUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import java.time.LocalDate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 长银消金直连对账
 */
@Component
@JobHandler("cybkReccJob")
public class CYBKReccJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(CYBKReccJob.class);

    @Autowired
    private FinReccService reccService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        ReccApplyDto reccApplyDto = new ReccApplyDto();
        reccApplyDto.setChannel(BankChannel.CYBK);
        try {
            CYBKReccParam reccParam = JsonUtil.convertToObject(param, CYBKReccParam.class);
            if (reccParam.getReccType() == null) {
                logger.error("CYBK 对账开始, type is null");
                return ReturnT.FAIL;
            }
            reccApplyDto.setReccType(reccParam.getReccType());
            LocalDate reccDay = reccParam.getReccDay();
            if (null == reccDay) {
                reccDay = LocalDate.now().minusDays(1L);
            }
            reccApplyDto.setReccDay(reccDay);
        } catch (Exception ex) {
            logger.error("CYBK 对账 异常: ", ex);
            return ReturnT.FAIL;
        }
        return doProcessRecc(reccApplyDto);
    }

    public ReturnT<String> doProcessRecc(ReccApplyDto applyDto) {
        RestResult<ReccResultDto> result = reccService.process(applyDto);
        logger.info("CYBK对账结果. status: {}, content: {}", result.getCode(), result.getData());
        return ReturnT.SUCCESS;
    }


}
