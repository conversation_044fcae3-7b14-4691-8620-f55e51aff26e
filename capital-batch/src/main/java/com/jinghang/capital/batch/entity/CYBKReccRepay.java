package com.jinghang.capital.batch.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jinghang.capital.batch.enums.ReccStateEnum;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <p>
 * 长银消金直连 还款对账文件
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-21
 */
@TableName("cybk_recc_repay")
public class CYBKReccRepay extends BaseEntity implements Serializable {


    @Serial
    private static final long serialVersionUID = -6932682760210511669L;

    /**
     * recc_id
     */
    private String reccId;

    /**
     * 长银借据号
     */
    private String cybkLoanNo;
    /**
     * 长银交易流水号
     */
    private String transNo;
    /**
     * 长银记账流水号(还款流水号)
     */
    private String bankRepayNo;
    /**
     * 长银客户号
     */
    private String custId;

    /**
     * 借据
     */
    private String sysId;
    /**
     * 放款申请流水号
     */
    private String outLoanReq;

    /**
     * 还款编号
     */
    private String repayReqNo;


    private LocalDate repayDay;

    /**
     * 是否长银扣款 Y-是  N-否
     */
    private String isCyCharge;

    private BigDecimal actRepayAmount;

    /**
     * 实还本金
     */
    private BigDecimal actPrincipalAmt;


    /**
     * 实还利息
     */
    private BigDecimal actInterestAmt;


    /**
     * 实还罚息
     */
    private BigDecimal actPenaltyAmt;

    /**
     * 实还复利
     */
    private BigDecimal actReinterestAmt;

    /**
     * 实还费用
     */
    private BigDecimal actFeeAmt;

    /**
     * 实还担保费
     */
    private BigDecimal actGuaranteeFeeAmt;

    /**
     * 是否代偿  Y-是   N-否
     */
    private String isClaim;

    /**
     * 长银扣款时,发送给支付渠道的商户订单号
     */
    private String payMerchantNo;
    /**
     * 处理状态
     */
    private String status;

    /**
     * 资产池编号
     */
    private String channel;

    /**
     * 还款结果说明
     */
    private String result;

    /**
     * 还款模式
     * 01：到期日及之后的还款，一次还一期
     * 02：提前结清
     * 04：整笔借据逾期后，逾期结清
     * 06 ：提前还一期"
     */
    private String repayMode;

    /**
     * 对账状态
     */
    private ReccStateEnum reccStatus;

    public String getOutLoanReq() {
        return outLoanReq;
    }

    public void setOutLoanReq(String outLoanReq) {
        this.outLoanReq = outLoanReq;
    }

    public String getPayMerchantNo() {
        return payMerchantNo;
    }

    public void setPayMerchantNo(String payMerchantNo) {
        this.payMerchantNo = payMerchantNo;
    }

    public BigDecimal getActGuaranteeFeeAmt() {
        return actGuaranteeFeeAmt;
    }

    public void setActGuaranteeFeeAmt(BigDecimal actGuaranteeFeeAmt) {
        this.actGuaranteeFeeAmt = actGuaranteeFeeAmt;
    }

    public String getIsClaim() {
        return isClaim;
    }

    public void setIsClaim(String isClaim) {
        this.isClaim = isClaim;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getReccId() {
        return reccId;
    }

    public void setReccId(String reccId) {
        this.reccId = reccId;
    }

    public String getCybkLoanNo() {
        return cybkLoanNo;
    }

    public void setCybkLoanNo(String cybkLoanNo) {
        this.cybkLoanNo = cybkLoanNo;
    }

    public String getTransNo() {
        return transNo;
    }

    public void setTransNo(String transNo) {
        this.transNo = transNo;
    }

    public String getBankRepayNo() {
        return bankRepayNo;
    }

    public void setBankRepayNo(String bankRepayNo) {
        this.bankRepayNo = bankRepayNo;
    }

    public String getSysId() {
        return sysId;
    }

    public void setSysId(String sysId) {
        this.sysId = sysId;
    }

    public String getRepayReqNo() {
        return repayReqNo;
    }

    public void setRepayReqNo(String repayReqNo) {
        this.repayReqNo = repayReqNo;
    }

    public LocalDate getRepayDay() {
        return repayDay;
    }

    public void setRepayDay(LocalDate repayDay) {
        this.repayDay = repayDay;
    }

    public String getIsCyCharge() {
        return isCyCharge;
    }

    public void setIsCyCharge(String isCyCharge) {
        this.isCyCharge = isCyCharge;
    }

    public BigDecimal getActRepayAmount() {
        return actRepayAmount;
    }

    public void setActRepayAmount(BigDecimal actRepayAmount) {
        this.actRepayAmount = actRepayAmount;
    }

    public BigDecimal getActPrincipalAmt() {
        return actPrincipalAmt;
    }

    public void setActPrincipalAmt(BigDecimal actPrincipalAmt) {
        this.actPrincipalAmt = actPrincipalAmt;
    }

    public BigDecimal getActInterestAmt() {
        return actInterestAmt;
    }

    public void setActInterestAmt(BigDecimal actInterestAmt) {
        this.actInterestAmt = actInterestAmt;
    }

    public BigDecimal getActPenaltyAmt() {
        return actPenaltyAmt;
    }

    public void setActPenaltyAmt(BigDecimal actPenaltyAmt) {
        this.actPenaltyAmt = actPenaltyAmt;
    }

    public BigDecimal getActReinterestAmt() {
        return actReinterestAmt;
    }

    public void setActReinterestAmt(BigDecimal actReinterestAmt) {
        this.actReinterestAmt = actReinterestAmt;
    }

    public BigDecimal getActFeeAmt() {
        return actFeeAmt;
    }

    public void setActFeeAmt(BigDecimal actFeeAmt) {
        this.actFeeAmt = actFeeAmt;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getRepayMode() {
        return repayMode;
    }

    public void setRepayMode(String repayMode) {
        this.repayMode = repayMode;
    }

    public ReccStateEnum getReccStatus() {
        return reccStatus;
    }

    public void setReccStatus(ReccStateEnum reccStatus) {
        this.reccStatus = reccStatus;
    }
}
