package com.jinghang.capital.batch.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jinghang.capital.batch.enums.ReccStateEnum;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <p>
 * 长银消金直连 还款计划文件sdf
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-21  fixme  这个地方的表需要重新创建
 */
@TableName("cybk_recc_plan")
public class CYBKReccPlan extends BaseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = -6932682760210511669L;

    /**
     * recc_id
     */
    private String reccId;

    private String reccRepayId;
    private String sysId;
    private String creditId;

    /**
     * 长银借据号
     */
    private String cybkLoanNo;

    /**
     * 期数
     */
    private Integer period;


    /**
     * 还款编号
     */
    private String repayReqNo;


    /**
     * 还款日期
     */
    private LocalDate repayDay;
    /**
     * 到期日
     */
    private LocalDate dueDate;

    private BigDecimal repayAmount;
    private BigDecimal repayPrincipalAmt;
    private BigDecimal repayInterestAmt;
    private BigDecimal repayPenaltyAmt;
    private BigDecimal repayReinterestAmt;
    private BigDecimal repayGuaranteeAmt;
    private BigDecimal repayGuaranteeOverdueAmt;

    /**
     * 处理状态
     */
    private String status;

    /**
     * 资产池编号
     */
    private String channel;

    /**
     * 还款结果说明
     */
    private String result;

    /**
     * 对账状态
     */
    private ReccStateEnum reccStatus;

    public String getSysId() {
        return sysId;
    }

    public void setSysId(String sysId) {
        this.sysId = sysId;
    }

    public String getReccId() {
        return reccId;
    }

    public void setReccId(String reccId) {
        this.reccId = reccId;
    }

    public String getReccRepayId() {
        return reccRepayId;
    }

    public void setReccRepayId(String reccRepayId) {
        this.reccRepayId = reccRepayId;
    }

    public String getCreditId() {
        return creditId;
    }

    public void setCreditId(String creditId) {
        this.creditId = creditId;
    }

    public String getCybkLoanNo() {
        return cybkLoanNo;
    }

    public void setCybkLoanNo(String cybkLoanNo) {
        this.cybkLoanNo = cybkLoanNo;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public String getRepayReqNo() {
        return repayReqNo;
    }

    public void setRepayReqNo(String repayReqNo) {
        this.repayReqNo = repayReqNo;
    }

    public LocalDate getRepayDay() {
        return repayDay;
    }

    public void setRepayDay(LocalDate repayDay) {
        this.repayDay = repayDay;
    }

    public LocalDate getDueDate() {
        return dueDate;
    }

    public void setDueDate(LocalDate dueDate) {
        this.dueDate = dueDate;
    }

    public BigDecimal getRepayAmount() {
        return repayAmount;
    }

    public void setRepayAmount(BigDecimal repayAmount) {
        this.repayAmount = repayAmount;
    }

    public BigDecimal getRepayPrincipalAmt() {
        return repayPrincipalAmt;
    }

    public void setRepayPrincipalAmt(BigDecimal repayPrincipalAmt) {
        this.repayPrincipalAmt = repayPrincipalAmt;
    }

    public BigDecimal getRepayInterestAmt() {
        return repayInterestAmt;
    }

    public void setRepayInterestAmt(BigDecimal repayInterestAmt) {
        this.repayInterestAmt = repayInterestAmt;
    }

    public BigDecimal getRepayPenaltyAmt() {
        return repayPenaltyAmt;
    }

    public void setRepayPenaltyAmt(BigDecimal repayPenaltyAmt) {
        this.repayPenaltyAmt = repayPenaltyAmt;
    }

    public BigDecimal getRepayReinterestAmt() {
        return repayReinterestAmt;
    }

    public void setRepayReinterestAmt(BigDecimal repayReinterestAmt) {
        this.repayReinterestAmt = repayReinterestAmt;
    }

    public BigDecimal getRepayGuaranteeAmt() {
        return repayGuaranteeAmt;
    }

    public void setRepayGuaranteeAmt(BigDecimal repayGuaranteeAmt) {
        this.repayGuaranteeAmt = repayGuaranteeAmt;
    }

    public BigDecimal getRepayGuaranteeOverdueAmt() {
        return repayGuaranteeOverdueAmt;
    }

    public void setRepayGuaranteeOverdueAmt(BigDecimal repayGuaranteeOverdueAmt) {
        this.repayGuaranteeOverdueAmt = repayGuaranteeOverdueAmt;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public ReccStateEnum getReccStatus() {
        return reccStatus;
    }

    public void setReccStatus(ReccStateEnum reccStatus) {
        this.reccStatus = reccStatus;
    }
}
