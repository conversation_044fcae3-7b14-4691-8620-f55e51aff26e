package com.jinghang.capital.batch.job.cybk.recc;

import com.jinghang.capital.batch.domain.cybk.CYBKHandlerFactory;
import com.jinghang.capital.batch.domain.cybk.CYBKReccFileTypeEnum;
import com.jinghang.capital.batch.job.ReccUploadAntJobParamEntity;
import com.jinghang.common.util.JsonUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 长银消金{放款明细文件}和{还款明细文件}下载后做处理并上传到 蚂蚁的sftp
 *
 * <AUTHOR>
 * @Date 2025-07-31 14:33:00
 */
@Component
@JobHandler("cybkReconcilitionFileUploadToAntJob")
public class CYBKReconcilitionFileUploadToAntJob extends IJobHandler {
    private static final List<String> FILE_TYPES;
    private static final Logger logger = LoggerFactory.getLogger(CYBKReconcilitionFileUploadToAntJob.class);

    static {
        FILE_TYPES = List.of(
                CYBKReccFileTypeEnum.LOAN_FILE.name(),
                CYBKReccFileTypeEnum.REPAYMENT_FILE.name()
        );
    }

    @Autowired
    private CYBKHandlerFactory cybkHandlerFactory;


    @Override
    public ReturnT<String> execute(String s) throws Exception {

        ReccUploadAntJobParamEntity jobEntity = JsonUtil.convertToObject(s, ReccUploadAntJobParamEntity.class);
        List<String> fileTypes = CollectionUtils.isEmpty(jobEntity.getFileType()) ? FILE_TYPES : jobEntity.getFileType();

        // 1. 解析日期范围（支持单日或日期区间）
        LocalDate processingDate;
        if (StringUtils.isNotBlank(jobEntity.getStartDate()) && StringUtils.isNotBlank(jobEntity.getEndDate())) {
            // 区间模式
            LocalDate startDate = LocalDate.parse(jobEntity.getStartDate(), DateTimeFormatter.ISO_DATE);
            LocalDate endDate = LocalDate.parse(jobEntity.getEndDate(), DateTimeFormatter.ISO_DATE);

            if (startDate.isAfter(endDate)) {
                throw new RuntimeException("开始日期不能晚于结束日期");
            }

            // 遍历日期区间
            for (processingDate = startDate; !processingDate.isAfter(endDate); processingDate = processingDate.plusDays(1)) {
                executeForSingleDate(jobEntity, fileTypes, processingDate);
            }
        }
        // 单日模式（使用fileDate）
        processingDate = LocalDate.parse(
                StringUtils.defaultIfBlank(jobEntity.getFileDate(), LocalDate.now().minusDays(1).toString()),
                DateTimeFormatter.ISO_DATE
        );
        executeForSingleDate(jobEntity, fileTypes, processingDate);


        return ReturnT.SUCCESS;
    }

    /**
     * 执行单个日期的处理逻辑
     */
    private void executeForSingleDate(ReccUploadAntJobParamEntity originalJob, List<String> fileTypes, LocalDate date) {
        // 创建日期特定的jobEntity（避免修改原始对象）
        ReccUploadAntJobParamEntity dateJob = new ReccUploadAntJobParamEntity();
        BeanUtils.copyProperties(originalJob, dateJob);
        dateJob.setFileDate(date.format(DateTimeFormatter.ISO_DATE));

        logger.info("[执行日期] {}", date);

        // 处理所有指定文件类型
        for (String fileType : fileTypes) {
            if (!FILE_TYPES.contains(fileType)) {
                logger.error("无效文件类型: {}", fileType);
                continue; // 或 throw new RuntimeException
            }

            try {
                var handler = cybkHandlerFactory.get(fileType);
                handler.pushToAnt(dateJob);
                logger.info("[{}] 处理成功", fileType);
            } catch (Exception e) {
                logger.error("[{}] 处理失败", fileType, e);
            }
        }
    }

}
