package com.jinghang.capital.batch.service.tc;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jinghang.capital.batch.entity.BankLoanReplan;
import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 对资实还款计划表; 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16
 */
public interface IBankLoanReplanService extends IService<BankLoanReplan> {

    /**
     * 查询已代偿成功的对资实还计划
     *
     * @param date     代偿时间
     * @param channels 资方list
     * @return
     */
    List<BankLoanReplan> queryClaimBankLoanPlans(LocalDate date, List<String> channels);

    /**
     * 根据借据ID与期数，查询对资实还计划
     *
     * @param loanId
     * @param period
     * @return
     */
    BankLoanReplan findByLoanAndPeriod(String loanId, Integer period);

    /**
     * 查询结清还款数据
     *
     * @param loanId
     * @return
     */
    List<BankLoanReplan> queryByLoanIdAndRepayPurpose(String loanId);

}
