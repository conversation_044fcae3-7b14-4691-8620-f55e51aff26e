package com.jinghang.capital.batch.job.state.Dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jinghang.capital.api.dto.BankChannel;
import java.time.LocalDate;
import java.util.List;

public class WarningLoanFileParam {

    private List<String> fileType;

    private BankChannel bankChannel;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate reccDay;

    public List<String> getFileType() {
        return fileType;
    }

    public void setFileType(List<String> fileType) {
        this.fileType = fileType;
    }

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public LocalDate getReccDay() {
        return reccDay;
    }

    public void setReccDay(LocalDate reccDay) {
        this.reccDay = reccDay;
    }
}
