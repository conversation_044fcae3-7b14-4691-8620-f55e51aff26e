package com.jinghang.capital.batch.service;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.ObjectMetadata;
import com.jinghang.common.util.DateUtil;
import java.io.InputStream;
import java.net.URL;
import java.time.LocalDate;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/5/13
 */
@Service
public class FileService {
    private static final Logger logger = LoggerFactory.getLogger(FileService.class);

    private static final Long EXPIRATION_DAY = 7L;

    private OSS ossClient;

    public OSS switchOss(String bucketName) {
        return ossClient;
    }

    public String getOssUrl(String bucket, String key) {
        URL url = switchOss(bucket).generatePresignedUrl(bucket, key, DateUtil.toDate(LocalDate.now().plusDays(EXPIRATION_DAY)));
        return url.toString();
    }

    /**
     * 获取流需要关闭
     */
    public InputStream getOssFile(String bucket, String key) {
        return switchOss(bucket).getObject(bucket, key).getObjectContent();
    }

    public void uploadOss(String bucket, String key, InputStream inputStream) {
        switchOss(bucket).putObject(bucket, key, inputStream);
        IOUtils.closeQuietly(inputStream);
    }

    public long getOssFileSize(String bucket, String key) {
        ObjectMetadata objectMeta = switchOss(bucket).getObjectMetadata(bucket, key);
        return objectMeta.getContentLength();
    }

    @Autowired
    public void setOssClient(OSS ossClient) {
        this.ossClient = ossClient;
    }
}
