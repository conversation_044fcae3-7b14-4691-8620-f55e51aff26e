package com.jinghang.capital.batch.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jinghang.capital.batch.enums.ReccStateEnum;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <p>
 * 长银消金直连 还款对账文件
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-21
 */
@TableName("cybk_recc_repay_detail")
public class CYBKReccRepayDetail extends BaseEntity implements Serializable {


    @Serial
    private static final long serialVersionUID = -6932682760210511669L;

    /**
     * recc_id
     */
    private String reccId;

    private String reccRepayId;
    private String sysId;
    private String creditId;

    /**
     * 长银借据号
     */
    private String cybkLoanNo;

    private Integer period;


    /**
     * 还款编号
     */
    private String repayReqNo;


    /**
     * 还款日期
     */
    private LocalDate repayDay;
    /**
     * 到期日
     */
    private LocalDate dueDate;

    /**
     * 是否结清
     */
    private String isSettle;

    /**
     * 结清日期
     */
    private LocalDate settleDate;

    private BigDecimal repayAmount;
    private BigDecimal repayPrincipalAmt;
    private BigDecimal repayInterestAmt;
    private BigDecimal repayPenaltyAmt;
    private BigDecimal repayReinterestAmt;
    private BigDecimal repayFeeAmt;
    private BigDecimal repayGuaranteeAmt;
    private BigDecimal repayGuaranteeOverdueAmt;

    private BigDecimal actRepayAmount;

    /**
     * 实还本金
     */
    private BigDecimal actPrincipalAmt;


    /**
     * 实还利息
     */
    private BigDecimal actInterestAmt;


    /**
     * 实还罚息
     */
    private BigDecimal actPenaltyAmt;

    /**
     * 实还复利
     */
    private BigDecimal actReinterestAmt;

    /**
     * 实还费用
     */
    private BigDecimal actFeeAmt;

    /**
     * 实还担保费
     */
    private BigDecimal actGuaranteeFeeAmt;
    /**
     * 实还担保费罚息金额
     */
    private BigDecimal actGuaranteeOverdueAmt;
    /**
     * 处理状态
     */
    private String status;

    /**
     * 资产池编号
     */
    private String channel;

    /**
     * 还款结果说明
     */
    private String result;

    /**
     * 还款模式
     * 01：到期日及之后的还款，一次还一期
     * 02：提前结清
     * 04：整笔借据逾期后，逾期结清
     * 06 ：提前还一期"
     */
    private String repayMode;

    /**
     * 对账状态
     */
    private ReccStateEnum reccStatus;

    public String getSysId() {
        return sysId;
    }

    public void setSysId(String sysId) {
        this.sysId = sysId;
    }

    public String getReccId() {
        return reccId;
    }

    public void setReccId(String reccId) {
        this.reccId = reccId;
    }

    public String getReccRepayId() {
        return reccRepayId;
    }

    public void setReccRepayId(String reccRepayId) {
        this.reccRepayId = reccRepayId;
    }

    public String getCreditId() {
        return creditId;
    }

    public void setCreditId(String creditId) {
        this.creditId = creditId;
    }

    public String getCybkLoanNo() {
        return cybkLoanNo;
    }

    public void setCybkLoanNo(String cybkLoanNo) {
        this.cybkLoanNo = cybkLoanNo;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public String getRepayReqNo() {
        return repayReqNo;
    }

    public void setRepayReqNo(String repayReqNo) {
        this.repayReqNo = repayReqNo;
    }

    public LocalDate getRepayDay() {
        return repayDay;
    }

    public void setRepayDay(LocalDate repayDay) {
        this.repayDay = repayDay;
    }

    public LocalDate getDueDate() {
        return dueDate;
    }

    public void setDueDate(LocalDate dueDate) {
        this.dueDate = dueDate;
    }

    public String getIsSettle() {
        return isSettle;
    }

    public void setIsSettle(String isSettle) {
        this.isSettle = isSettle;
    }

    public LocalDate getSettleDate() {
        return settleDate;
    }

    public void setSettleDate(LocalDate settleDate) {
        this.settleDate = settleDate;
    }

    public BigDecimal getRepayAmount() {
        return repayAmount;
    }

    public void setRepayAmount(BigDecimal repayAmount) {
        this.repayAmount = repayAmount;
    }

    public BigDecimal getRepayPrincipalAmt() {
        return repayPrincipalAmt;
    }

    public void setRepayPrincipalAmt(BigDecimal repayPrincipalAmt) {
        this.repayPrincipalAmt = repayPrincipalAmt;
    }

    public BigDecimal getRepayInterestAmt() {
        return repayInterestAmt;
    }

    public void setRepayInterestAmt(BigDecimal repayInterestAmt) {
        this.repayInterestAmt = repayInterestAmt;
    }

    public BigDecimal getRepayPenaltyAmt() {
        return repayPenaltyAmt;
    }

    public void setRepayPenaltyAmt(BigDecimal repayPenaltyAmt) {
        this.repayPenaltyAmt = repayPenaltyAmt;
    }

    public BigDecimal getRepayReinterestAmt() {
        return repayReinterestAmt;
    }

    public void setRepayReinterestAmt(BigDecimal repayReinterestAmt) {
        this.repayReinterestAmt = repayReinterestAmt;
    }

    public BigDecimal getRepayFeeAmt() {
        return repayFeeAmt;
    }

    public void setRepayFeeAmt(BigDecimal repayFeeAmt) {
        this.repayFeeAmt = repayFeeAmt;
    }

    public BigDecimal getRepayGuaranteeAmt() {
        return repayGuaranteeAmt;
    }

    public void setRepayGuaranteeAmt(BigDecimal repayGuaranteeAmt) {
        this.repayGuaranteeAmt = repayGuaranteeAmt;
    }

    public BigDecimal getRepayGuaranteeOverdueAmt() {
        return repayGuaranteeOverdueAmt;
    }

    public void setRepayGuaranteeOverdueAmt(BigDecimal repayGuaranteeOverdueAmt) {
        this.repayGuaranteeOverdueAmt = repayGuaranteeOverdueAmt;
    }

    public BigDecimal getActRepayAmount() {
        return actRepayAmount;
    }

    public void setActRepayAmount(BigDecimal actRepayAmount) {
        this.actRepayAmount = actRepayAmount;
    }

    public BigDecimal getActPrincipalAmt() {
        return actPrincipalAmt;
    }

    public void setActPrincipalAmt(BigDecimal actPrincipalAmt) {
        this.actPrincipalAmt = actPrincipalAmt;
    }

    public BigDecimal getActInterestAmt() {
        return actInterestAmt;
    }

    public void setActInterestAmt(BigDecimal actInterestAmt) {
        this.actInterestAmt = actInterestAmt;
    }

    public BigDecimal getActPenaltyAmt() {
        return actPenaltyAmt;
    }

    public void setActPenaltyAmt(BigDecimal actPenaltyAmt) {
        this.actPenaltyAmt = actPenaltyAmt;
    }

    public BigDecimal getActReinterestAmt() {
        return actReinterestAmt;
    }

    public void setActReinterestAmt(BigDecimal actReinterestAmt) {
        this.actReinterestAmt = actReinterestAmt;
    }

    public BigDecimal getActFeeAmt() {
        return actFeeAmt;
    }

    public void setActFeeAmt(BigDecimal actFeeAmt) {
        this.actFeeAmt = actFeeAmt;
    }

    public BigDecimal getActGuaranteeFeeAmt() {
        return actGuaranteeFeeAmt;
    }

    public void setActGuaranteeFeeAmt(BigDecimal actGuaranteeFeeAmt) {
        this.actGuaranteeFeeAmt = actGuaranteeFeeAmt;
    }

    public BigDecimal getActGuaranteeOverdueAmt() {
        return actGuaranteeOverdueAmt;
    }

    public void setActGuaranteeOverdueAmt(BigDecimal actGuaranteeOverdueAmt) {
        this.actGuaranteeOverdueAmt = actGuaranteeOverdueAmt;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getRepayMode() {
        return repayMode;
    }

    public void setRepayMode(String repayMode) {
        this.repayMode = repayMode;
    }

    public ReccStateEnum getReccStatus() {
        return reccStatus;
    }

    public void setReccStatus(ReccStateEnum reccStatus) {
        this.reccStatus = reccStatus;
    }
}
