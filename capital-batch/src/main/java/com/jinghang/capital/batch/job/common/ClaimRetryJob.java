package com.jinghang.capital.batch.job.common;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.repay.ClaimRetryDto;
import com.jinghang.capital.batch.remote.FinRepayService;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 代偿重试
 */
@Component
@JobHandler("claimRetryJob")
public class ClaimRetryJob extends IJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(ClaimRetryJob.class);

    @Autowired
    private FinRepayService repayService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {

        // {"claimDay": "2023-05-01", "bankChannel": "LH_RL"}
        LocalDate claimDate = null;
        BankChannel bankChannel = null;
        if (!StringUtil.isEmpty(param)) {
            ClaimRetryParam claimRetryParam = JsonUtil.convertToObject(param, ClaimRetryParam.class);
            claimDate = claimRetryParam.getClaimDay();
            bankChannel = BankChannel.valueOf(claimRetryParam.getBankChannel());
        }

        if (null == claimDate) {
            logger.error("claimDate is null");
            return ReturnT.FAIL;
        }

        logger.info("claim fail retry, claimDate: {}, bankChannel: {}", claimDate.format(DateTimeFormatter.ISO_DATE), bankChannel);
        ClaimRetryDto claimRetryDto = new ClaimRetryDto();
        claimRetryDto.setClaimDay(claimDate);
        claimRetryDto.setBankChannel(bankChannel);
        logger.info("claim fail retry, claimRetryDto: {}", JsonUtil.toJsonString(claimRetryDto));
        repayService.claimRetry(claimRetryDto);
        return ReturnT.SUCCESS;
    }
}
