package com.jinghang.capital.batch.job.state.Dto;


import com.jinghang.capital.api.dto.RepayChannel;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/5/30
 */
public class RepayRecordDto {

    private String id;
    /**
     * 借据
     */
    private String loanId;
    /**
     * 期次
     */
    private Integer period;
    /**
     * 还款目的
     */
    private String repayPurpose;
    /**
     * 申请日期
     */
    private LocalDateTime repayApplyDate;
    /**
     * 成功日期
     */
    private LocalDateTime repaidDate;
    /**
     * 还款模式（线上，线下）
     */
    private String repayMode;
    /**
     * 还款支付方式
     */
    private RepayChannel repayChannel;
    /**
     * 支付通道
     */
    private String channelId;
    /**
     * 状态
     */
    private String repayState;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public String getRepayPurpose() {
        return repayPurpose;
    }

    public void setRepayPurpose(String repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public LocalDateTime getRepayApplyDate() {
        return repayApplyDate;
    }

    public void setRepayApplyDate(LocalDateTime repayApplyDate) {
        this.repayApplyDate = repayApplyDate;
    }

    public LocalDateTime getRepaidDate() {
        return repaidDate;
    }

    public void setRepaidDate(LocalDateTime repaidDate) {
        this.repaidDate = repaidDate;
    }

    public String getRepayMode() {
        return repayMode;
    }

    public void setRepayMode(String repayMode) {
        this.repayMode = repayMode;
    }

    public RepayChannel getRepayChannel() {
        return repayChannel;
    }

    public void setRepayChannel(RepayChannel repayChannel) {
        this.repayChannel = repayChannel;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getRepayState() {
        return repayState;
    }

    public void setRepayState(String repayState) {
        this.repayState = repayState;
    }
}
