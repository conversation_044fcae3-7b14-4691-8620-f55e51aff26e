package com.jinghang.capital.batch.job.state;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jinghang.capital.api.dto.ProcessStatus;
import com.jinghang.capital.api.dto.repay.RepayMode;
import com.jinghang.capital.batch.entity.BankRepayRecord;
import com.jinghang.capital.batch.mapper.BankRepayRecordMapper;
import com.jinghang.capital.batch.service.WarningService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@JobHandler("warningOfflineFailJob")
public class WarningOfflineFailJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(WarningOfflineFailJob.class);

    @Resource(name = "warningStateService")
    private WarningService warningService;

    @Autowired
    private BankRepayRecordMapper bankRepayRecordMapper;

    private final Long subtractionHours = 24L;

    @Value("${warningJob.size}")
    private Integer size;

    /**
     * 还款预警阈值
     */
    @Value("${warningJob.repay.threshold:100}")
    private int repayThreshold;

    /**
     * 达到阈值时@指定人
     */
    @Value("${warningJob.repay.at:}")
    private String warningAt;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        logger.info("warningOfflineFailJob start");
        LocalDateTime end = LocalDateTime.now().minusHours(1L);
        LocalDateTime start = end.minusHours(subtractionHours);
        logger.info("查询开始时间为:{},结束时间为:{}", start, end);
        StringBuilder sb = new StringBuilder();

        //是否@相关人
        boolean isAt = false;

        // 对资线下还款失败
        LambdaQueryWrapper<BankRepayRecord> failQueryWrapper = new LambdaQueryWrapper<>();
        failQueryWrapper
                .eq(BankRepayRecord::getRepayMode, RepayMode.OFFLINE.name())
                .eq(BankRepayRecord::getRepayStatus, ProcessStatus.FAIL.name())
                .between(BankRepayRecord::getRepayTime, start, end);
        List<BankRepayRecord> failRepayRecords = bankRepayRecordMapper.selectList(failQueryWrapper);

        // 对资线下还款成功
        LambdaQueryWrapper<BankRepayRecord> successQueryWrapper = new LambdaQueryWrapper<>();
        successQueryWrapper
                .eq(BankRepayRecord::getRepayMode, RepayMode.OFFLINE.name())
                .eq(BankRepayRecord::getRepayStatus, ProcessStatus.SUCCESS.name())
                .between(BankRepayRecord::getRepayTime, start, end);
        List<BankRepayRecord> successRepayRecords = bankRepayRecordMapper.selectList(successQueryWrapper);

        Set<String> successLoanPeriodSet = new HashSet<>();
        for (BankRepayRecord successRecord : successRepayRecords) {
            String key = successRecord.getLoanId() + "_" + successRecord.getPeriod();
            successLoanPeriodSet.add(key);
        }

        // 过滤掉已经成功还款的记录
        failRepayRecords.removeIf(failRecord -> {
            String key = failRecord.getLoanId() + "_" + failRecord.getPeriod();
            return successLoanPeriodSet.contains(key);
        });

        int bankRepayRecordCount = failRepayRecords.size();

        if (bankRepayRecordCount >= size) {
            sb.append(message(start, end, "bank_repay_record", bankRepayRecordCount)).append("\n").append("\n");

            if (bankRepayRecordCount >= repayThreshold) {
                isAt = true;
            }
            // 将所有 failRepayRecords 的 id 收集到一个字符串中
            String ids = failRepayRecords.stream()
                    .map(BankRepayRecord::getId)
                    .map(String::valueOf)
                    .collect(Collectors.joining(", "));

            logger.info("线下还款失败 bankRepayRecordIds: {}", ids);
        }

        if (!sb.isEmpty()) {
            if (isAt) {
                warningService.warn(sb.toString(), warningAt.split(","));
            } else {
                warningService.warn(sb.toString());
            }
        }
        logger.info("warningOfflineFailJob end");
        return ReturnT.SUCCESS;
    }

    public String message(LocalDateTime start, LocalDateTime end, String table, Integer number) {
        StringBuilder string = new StringBuilder();
        string.append(formatLocalDateTime(start)).append(" ~ ").append(formatLocalDateTime(end))
                .append("\n")
                .append("fin-service.").append(table).append("线下还款失败条数为").append(number);
        return string.toString();
    }

    public static String formatLocalDateTime(LocalDateTime localDateTime) {
        return localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
}

