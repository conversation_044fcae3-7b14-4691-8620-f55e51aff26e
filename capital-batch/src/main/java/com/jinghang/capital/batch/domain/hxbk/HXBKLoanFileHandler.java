package com.jinghang.capital.batch.domain.hxbk;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.FileMode;
import com.jinghang.capital.api.dto.FileType;
import com.jinghang.capital.api.dto.Product;
import com.jinghang.capital.batch.domain.product.LoanReccDto;
import com.jinghang.capital.batch.entity.CYBKReconcileFile;
import com.jinghang.capital.batch.entity.Loan;
import com.jinghang.capital.batch.entity.ReconciliationFile;
import com.jinghang.capital.batch.entity.hxbk.HXBKReccLoan;
import com.jinghang.capital.batch.enums.ReccStateEnum;
import com.jinghang.capital.batch.service.FileService;
import com.jinghang.capital.batch.service.hxbk.HXBKReccLoanService;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 放款合约文件处理
 *
 * @作者 Mr.sandman
 * @时间 2025/07/09 17:02
 */
@Component
public class HXBKLoanFileHandler extends AbstractHXBKReconFileHandler<HXBKReccLoan> {
    private static final Logger logger = LoggerFactory.getLogger(HXBKLoanFileHandler.class);


    @Autowired
    private FileService fileService;
    @Autowired
    private HXBKReccLoanService hxbkReccLoanService;
    @Autowired
    private HXBKConfig hxbkConfig;
    private static final String RECC_FILE_NAME = "%s/loan_detail_%s.csv";

    private static final int ID_0 = 0;
    private static final int ID_1 = 1;
    private static final int ID_2 = 2;
    private static final int ID_3 = 3;
    private static final int ID_4 = 4;
    private static final int ID_5 = 5;
    private static final int ID_6 = 6;
    private static final int ID_7 = 7;
    private static final int ID_8 = 8;
    private static final int ID_9 = 9;
    private static final int ID_10 = 10;
    private static final int ID_11 = 11;
    private static final int ID_12 = 12;
    private static final int ID_13 = 13;
    private static final int ID_14 = 14;
    private static final int ID_15 = 15;
    private static final int ID_16 = 16;
    private static final int ID_17 = 17;
    private static final int ID_18 = 18;
    private static final int ID_19 = 19;
    private static final Integer LINE_LENGTH = 20;

    @Override
    protected String ossFilePath(LocalDate data) {
        return "hxbk/recc/loan/" + getFileName(data);
    }

    @Override
    protected String sftpFilePath(LocalDate data) {
        return hxbkConfig.getLoanDir() + getFileName(data);
    }

    private String getFileName(LocalDate data) {
        String dateStr1 = data.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return RECC_FILE_NAME.formatted(dateStr1, dateStr1);
    }

    @Override
    protected List<HXBKReccLoan> getReccFileDetails(InputStream inputStream, StringBuffer sftpFileStr) {
        List<HXBKReccLoan> entityList = new ArrayList<>();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
            String line;
            int lineNum = 0;
            while ((line = reader.readLine()) != null) {
                line = line.trim();

                if (line.isEmpty()) {
                    continue;
                }
                lineNum++;
                if (lineNum == 1) {
                    continue;
                }
                appendStr(sftpFileStr, line);
                String[] lineArr = line.split(SEPARATOR, LINE_LENGTH);
                if (lineArr.length < LINE_LENGTH) {
                    throw new RuntimeException("湖消直连解析数据异常,line:" + (entityList.size() + 1) + "__" + line);
                }

                String loanNo = lineArr[ID_0];

                if (!loanNo.startsWith("LO")) {
                    logger.info("reccLoan loanId: [{}] 非本平台固有借据号格式 忽略", loanNo);
                    continue;
                }
                // contract_no既是我们的借据号
                Loan loan = getLoanService().findByIdAndChannel(loanNo, BankChannel.HXBK.name());
                if (Objects.isNull(loan)) {
                    logger.info("reccLoan loanId: [{}] 未查到数据库匹配信息 忽略", loanNo);
                    continue;
                }

                // 落库保存湖消直连放款数据
                HXBKReccLoan entity = new HXBKReccLoan();
                if (loan != null) {
                    entity.setSysId(loan.getId());
                }
                entity.setContractNo(lineArr[ID_0]);
                entity.setName(lineArr[ID_1]);
                entity.setCertNo(lineArr[ID_2]);
                entity.setLoanUse(lineArr[ID_3]);
                entity.setApplyDate(lineArr[ID_4]);
                entity.setEncashDate(lineArr[ID_5]);
                entity.setEncashAmt(new BigDecimal(lineArr[ID_6]));
                entity.setStartDate(lineArr[ID_7]);
                entity.setEndDate(lineArr[ID_8]);
                entity.setBillDate(lineArr[ID_9]);
                entity.setTotalTerms(Integer.valueOf(lineArr[ID_10]));
                entity.setRepayMode(lineArr[ID_11]);
                entity.setGraceDay(Integer.valueOf(lineArr[ID_12]));
                entity.setDayRate(new BigDecimal(lineArr[ID_13]));
                entity.setYearRate(new BigDecimal(lineArr[ID_14]));
                entity.setCouponRate(new BigDecimal(lineArr[ID_15]));
                entity.setCreditNo(lineArr[ID_16]);
                entity.setApplyNo(lineArr[ID_17]);
                entity.setOtherData(lineArr[ID_18]);
                entity.setBsnType(lineArr[ID_19]);
                entity.setChannel(BankChannel.HXBK.name());
                entityList.add(entity);

            }
        } catch (Exception e) {
            logger.error("湖消放款对账文件解析异常", e);
            throw new RuntimeException("湖消直连解析文件失败", e);
        }
        return entityList;
    }

    public void processTszRecFile(LocalDate fileDate, List<HXBKReccLoan> entityList) {
        List<LoanReccDto> result = new ArrayList<>();
        for (HXBKReccLoan reccLoan : entityList) {
            // 这里需要跟蚂蚁确定是否是我们提供的借据号 reccLoan.getContractNo() 已确定是我们提供的借据号
            Loan loan = getLoanService().findByIdAndChannel(reccLoan.getContractNo(), BankChannel.HXBK.name());
            if (loan != null) {
                LoanReccDto tszRecc = toTszRecc(reccLoan);
                tszRecc.setOutLoanId(loan.getOuterLoanId());
                tszRecc.setChannel(BankChannel.HXBK.name());
                result.add(tszRecc);
            }
        }

        try {
            Path tempFile = Files.createTempFile("tsz_loan_recc", "txt");
            BufferedWriter writer = Files.newBufferedWriter(tempFile);
            for (LoanReccDto dto : result) {
                writer.write(dto.getFormattedTxt());
                writer.newLine();
            }
            writer.close();

            String targetFileName = fileDate.format(DateTimeFormatter.BASIC_ISO_DATE) + "_loanCheck.txt";
            String ossPath = "tsz/recc/hxbk/loan/" + targetFileName;
            fileService.switchOss(ossBucketName()).putObject(ossBucketName(), ossPath, Files.newInputStream(tempFile));

            ReconciliationFile tszRecFile = new ReconciliationFile();

            tszRecFile.setProduct(Product.ZC_CASH);
            tszRecFile.setBankChannel(BankChannel.HXBK);
            tszRecFile.setFileType(FileType.LOAN_FILE);
            tszRecFile.setFileName(targetFileName);
            tszRecFile.setFileDate(fileDate);
            tszRecFile.setMode(FileMode.OSS);
            tszRecFile.setReconciliationState(ReccStateEnum.P);
            tszRecFile.setTargetOssBucket(ossBucketName());
            tszRecFile.setTargetOssKey(ossPath);

            getReconciliationFileService().save(tszRecFile);

            Files.delete(tempFile);

        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private LoanReccDto toTszRecc(HXBKReccLoan reccLoan) {
        LoanReccDto dto = new LoanReccDto();
        dto.setLoanId(reccLoan.getContractNo());
        dto.setAmount(reccLoan.getEncashAmt());
        dto.setPeriod(reccLoan.getTotalTerms());
        dto.setLoanTime(safeParseDate(reccLoan.getEncashDate()));
        // dto.setStatus(reccLoan.getLoanStatus());
        dto.setRemark(reccLoan.getRemark());
        return dto;
    }

    // 在 HXBKLoanFileHandler 类中添加日期处理方法
    private LocalDate safeParseDate(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty() || "null".equalsIgnoreCase(dateStr.trim())) {
            return LocalDate.now(); // 或者返回 null，取决于业务需求
        }

        try {
            // 处理 "2025/7/28 9:54" 这种格式
            String trimmedDate = dateStr.trim();
            if (trimmedDate.contains(" ")) {
                // 只取日期部分
                trimmedDate = trimmedDate.split(" ")[0];
            }

            // 解析不同格式的日期
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/M/d");
            return LocalDate.parse(trimmedDate, formatter);
        } catch (Exception e) {
            logger.warn("无法解析日期: {}, 使用当前日期", dateStr);
            return LocalDate.now(); // 或者返回 null，取决于业务需求
        }
    }

    @Override
    protected void saveEntity(List<HXBKReccLoan> entityList) {
        hxbkReccLoanService.saveBatch(entityList, DEFAULT_BATCH_SIZE);
    }

    @Override
    protected void fillEntityData(List<HXBKReccLoan> entityList, CYBKReconcileFile reconcileFile) {
        entityList.forEach(e -> {
            e.setReccStatus(null);
            e.setReccId(reconcileFile.getId());
            e.setCreatedTime(LocalDateTime.now());
            e.setUpdatedTime(LocalDateTime.now());
        });
    }

    /**
     * 获取文件类型
     *
     * @return 文件类型
     */
    @Override
    public HXBKReccFileTypeEnum getReccType() {
        return HXBKReccFileTypeEnum.LOAN_FILE;
    }
}
