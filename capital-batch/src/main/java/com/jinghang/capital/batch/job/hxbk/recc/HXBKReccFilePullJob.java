package com.jinghang.capital.batch.job.hxbk.recc;

import com.jinghang.capital.batch.domain.hxbk.HXBKHandlerFactory;
import com.jinghang.capital.batch.domain.hxbk.HXBKReccFileTypeEnum;
import com.jinghang.capital.batch.job.AbstractBaseJob;
import com.jinghang.capital.batch.job.ReccJobParamEntity;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 湖消对账文件下载任务
 *
 * @作者 Mr.sandman
 * @时间 2025/07/09 16:55
 */
@Component
@JobHandler("hxbkReccFilePullJob")
public class HXBKReccFilePullJob extends AbstractBaseJob {

    @Autowired
    private HXBKHandlerFactory hxbkHandlerFactory;
    private static final List<String> FILE_TYPES;

    static {
        FILE_TYPES = List.of(
                HXBKReccFileTypeEnum.LOAN_FILE.name(),
                HXBKReccFileTypeEnum.REPAYMENT_DETAIL_FILE.name());
    }

    @Override
    public ReturnT<String> doExecute(ReccJobParamEntity jobEntity) throws Exception {
        List<String> fileTypes = CollectionUtils.isEmpty(jobEntity.getFileType())
                ? FILE_TYPES
                : jobEntity.getFileType();

        for (String type : fileTypes) {
            if (!FILE_TYPES.contains(type)) {
                logger.error("对账文件下载: 文件类型 [{}] 不存在", type);
                throw new RuntimeException("文件类型不存在:" + type);
            }
            logger.info("对账文件 [{}] 下载 开始。" + type);

            var handler = hxbkHandlerFactory.get(type);
            try {
                handler.handle(jobEntity);
            } catch (Exception ex) {
                logger.error("reccType: [{}], 对账文件下载处理异常: ", handler.getReccType().name(), ex);
            }

            logger.info("对账文件 [{}] [{}] [{}]下载 结束。", type, jobEntity.getChannelType(), jobEntity.getFileDate());
        }
        return ReturnT.SUCCESS;
    }
}
