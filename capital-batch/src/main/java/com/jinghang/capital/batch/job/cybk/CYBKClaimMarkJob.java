package com.jinghang.capital.batch.job.cybk;


import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.repay.ClaimMarkApplyDto;
import com.jinghang.capital.api.dto.repay.ClaimMarkResultDto;
import com.jinghang.capital.batch.remote.FinRepayService;
import com.jinghang.common.util.JsonUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import java.time.LocalDate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 长银消金直连标记代偿
 */
@Component
@JobHandler("cybkClaimMarkJob")
public class CYBKClaimMarkJob extends IJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(CYBKClaimMarkJob.class);

    @Autowired
    private FinRepayService repayService;

    private static final long THREE = 31;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        // 应还款日
        // {"repayDay": "2023-05-01"}, default is  yesterday
        LocalDate repayDay = LocalDate.now().minusDays(THREE);
        try {
            CYBKClaimMarkParam markParam = JsonUtil.convertToObject(param, CYBKClaimMarkParam.class);
            LocalDate paramDay = markParam.getRepayDay();
            if (null != paramDay) {
                repayDay = paramDay;
            }
        } catch (Exception ex) {
            // no throw
        }
        logger.info("cybk claim mark, cul_repay_day: {}", repayDay);
        return doClaimMark(repayDay);
    }

    public ReturnT<String> doClaimMark(LocalDate repayDay) throws Exception {
        ClaimMarkApplyDto applyDto = new ClaimMarkApplyDto();
        applyDto.setChannel(BankChannel.CYBK);
        applyDto.setRepayDay(repayDay);
        RestResult<ClaimMarkResultDto> result = repayService.claimMark(applyDto);
        logger.info("cybk claim mark result. status: {}, content: {}", result.getCode(), JsonUtil.convertToString(result.getData()));
        return ReturnT.SUCCESS;
    }


}
