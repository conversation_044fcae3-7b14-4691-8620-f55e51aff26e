package com.jinghang.capital.batch.domain.hxbk;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.batch.entity.CYBKReconcileFile;
import com.jinghang.capital.batch.entity.Loan;
import com.jinghang.capital.batch.entity.hxbk.HXBKReccRepay;
import com.jinghang.capital.batch.service.hxbk.HXBKReccRepayService;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 描述:还款合约明细文件处理
 *
 * @作者 Mr.sandman
 * @时间 2025/07/09 18:05
 */
@Component
public class HXBKRepayDetailFileHandler extends AbstractHXBKReconFileHandler<HXBKReccRepay> {

    private static final Logger logger = LoggerFactory.getLogger(HXBKRepayDetailFileHandler.class);

    private static final String RECC_FILE_NAME = "%s/repay_loan_detail_%s.csv";

    private static final int ID_0 = 0;
    private static final int ID_1 = 1;
    private static final int ID_2 = 2;
    private static final int ID_3 = 3;
    private static final int ID_4 = 4;
    private static final int ID_5 = 5;
    private static final int ID_6 = 6;
    private static final int ID_7 = 7;
    private static final int ID_8 = 8;
    private static final int ID_9 = 9;
    private static final int ID_10 = 10;
    private static final int ID_11 = 11;
    private static final int ID_12 = 12;
    private static final int ID_13 = 13;
    private static final int ID_14 = 14;
    private static final int ID_15 = 15;
    private static final int ID_16 = 16;
    private static final int ID_17 = 17;

    private static final Integer LINE_LENGTH = 18;

    @Autowired
    private HXBKReccRepayService hxbkReccRepayService;
    @Autowired
    private HXBKConfig hxbkConfig;

    @Override
    protected String ossFilePath(LocalDate data) {
        return "hxbk/recc/repayPlan/" + getFileName(data);
    }

    @Override
    protected String sftpFilePath(LocalDate data) {
        return hxbkConfig.getRepayDir() + getFileName(data);
    }

    private String getFileName(LocalDate data) {
        String dateStr1 = data.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return RECC_FILE_NAME.formatted(dateStr1, dateStr1);
    }

    @Override
    protected List<HXBKReccRepay> getReccFileDetails(InputStream inputStream, StringBuffer sftpFileStr) {
        List<HXBKReccRepay> reccRepayList = new ArrayList<>();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
            String line;
            int lineNum = 0;
            while ((line = reader.readLine()) != null) {
                line = line.trim();

                if (line.isEmpty()) {
                    continue;
                }
                lineNum++;
                if (lineNum == 1) {
                    continue;
                }
                appendStr(sftpFileStr, line);
                String[] lineArr = line.split(SEPARATOR, LINE_LENGTH);
                if (lineArr.length < LINE_LENGTH) {
                    throw new RuntimeException("湖消直连, 解析还款数据异常,line:" + (reccRepayList.size() + 1) + "__" + line);
                }

                String loanNo = lineArr[ID_0];

                if (!loanNo.startsWith("LO")) {
                    logger.info("reccRepaymentPlan loanId: [{}] 非本平台固有借据号格式 忽略", loanNo);
                    continue;
                }
                // contract_no既是我们的借据号
                Loan loan = getLoanService().findByIdAndChannel(loanNo, BankChannel.HXBK.name());
                if (Objects.isNull(loan)) {
                    logger.info("reccRepaymentPlan loanId: [{}] 未查到数据库匹配信息 忽略", loanNo);
                    continue;
                }

                // 还款计划落库到表中
                HXBKReccRepay entity = new HXBKReccRepay();
                if (loan != null) {
                    entity.setSysId(loan.getId());
                }
                entity.setContractNo(lineArr[ID_0]);
                entity.setSeqNo(lineArr[ID_1]);
                entity.setFeeNo(lineArr[ID_2]);
                entity.setRepayType(lineArr[ID_3]);
                entity.setRepayDate(lineArr[ID_4]);
                entity.setRepayAmt(new BigDecimal(lineArr[ID_5]));
                entity.setPaidPrinAmt(new BigDecimal(lineArr[ID_6]));
                entity.setPaidIntAmt(new BigDecimal(lineArr[ID_7]));
                entity.setPaidGuarIntAmt(new BigDecimal(lineArr[ID_8]));
                entity.setPaidOvdGuarIntAmt(new BigDecimal(lineArr[ID_9]));
                entity.setPaidOvdPrinPnltAmt(new BigDecimal(lineArr[ID_10]));
                entity.setPaidOvdIntPnltAmt(new BigDecimal(lineArr[ID_11]));
                entity.setPaidOvdGuarIntPnltAmt(new BigDecimal(lineArr[ID_12]));
                entity.setPaidBreachAmt(new BigDecimal(lineArr[ID_13]));
                entity.setPreRepayFeeAmt(new BigDecimal(lineArr[ID_14]));
                entity.setFeeAmt(new BigDecimal(lineArr[ID_15]));
                entity.setOtherData(lineArr[ID_16]);
                entity.setBsnType(lineArr[ID_17]);
                entity.setChannel(BankChannel.HXBK.name());
                reccRepayList.add(entity);

            }
        } catch (Exception e) {
            logger.error("湖消直连解析还款明细文件失败", e);
            throw new RuntimeException("解析还款后计划文件失败", e);
        }
        return reccRepayList;
    }

    @Override
    protected void saveEntity(List<HXBKReccRepay> entityList) {
        hxbkReccRepayService.saveBatch(entityList, DEFAULT_BATCH_SIZE);
    }


    @Override
    protected void fillEntityData(List<HXBKReccRepay> entityList, CYBKReconcileFile reconcileFile) {
        entityList.forEach(e -> {
            e.setReccStatus(null);
            e.setReccId(reconcileFile.getId());
            e.setCreatedTime(LocalDateTime.now());
            e.setUpdatedTime(LocalDateTime.now());
        });
    }

    /**
     * 获取文件类型
     *
     * @return 文件类型
     */
    @Override
    public HXBKReccFileTypeEnum getReccType() {
        return HXBKReccFileTypeEnum.REPAYMENT_DETAIL_FILE;
    }
}
