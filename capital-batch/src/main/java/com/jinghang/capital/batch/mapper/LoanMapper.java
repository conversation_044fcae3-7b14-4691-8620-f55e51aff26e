package com.jinghang.capital.batch.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jinghang.capital.batch.entity.Loan;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 借款表; Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-14
 */
public interface LoanMapper extends BaseMapper<Loan> {

    /**
     * 中科金辽农 查询总账数据
     *
     * @param channel
     * @param startDate
     * @param endDate
     * @return
     */
    Map<String, BigDecimal> queryZkjLnFU300(String channel, LocalDate startDate, LocalDate endDate);

    /**
     * 对资--查询至截止日未结清的借据（包含截止日当天结清的借据）
     *
     * @param channel
     * @param endDate
     * @return
     */
    List<Loan> findBankNotSettleLoan(String channel, LocalDate endDate);

    /**
     * 对客--查询至截止日未结清的借据（包含截止日当天结清的借据）
     *
     * @param channel
     * @param endDate
     * @return
     */
    List<Loan> findCustomerNotSettleLoan(String channel, LocalDate endDate);

}
