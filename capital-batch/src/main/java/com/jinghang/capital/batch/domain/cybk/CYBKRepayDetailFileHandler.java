package com.jinghang.capital.batch.domain.cybk;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.batch.entity.CYBKReccRepayDetail;
import com.jinghang.capital.batch.entity.CYBKReconcileFile;
import com.jinghang.capital.batch.entity.Loan;
import com.jinghang.capital.batch.job.ReccUploadAntJobParamEntity;
import com.jinghang.capital.batch.service.FileService;
import com.jinghang.capital.batch.service.cybk.CYBKReccRepayDetailService;
import com.jinghang.capital.batch.service.tc.impl.CreditServiceImpl;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 还款期次明细文件
 */
@Component
public class CYBKRepayDetailFileHandler extends AbstractCYBKReconFileHandler<CYBKReccRepayDetail> {

    private static final Logger logger = LoggerFactory.getLogger(CYBKRepayDetailFileHandler.class);


    private static final int ID_0 = 0;
    private static final int ID_1 = 1;
    private static final int ID_2 = 2;
    private static final int ID_3 = 3;
    private static final int ID_4 = 4;
    private static final int ID_5 = 5;
    private static final int ID_6 = 6;
    private static final int ID_7 = 7;
    private static final int ID_8 = 8;
    private static final int ID_9 = 9;
    private static final int ID_10 = 10;
    private static final int ID_11 = 11;
    private static final int ID_12 = 12;
    private static final int ID_13 = 13;
    private static final int ID_14 = 14;
    private static final int ID_15 = 15;
    private static final int ID_16 = 16;
    private static final int ID_17 = 17;
    private static final int ID_18 = 18;
    private static final int ID_19 = 19;
    private static final int ID_20 = 20;
    private static final int ID_21 = 21;


    //   /upload/cyxf/{产品编码}/in/files/{YYYYMMDD}/repayment_plan_${yyyyMMdd}.csv
    // 长银还款后还款计划
    private static final String RECC_FILE_NAME = "%s/repayment_plan_%s.csv";


    private static final Integer LINE_LENGTH = 22;

    @Autowired
    private FileService fileService;

    @Autowired
    private CYBKReccRepayDetailService reccRepayDetailService;

    @Autowired
    private CreditServiceImpl creditService;


    @Override
    protected String ossFilePath(LocalDate data) {
        return "cybk/recc/repayPlan/" + getFileName(data);
    }

    @Override
    protected String sftpFilePath(LocalDate data) {
        return getSftpPathPrefix() + "/" + getFileName(data);
    }


    private String getFileName(LocalDate data) {
        String dateStr1 = data.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return RECC_FILE_NAME.formatted(dateStr1, dateStr1);
    }

    @Override
    protected List<CYBKReccRepayDetail> getReccFileDetails(InputStream inputStream, StringBuffer sftpFileStr) {
        List<CYBKReccRepayDetail> reccRepayList = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
            String line;
            int lineNum = 0;
            while ((line = reader.readLine()) != null) {
                line = line.trim();

                if (line.isEmpty()) {
                    continue;
                }
                lineNum++;
                if (lineNum == 1) {
                    continue;
                }
                appendStr(sftpFileStr, line);
                String[] lineArr = line.split(SEPARATOR, LINE_LENGTH);
                if (lineArr.length < LINE_LENGTH) {
                    throw new RuntimeException("长银消金直连, 解析还款数据异常,line:" + (reccRepayList.size() + 1) + "__" + line);
                }

                String loanNo = lineArr[ID_0];

                if (!loanNo.startsWith("LO")) {
                    logger.info("reccRepaymentPlan loanId: [{}] 非本平台固有借据号格式 忽略", loanNo);
                    continue;
                }
                Loan loan = getLoanService().findByIdAndChannel(loanNo, BankChannel.CYBK.name());
                if (Objects.isNull(loan)) {
                    logger.info("reccRepaymentPlan loanId: [{}] 未查到数据库匹配信息 忽略", loanNo);
                    continue;
                }

                CYBKReccRepayDetail entity = new CYBKReccRepayDetail();
                entity.setCybkLoanNo(lineArr[ID_1]);
                entity.setPeriod(Integer.parseInt(lineArr[ID_2]));
                entity.setDueDate(LocalDate.parse(lineArr[ID_3], DateTimeFormatter.ISO_LOCAL_DATE));
                entity.setRepayAmount(new BigDecimal(lineArr[ID_4]));
                entity.setRepayPrincipalAmt(new BigDecimal(lineArr[ID_5]));
                entity.setRepayInterestAmt(new BigDecimal(lineArr[ID_6]));
                entity.setRepayPenaltyAmt(new BigDecimal(lineArr[ID_7]));
                entity.setRepayReinterestAmt(new BigDecimal(lineArr[ID_8]));
                entity.setRepayFeeAmt(new BigDecimal(lineArr[ID_9]));
                entity.setRepayGuaranteeAmt(new BigDecimal(lineArr[ID_10]));
                entity.setRepayGuaranteeOverdueAmt(new BigDecimal(lineArr[ID_11]));
                entity.setActRepayAmount(new BigDecimal(lineArr[ID_12]));
                entity.setActPrincipalAmt(new BigDecimal(lineArr[ID_13]));
                entity.setActInterestAmt(new BigDecimal(lineArr[ID_14]));
                entity.setActPenaltyAmt(new BigDecimal(lineArr[ID_15]));
                entity.setActReinterestAmt(new BigDecimal(lineArr[ID_16]));
                entity.setActFeeAmt(new BigDecimal(lineArr[ID_17]));
                entity.setActGuaranteeFeeAmt(new BigDecimal(lineArr[ID_18]));
                entity.setActGuaranteeOverdueAmt(new BigDecimal(lineArr[ID_19]));
                entity.setIsSettle(lineArr[ID_20]);
                entity.setSettleDate(LocalDate.parse(lineArr[ID_21], DateTimeFormatter.ISO_LOCAL_DATE));
                if (loan != null) {
                    entity.setCreditId(loan.getCreditId());
                    entity.setSysId(loan.getId());
                }

                reccRepayList.add(entity);
            }
        } catch (Exception e) {
            logger.error("CYBK repaymentPlan file recc error.", e);
            throw new RuntimeException("解析还款后计划文件失败");
        }
        return reccRepayList;
    }

    @Override
    protected void saveEntity(List<CYBKReccRepayDetail> entityList) {
        reccRepayDetailService.saveBatch(entityList, DEFAULT_BATCH_SIZE);
    }

    @Override
    protected void fillEntityData(List<CYBKReccRepayDetail> entityList, CYBKReconcileFile reconcileFile) {
        entityList.forEach(e -> {
            e.setReccStatus(null);
            e.setReccId(reconcileFile.getId());
            e.setCreatedTime(LocalDateTime.now());
            e.setUpdatedTime(LocalDateTime.now());
        });
    }

    @Override
    public CYBKReccFileTypeEnum getReccType() {
        return CYBKReccFileTypeEnum.REPAYMENT_DETAIL_FILE;
    }

    @Override
    public void pushToAnt(ReccUploadAntJobParamEntity jobEntity) {

    }
}
