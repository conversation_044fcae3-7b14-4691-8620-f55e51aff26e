package com.jinghang.capital.batch.job.cybk.cust;


import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.recc.ReccApplyDto;
import com.jinghang.capital.api.dto.recc.ReccResultDto;
import com.jinghang.capital.api.dto.recc.ReccType;
import com.jinghang.capital.batch.job.cybk.recc.CYBKReccParam;
import com.jinghang.capital.batch.remote.FinReccService;
import com.jinghang.common.util.JsonUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import java.time.LocalDate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 长银消金直连用户真实还款D+1 上送 D日发生客户真实还款的数据
 * （2024-02-05 润楼把 蓝海和长银放在同一个文件中，长银的暂时不要上传）
 * 格式示例：
 * <p>
 * 放款编号|贷款总期数|当前期次|贷款本金|前置费用|应还日期|实还日期|应还本金|应还利息|应还罚息|应还费用|应还融担费|应还总金额|实还本金|实还利息|实还罚息|实还费用|实还融担费|实还总金额|剩余应还本金|剩余应还利息|剩余应还罚息
 * |剩余应还费用|剩余应还融担费|剩余应还总金额|减免本金|减免利息|减免罚息|减免费用|减免融担费|减免总金额|当前逾期天数|历史最大逾期天数|累计逾期次数|首次逾期日期|还款类型|资产池编号
 * LA1HLS7CD34S074|3|1|900.00|0.00|20230618|20230619|300.00|10.00|0.00|0.00|15.40|325.40
 * |300.00|10.00|0.00|0.00|15.40|325.4.00|0.00|0.00|0.00|0.00|0.00|0.00|0.00|0.00|0.00|0.00|0.00|0.00|1|1|1|20230619|4|AD0LHK61EDU
 */

@Component
@JobHandler("cybkCustRepayReccJob")
public class CYBKCustRepayReccJob extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(CYBKCustRepayReccJob.class);

    @Autowired
    private FinReccService reccService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        // 交易发生日是job的D-1日
        // {"reccDay": "2023-05-01"}, default is  yesterday
        LocalDate reccDay = LocalDate.now().minusDays(1L);
        ReccApplyDto reccApplyDto = new ReccApplyDto();
        reccApplyDto.setChannel(BankChannel.CYBK);

        try {
            CYBKReccParam reccParam = JsonUtil.convertToObject(param, CYBKReccParam.class);
            LocalDate paramDay = reccParam.getReccDay();
            if (null != paramDay) {
                reccDay = paramDay;
            }
        } catch (Exception ex) {
            // no throw
        }

        reccApplyDto.setReccType(ReccType.CUST_REPAY);
        reccApplyDto.setReccDay(reccDay);
        logger.info("长银消金直连还款文件上传:{}", JsonUtil.toJsonString(reccApplyDto));
        return doProcessRecc(reccApplyDto);
    }

    public ReturnT<String> doProcessRecc(ReccApplyDto applyDto) throws Exception {

        RestResult<ReccResultDto> result = reccService.process(applyDto);
        logger.info("cybk cust repay process result. status: {}, content: {}", result.getCode(), result.getData());
        return ReturnT.SUCCESS;
    }
}
