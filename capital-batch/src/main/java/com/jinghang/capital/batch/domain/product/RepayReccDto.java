package com.jinghang.capital.batch.domain.product;


import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

public class RepayReccDto {
    private static final String SPLIT = "|";

    private String repayId;
    private String outerRepayId;
    private LocalDate repayDate;
    private BigDecimal amount;
    private Integer period;
    private BigDecimal principal;
    private BigDecimal interest;
    private BigDecimal penalty;
    private BigDecimal guarantee;
    private BigDecimal breach;

    /**
     * 还款优惠劵金额(360活动减免金额-对客咨询费)
     */
    private BigDecimal couponAmt = BigDecimal.ZERO;

    public BigDecimal getCouponAmt() {
        return couponAmt;
    }

    public void setCouponAmt(BigDecimal couponAmt) {
        this.couponAmt = couponAmt;
    }

    public void setRepayId(String repayId) {
        this.repayId = repayId;
    }

    public void setOuterRepayId(String outerRepayId) {
        this.outerRepayId = outerRepayId;
    }

    public void setRepayDate(LocalDate repayDate) {
        this.repayDate = repayDate;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public void setPrincipal(BigDecimal principal) {
        this.principal = principal;
    }

    public void setInterest(BigDecimal interest) {
        this.interest = interest;
    }

    public void setPenalty(BigDecimal penalty) {
        this.penalty = penalty;
    }

    public void setGuarantee(BigDecimal guarantee) {
        this.guarantee = guarantee;
    }

    public void setBreach(BigDecimal breach) {
        this.breach = breach;
    }

    public String getFormattedTxt() {
        return repayId
                + SPLIT + outerRepayId
                + SPLIT + repayDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"))
                + SPLIT + amount.setScale(2, RoundingMode.HALF_UP).toPlainString()
                + SPLIT + period
                + SPLIT + principal.toPlainString()
                + SPLIT + interest.toPlainString()
                + SPLIT + penalty.toPlainString()
                + SPLIT + guarantee.toPlainString()
                + SPLIT + breach.toPlainString()
                + SPLIT + couponAmt.toPlainString();
    }
}
