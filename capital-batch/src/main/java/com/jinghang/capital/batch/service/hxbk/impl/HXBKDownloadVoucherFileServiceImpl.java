package com.jinghang.capital.batch.service.hxbk.impl;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.FileType;
import com.jinghang.capital.api.dto.file.FileDailyProcessDto;
import com.jinghang.capital.batch.remote.FinVoucherFileService;
import com.jinghang.capital.batch.service.hxbk.HXBKDownloadVoucherFileService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * HXBK证明、凭证文件下载服务实现
 */
@Component
public class HXBKDownloadVoucherFileServiceImpl implements HXBKDownloadVoucherFileService {

    private static final Logger logger = LoggerFactory.getLogger(HXBKDownloadVoucherFileServiceImpl.class);

    @Autowired
    private FinVoucherFileService voucherFileService;

    @Override
    public void processHXBKDownloadVoucher() {
        logger.info("开始处理HXBK结清证明查询和下载");

        FileDailyProcessDto dto = new FileDailyProcessDto();
        dto.setBankChannel(BankChannel.HXBK);
        dto.setType(FileType.CREDIT_SETTLE_VOUCHER_FILE);

        try {
            voucherFileService.fileApplyQuery(dto);
            logger.info("HXBK结清证明查询和下载处理完成");
        } catch (Exception e) {
            logger.error("HXBK结清证明查询和下载处理失败", e);
            throw e;
        }
    }
}
