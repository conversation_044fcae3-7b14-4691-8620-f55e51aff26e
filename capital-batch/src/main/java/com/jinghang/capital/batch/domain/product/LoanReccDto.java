package com.jinghang.capital.batch.domain.product;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

public class LoanReccDto {
    private static final String SPLIT = "|";

    private String loanId;

    private String outLoanId;

    /**
     * 放款金额
     */
    private BigDecimal amount;

    /**
     * 期限
     */
    private Integer period;

    /**
     * 放款时间
     */
    private LocalDate loanTime;

    /**
     * 放款状态
     */
    private String status;

    /**
     * 资方
     */
    private String channel;

    /**
     * 备注
     */
    private String remark;

    /**
     * 连接模式
     */
    private String linkMode;

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public String getOutLoanId() {
        return outLoanId;
    }

    public void setOutLoanId(String outLoanId) {
        this.outLoanId = outLoanId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public LocalDate getLoanTime() {
        return loanTime;
    }

    public void setLoanTime(LocalDate loanTime) {
        this.loanTime = loanTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getLinkMode() {
        return linkMode;
    }

    public void setLinkMode(String linkMode) {
        this.linkMode = linkMode;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getFormattedTxt() {
        // 7800.00|YLQINJQB20230329083612339068|12|1|2023/03/29|放款|F003|资金路由
        return loanId
                + SPLIT + outLoanId
                + SPLIT + loanTime.format(DateTimeFormatter.ofPattern("yyyyMMdd"))
                + SPLIT + amount.setScale(2, RoundingMode.HALF_UP).toPlainString()
                + SPLIT + period;
    }
}
