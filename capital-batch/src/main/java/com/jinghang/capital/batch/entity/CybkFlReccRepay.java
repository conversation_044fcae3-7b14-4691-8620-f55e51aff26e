package com.jinghang.capital.batch.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 长银分润还款对账文件;
 *
 * <AUTHOR> http://www.chiner.pro
 * @date : 2025-1-9
 */
@TableName("cybk_fl_recc_repay")
public class CybkFlReccRepay extends BaseEntity implements Serializable {

    /**
     * recc_id
     */
    private String reccId;
    /**
     * 对账状态
     */
    private String reccStatus;
    /**
     * 借据
     */
    private String sysId;
    /**
     * 长银借据号
     */
    private String loanNo;
    /**
     * 还款申请流水号
     */
    private String repaymentSeq;
    /**
     * 长银记账流水号
     */
    private String setlSeq;
    /**
     * 对方业务号
     */
    private String outApplSeq;
    /**
     * 客户编号
     */
    private String custId;
    /**
     * 还款模式
     */
    private String payMode;
    /**
     * 实际还款日期
     */
    private LocalDate setlDt;
    /**
     * 实际还款时间
     */
    private LocalDateTime setlDtTime;
    /**
     * 是否长银扣款
     */
    private String isCy;
    /**
     * 还款金额
     */
    private BigDecimal totalAmt;
    /**
     * 归还本金
     */
    private BigDecimal prcpAmt;
    /**
     * 归还利息
     */
    private BigDecimal intAmt;
    /**
     * 归还罚息
     */
    private BigDecimal odIntAmt;
    /**
     * 归还复利
     */
    private BigDecimal commOdIntAmt;
    /**
     * 归还费用
     */
    private BigDecimal feeAmt;
    /**
     * 还款后贷款余额
     */
    private BigDecimal balanceAmt;
    /**
     * 是否代偿
     */
    private String isDc;
    /**
     * 归还担保费
     */
    private BigDecimal guaranteeFeeAmt;
    /**
     * 归还担保费逾期费用
     */
    private BigDecimal guaranteeFeeOdAmt;
    /**
     * 商户订单号
     */
    private String platformFlowNo;

    public String getReccId() {
        return reccId;
    }

    public void setReccId(String reccId) {
        this.reccId = reccId;
    }

    public String getReccStatus() {
        return reccStatus;
    }

    public void setReccStatus(String reccStatus) {
        this.reccStatus = reccStatus;
    }

    public String getSysId() {
        return sysId;
    }

    public void setSysId(String sysId) {
        this.sysId = sysId;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public String getRepaymentSeq() {
        return repaymentSeq;
    }

    public void setRepaymentSeq(String repaymentSeq) {
        this.repaymentSeq = repaymentSeq;
    }

    public String getSetlSeq() {
        return setlSeq;
    }

    public void setSetlSeq(String setlSeq) {
        this.setlSeq = setlSeq;
    }

    public String getOutApplSeq() {
        return outApplSeq;
    }

    public void setOutApplSeq(String outApplSeq) {
        this.outApplSeq = outApplSeq;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getPayMode() {
        return payMode;
    }

    public void setPayMode(String payMode) {
        this.payMode = payMode;
    }

    public LocalDate getSetlDt() {
        return setlDt;
    }

    public void setSetlDt(LocalDate setlDt) {
        this.setlDt = setlDt;
    }

    public LocalDateTime getSetlDtTime() {
        return setlDtTime;
    }

    public void setSetlDtTime(LocalDateTime setlDtTime) {
        this.setlDtTime = setlDtTime;
    }

    public String getIsCy() {
        return isCy;
    }

    public void setIsCy(String isCy) {
        this.isCy = isCy;
    }

    public BigDecimal getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(BigDecimal totalAmt) {
        this.totalAmt = totalAmt;
    }

    public BigDecimal getPrcpAmt() {
        return prcpAmt;
    }

    public void setPrcpAmt(BigDecimal prcpAmt) {
        this.prcpAmt = prcpAmt;
    }

    public BigDecimal getIntAmt() {
        return intAmt;
    }

    public void setIntAmt(BigDecimal intAmt) {
        this.intAmt = intAmt;
    }

    public BigDecimal getOdIntAmt() {
        return odIntAmt;
    }

    public void setOdIntAmt(BigDecimal odIntAmt) {
        this.odIntAmt = odIntAmt;
    }

    public BigDecimal getCommOdIntAmt() {
        return commOdIntAmt;
    }

    public void setCommOdIntAmt(BigDecimal commOdIntAmt) {
        this.commOdIntAmt = commOdIntAmt;
    }

    public BigDecimal getFeeAmt() {
        return feeAmt;
    }

    public void setFeeAmt(BigDecimal feeAmt) {
        this.feeAmt = feeAmt;
    }

    public BigDecimal getBalanceAmt() {
        return balanceAmt;
    }

    public void setBalanceAmt(BigDecimal balanceAmt) {
        this.balanceAmt = balanceAmt;
    }

    public String getIsDc() {
        return isDc;
    }

    public void setIsDc(String isDc) {
        this.isDc = isDc;
    }

    public BigDecimal getGuaranteeFeeAmt() {
        return guaranteeFeeAmt;
    }

    public void setGuaranteeFeeAmt(BigDecimal guaranteeFeeAmt) {
        this.guaranteeFeeAmt = guaranteeFeeAmt;
    }

    public BigDecimal getGuaranteeFeeOdAmt() {
        return guaranteeFeeOdAmt;
    }

    public void setGuaranteeFeeOdAmt(BigDecimal guaranteeFeeOdAmt) {
        this.guaranteeFeeOdAmt = guaranteeFeeOdAmt;
    }

    public String getPlatformFlowNo() {
        return platformFlowNo;
    }

    public void setPlatformFlowNo(String platformFlowNo) {
        this.platformFlowNo = platformFlowNo;
    }
}
