package com.jinghang.capital.batch.domain.product;


import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 代偿结果明细
 */
public class ClaimReccDto {
    private static final String SPLIT = "|";

    /**
     * core放款ID
     */
    private String loanId;
    /**
     * 业务系统放款ID
     */
    private String outLoanId;
    /**
     * 期数
     */
    private Integer period;
    /**
     * 应还时间
     */
    private LocalDate repayTime;
    /**
     * 实还时间
     */
    private LocalDateTime actRepayTime;
    /**
     * 本金
     */
    private BigDecimal principal;
    /**
     * 利息
     */
    private BigDecimal interest;
    /**
     * 罚息
     */
    private BigDecimal penalty;
    /**
     * 违约金
     */
    private BigDecimal breach;

    /**
     * 还款目的
     */
    private String repayPurpose;

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public String getOutLoanId() {
        return outLoanId;
    }

    public void setOutLoanId(String outLoanId) {
        this.outLoanId = outLoanId;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public LocalDate getRepayTime() {
        return repayTime;
    }

    public void setRepayTime(LocalDate repayTime) {
        this.repayTime = repayTime;
    }

    public LocalDateTime getActRepayTime() {
        return actRepayTime;
    }

    public void setActRepayTime(LocalDateTime actRepayTime) {
        this.actRepayTime = actRepayTime;
    }

    public BigDecimal getPrincipal() {
        return principal;
    }

    public void setPrincipal(BigDecimal principal) {
        this.principal = principal;
    }

    public BigDecimal getInterest() {
        return interest;
    }

    public void setInterest(BigDecimal interest) {
        this.interest = interest;
    }

    public BigDecimal getPenalty() {
        return penalty;
    }

    public void setPenalty(BigDecimal penalty) {
        this.penalty = penalty;
    }

    public BigDecimal getBreach() {
        return breach;
    }

    public void setBreach(BigDecimal breach) {
        this.breach = breach;
    }

    public String getRepayPurpose() {
        return repayPurpose;
    }

    public void setRepayPurpose(String repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public String getFormattedTxt() {
        // core放款ID|业务系统放款ID|期数|应还时间|实还时间|本金|利息|罚息|违约金|还款目的
        return loanId
                + SPLIT + outLoanId
                + SPLIT + period
                + SPLIT + repayTime.format(DateTimeFormatter.ofPattern("yyyyMMdd"))
                + SPLIT + actRepayTime.format(DateTimeFormatter.ofPattern("yyyyMMdd HH:mm:ss"))
                + SPLIT + principal
                + SPLIT + interest
                + SPLIT + penalty
                + SPLIT + breach
                + SPLIT + repayPurpose;
    }
}
