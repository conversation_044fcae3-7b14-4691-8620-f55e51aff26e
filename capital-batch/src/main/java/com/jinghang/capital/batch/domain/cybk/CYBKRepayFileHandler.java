package com.jinghang.capital.batch.domain.cybk;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.SftpException;
import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.FileMode;
import com.jinghang.capital.api.dto.FileType;
import com.jinghang.capital.api.dto.Product;
import com.jinghang.capital.batch.domain.product.RepayReccDto;
import com.jinghang.capital.batch.entity.CYBKReccRepay;
import com.jinghang.capital.batch.entity.CYBKReconcileFile;
import com.jinghang.capital.batch.entity.CustomerRepayRecord;
import com.jinghang.capital.batch.entity.ReconciliationFile;
import com.jinghang.capital.batch.enums.ReccStateEnum;
import com.jinghang.capital.batch.job.ReccUploadAntJobParamEntity;
import com.jinghang.capital.batch.service.FileService;
import com.jinghang.capital.batch.service.WarningService;
import com.jinghang.capital.batch.service.cybk.CYBKReccRepayService;
import com.jinghang.capital.batch.service.tc.ICustomerRepayRecordService;
import com.jinghang.capital.batch.service.tc.ILoanService;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 还款文件
 */
@Component
public class CYBKRepayFileHandler extends AbstractCYBKReconFileHandler<CYBKReccRepay> {

    private static final Logger logger = LoggerFactory.getLogger(CYBKRepayFileHandler.class);


    private static final int BANK_LOAN_NO_IDX = 0;
    private static final int BANK_REPAY_NO_IDX = 1;
    private static final int BANK_SETL_SEQ_IDX = 2;
    private static final int OUT_LOAN_SEQ_IDX = 3;
    private static final int BANK_CUST_ID_IDX = 4;
    private static final int REPAY_MODE_IDX = 5;
    private static final int REPAY_DATE_IDX = 6;
    private static final int IS_BANK_CHARGE_IDX = 8;
    private static final int TOTAL_AMOUNT_IDX = 9;
    private static final int PRINCIPAL_AMT_IDX = 10;
    private static final int INTEREST_AMT_IDX = 11;
    private static final int PENALTY_AMT_IDX = 12;
    private static final int RE_INTEREST_MAT_IDX = 13;
    private static final int FEE_IDX = 14;
    private static final int IS_CLAIM_IDX = 16;
    private static final int GUARANTEE_FEE_IDX = 17;
    // 这个是商户订单号
    private static final int MERCHANT_NO_IDX = 19;

    //    /upload/cyxf/{产品编码}/in/files/{YYYYMMDD}/repay_${yyyyMMdd}.csv
    // 长银还款明细文件
    private static final String RECC_FILE_NAME = "%s/repay_%s.csv";


    private static final Integer LINE_LENGTH = 21;

    @Autowired
    private FileService fileService;

    @Autowired
    private CYBKReccRepayService reccRepayService;

    @Autowired
    private ICustomerRepayRecordService customerRepayRecordService;

    @Autowired
    private CYBKConfig config;
    @Autowired
    private ILoanService loanService;
    @Autowired
    private WarningService warningService;


    @Override
    protected String ossFilePath(LocalDate data) {
        return "cybk/recc/repay/" + getFileName(data);
    }

    @Override
    protected String sftpFilePath(LocalDate data) {
        return getSftpPathPrefix() + "/" + getFileName(data);
    }


    private String getFileName(LocalDate data) {
        String dateStr1 = data.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return RECC_FILE_NAME.formatted(dateStr1, dateStr1);
    }

    @Override
    protected List<CYBKReccRepay> getReccFileDetails(InputStream inputStream, StringBuffer sftpFileStr) {
        List<CYBKReccRepay> reccRepayList = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
            String line;
            int lineNum = 0;
            while ((line = reader.readLine()) != null) {
                line = line.trim();

                if (line.isEmpty()) {
                    continue;
                }
                lineNum++;
                if (lineNum == 1) {
                    continue;
                }
                appendStr(sftpFileStr, line);
                String[] lineArr = line.split(SEPARATOR, LINE_LENGTH);
                if (lineArr.length < LINE_LENGTH) {
                    throw new RuntimeException("长银消金直连, 解析还款数据异常,line:" + (reccRepayList.size() + 1) + "__" + line);
                }

                // 资方还款流水号
                String bankSerial = lineArr[BANK_SETL_SEQ_IDX];

                //保存长银消金直连还款数据
                CYBKReccRepay entity = new CYBKReccRepay();
                entity.setCybkLoanNo(lineArr[BANK_LOAN_NO_IDX]);
                entity.setTransNo(lineArr[BANK_REPAY_NO_IDX]);
                entity.setBankRepayNo(bankSerial);
                entity.setSysId(lineArr[OUT_LOAN_SEQ_IDX]);
                entity.setOutLoanReq(lineArr[OUT_LOAN_SEQ_IDX]);
                entity.setCustId(lineArr[BANK_CUST_ID_IDX]);
                entity.setRepayMode(lineArr[REPAY_MODE_IDX]);
                entity.setRepayDay(LocalDate.parse(lineArr[REPAY_DATE_IDX], DateTimeFormatter.ISO_LOCAL_DATE));
                entity.setIsCyCharge(lineArr[IS_BANK_CHARGE_IDX]);
                entity.setActRepayAmount(new BigDecimal(lineArr[TOTAL_AMOUNT_IDX]));
                entity.setActPrincipalAmt(new BigDecimal(lineArr[PRINCIPAL_AMT_IDX]));
                entity.setActInterestAmt(new BigDecimal(lineArr[INTEREST_AMT_IDX]));
                entity.setActPenaltyAmt(new BigDecimal(lineArr[PENALTY_AMT_IDX]));
                entity.setActReinterestAmt(new BigDecimal(lineArr[RE_INTEREST_MAT_IDX]));
                entity.setActFeeAmt(new BigDecimal(lineArr[FEE_IDX]));
                entity.setIsClaim(lineArr[IS_CLAIM_IDX]);
                entity.setActGuaranteeFeeAmt(new BigDecimal(lineArr[GUARANTEE_FEE_IDX]));
                entity.setPayMerchantNo(lineArr[MERCHANT_NO_IDX]);
                //logger.info("repay数据表 paymerchantNo: [{}] length: [{}]", lineArr[MERCHANT_NO_IDX], lineArr[MERCHANT_NO_IDX].length());
                reccRepayList.add(entity);
            }
        } catch (Exception e) {
            logger.error("CYBK repay file recc error.", e);
            throw new RuntimeException("解析还款文件失败");
        }
        return reccRepayList;
    }


    @Override
    protected void processTszRecFile(LocalDate fileDate, List<CYBKReccRepay> entityList) {

        // 扣款成功的记录
        List<CustomerRepayRecord> records = customerRepayRecordService.list(new QueryWrapper<CustomerRepayRecord>()
                .eq("channel", BankChannel.CYBK.name())
                .ge("repay_time", fileDate).lt("repay_time", fileDate.plusDays(1)).eq("repay_status", "SUCCESS"));

        List<RepayReccDto> result = new ArrayList<>();
        records.stream().forEach(r -> {
            RepayReccDto tszRecc = new RepayReccDto();
            tszRecc.setRepayId(r.getId());
            tszRecc.setOuterRepayId(r.getOuterRepayId());
            tszRecc.setRepayDate(r.getRepayTime().toLocalDate());
            tszRecc.setAmount(r.getTotalAmt());
            tszRecc.setPeriod(Objects.isNull(r.getPeriod()) ? 0 : r.getPeriod());
            tszRecc.setPrincipal(r.getPrincipalAmt());
            tszRecc.setInterest(r.getInterestAmt());
            tszRecc.setPenalty(r.getPenaltyAmt());
            tszRecc.setGuarantee(Objects.isNull(r.getGuaranteeAmt()) ? BigDecimal.ZERO : r.getGuaranteeAmt());
            tszRecc.setBreach(Objects.isNull(r.getBreachAmt()) ? BigDecimal.ZERO : r.getBreachAmt());
            result.add(tszRecc);
        });

        try {
            Path tempFile = Files.createTempFile("tsz_loan_repay", "txt");
            BufferedWriter writer = Files.newBufferedWriter(tempFile);
            for (RepayReccDto dto : result) {
                writer.write(dto.getFormattedTxt());
                writer.newLine();
            }
            writer.close();

            String targetFileName = fileDate.format(DateTimeFormatter.BASIC_ISO_DATE) + "_repayCheck.txt";
            String ossPath = "tsz/recc/CYBK/repay/" + targetFileName;
            fileService.switchOss(ossBucketName()).putObject(ossBucketName(), ossPath, Files.newInputStream(tempFile));

            ReconciliationFile tszRecFile = new ReconciliationFile();
            tszRecFile.setFileType(FileType.REPAYMENT_FILE);
            tszRecFile.setFileName(targetFileName);
            tszRecFile.setFileDate(fileDate);
            tszRecFile.setProduct(Product.ZC_CASH);
            tszRecFile.setBankChannel(BankChannel.CYBK);
            tszRecFile.setMode(FileMode.OSS);
            tszRecFile.setReconciliationState(ReccStateEnum.P);
            tszRecFile.setTargetOssBucket(ossBucketName());
            tszRecFile.setTargetOssKey(ossPath);
            //
            getReconciliationFileService().save(tszRecFile);

            Files.delete(tempFile);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    protected void saveEntity(List<CYBKReccRepay> entityList) {
        reccRepayService.saveBatch(entityList, DEFAULT_BATCH_SIZE);
    }

    @Override
    protected void fillEntityData(List<CYBKReccRepay> entityList, CYBKReconcileFile reconcileFile) {
        entityList.forEach(e -> {
            e.setReccStatus(null);
            e.setReccId(reconcileFile.getId());
            e.setCreatedTime(LocalDateTime.now());
            e.setUpdatedTime(LocalDateTime.now());
        });
    }

    @Override
    public CYBKReccFileTypeEnum getReccType() {
        return CYBKReccFileTypeEnum.REPAYMENT_FILE;
    }


    @Override
    public void pushToAnt(ReccUploadAntJobParamEntity jobEntity) {
        LocalDate fileDate = LocalDate.parse(jobEntity.getFileDate());
        boolean needCover = jobEntity.isNeedCover();
        String sftpFilePath = sftpFilePath(fileDate); // 原始csv文件路径
        String sftpOkFilePath = sftpFilePath + ".ok"; // .ok文件路径

        logger.info("[长银对账文件上传蚂蚁job]开始处理长银消金直连对账文件: [{}] 和 [{}]", sftpFilePath, sftpOkFilePath);

        try {
            super.getSftp().custom(channelSftp -> {
                // 1. 处理主csv文件
                processAndUploadFile(channelSftp, sftpFilePath, fileDate, false, needCover);

                // 2. 处理.ok文件（直接上传，不修改内容）
                try {
                    processAndUploadFile(channelSftp, sftpOkFilePath, fileDate, false, needCover);
                } catch (Exception e) {
                    logger.warn("[长银对账文件上传蚂蚁job].ok文件处理失败，可能文件不存在，继续执行", e);
                }
            });
        } catch (Exception e) {
            warningService.warn("[长银对账文件上传蚂蚁job]长银消金直连对账文件处理失败|%s".formatted(e.getMessage()),
                    msg -> logger.error(msg, e));
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    /**
     * 通用文件处理方法
     *
     * @param channelSftp    SFTP通道
     * @param sourcePath     源文件路径
     * @param fileDate       文件日期
     * @param needProcessing 是否需要处理文件内容
     */
    private void processAndUploadFile(ChannelSftp channelSftp, String sourcePath, LocalDate fileDate,
                                      boolean needProcessing, boolean needCover) {
        try (InputStream inputStream = channelSftp.get(sourcePath)) {
            byte[] fileContent = inputStream.readAllBytes();

            // 构建目标路径
            String targetFileName = sourcePath.substring(sourcePath.lastIndexOf('/') + 1);
            String remotePath = config.getAntUploadDir() + "repay/"
                    + fileDate.format(DateTimeFormatter.BASIC_ISO_DATE) + "/" + targetFileName;

            super.getAntSftp().custom(antChannelSftp -> {
                // 创建目录逻辑（保持不变）
                String dirPath = remotePath.substring(0, remotePath.lastIndexOf('/'));
                String remotePathAbs = "";
                try {
                    String currentDir = antChannelSftp.pwd();
                    dirPath = currentDir + dirPath;
                    remotePathAbs = currentDir + remotePath;

                    // 检查文件是否存在
                    if (!needCover) {
                        try {
                            antChannelSftp.stat(remotePathAbs);
                            logger.warn("[长银对账文件上传蚂蚁job]文件已存在且不覆盖: {}", remotePathAbs);
                            return; // 文件存在且不覆盖，直接返回
                        } catch (SftpException e) {
                            if (e.id != ChannelSftp.SSH_FX_NO_SUCH_FILE) {
                                throw e; // 非"文件不存在"异常则抛出
                            }
                        }
                    }

                    mkdirs(antChannelSftp, dirPath);
                } catch (SftpException e) {
                    e.printStackTrace();
                }

                // 上传文件
                try (InputStream antInputStream = new ByteArrayInputStream(fileContent)) {
                    antChannelSftp.put(antInputStream, remotePathAbs);
                    logger.info("[长银repay文件上传蚂蚁job]成功上传文件到蚂蚁 SFTP: {}", remotePathAbs);
                } catch (IOException e) {
                    e.printStackTrace();
                    warningService.warn("[长银repay文件上传蚂蚁job]异常，上传文件到蚂蚁 SFTP。" + e.getMessage());
                    logger.error("[长银repay文件上传蚂蚁job]IO异常，上传文件到蚂蚁 SFTP: {}", remotePathAbs);
                } catch (SftpException e) {
                    e.printStackTrace();
                    warningService.warn("[长银repay文件上传蚂蚁job]异常，上传文件到蚂蚁 SFTP。" + e.getMessage());
                    logger.error("[长银repay文件上传蚂蚁job]SftpException异常，上传文件到蚂蚁 SFTP: {}", remotePathAbs);
                }
            });
        } catch (SftpException e) {
            if (e.id == ChannelSftp.SSH_FX_NO_SUCH_FILE) {
                throw new RuntimeException("[长银repay文件上传蚂蚁job]文件不存在: " + sourcePath, e);
            }
            throw new RuntimeException("[长银repay文件上传蚂蚁job]SFTP操作失败", e);
        } catch (IOException e) {
            throw new RuntimeException("[长银repay文件上传蚂蚁job]文件处理失败", e);
        } catch (com.jinghang.common.sftp.exception.SftpException e) {
            e.printStackTrace();
        }
    }

    /**
     * 创建目录（复用原有逻辑）
     */
    private void mkdirs(ChannelSftp channelSftp, String dirPath) throws SftpException {
        try {
            channelSftp.cd(dirPath);
        } catch (SftpException e) {
            if (e.id == ChannelSftp.SSH_FX_NO_SUCH_FILE) {
                String[] folders = dirPath.split("/");
                String currentPath = "";
                for (String folder : folders) {
                    if (folder.isEmpty()) {
                        continue;
                    }
                    currentPath += "/" + folder;
                    try {
                        channelSftp.cd(currentPath);
                    } catch (SftpException ex) {
                        channelSftp.mkdir(currentPath);
                        channelSftp.cd(currentPath);
                    }
                }
            }
        }
    }


}
