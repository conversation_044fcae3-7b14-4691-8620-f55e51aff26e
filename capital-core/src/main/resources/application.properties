server.compression.enabled=true
server.port=8001
server.tomcat.threads.max=500
server.tomcat.threads.min-spare=20
server.tomcat.max-connections=10000
server.tomcat.accept-count=600
server.shutdown=graceful

logging.file.name=/home/<USER>/logs/capital-core-service/capital-core-service.log
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} [%-5level][%X{traceId:-},%X{spanId:-}][%thread] %logger{50}.%method:%line - %msg%n
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} [%-5level][%X{traceId:-},%X{spanId:-}][%thread] %logger{50}.%method:%line - %msg%n
logging.logback.rollingpolicy.max-file-size=500MB
logging.logback.rollingpolicy.total-size-cap=1200MB
logging.level.com.netflix.discovery=warn

spring.application.name=capital-core-service
spring.config.import=apollo://
spring.jpa.open-in-view=false
spring.rabbitmq.listener.simple.acknowledge-mode=manual
spring.rabbitmq.listener.direct.acknowledge-mode=manual

spring.data.redis.repositories.enabled=false

eureka.instance.prefer-ip-address=true
eureka.instance.lease-expiration-duration-in-seconds=12
eureka.instance.lease-renewal-interval-in-seconds=4
eureka.client.registry-fetch-interval-seconds=5
eureka.client.register-with-eureka=true
eureka.client.fetch-registry=true

management.endpoints.web.exposure.include=health,serviceregistry,shutdown


#logging.level.org.springframework.data.jpa=trace
#logging.level.org.springframework.jdbc=debug
#logging.level.org.hibernate.SQL=debug
#logging.level.org.hibernate.orm.jdbc.bind=trace
#logging.level.org.hibernate.orm.query=trace
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.tags-sorter=alpha
springdoc.swagger-ui.operations-sorter=alpha
springdoc.api-docs.path=/v3/api-docs
springdoc.group-configs[0].group=fin-core-service
springdoc.group-configs[0].paths-to-match=/**
springdoc.group-configs[0].packages-to-scan=com.jinghang.capital.core.controller
knife4j.setting.language=zh_cn
knife4j.enable=false
spring.lifecycle.timeout-per-shutdown-phase = 30s
management.endpoint.shutdown.enabled=true
management.endpoints.web.exposure.base-path = /actuator
management.endpoint.health.show-details = never
