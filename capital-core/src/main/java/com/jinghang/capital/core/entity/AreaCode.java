package com.jinghang.capital.core.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serial;
import java.io.Serializable;

/**
 * 行政区划表
 */
@Entity
@Table(name = "area_code")
public class AreaCode implements Serializable {
    @Serial
    private static final long serialVersionUID = 3695531764159221737L;

    @Id
    private String code;
    /**
     * 父节点
     */
    private String parentCode;
    /**
     * 名称
     */
    private String name;


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
