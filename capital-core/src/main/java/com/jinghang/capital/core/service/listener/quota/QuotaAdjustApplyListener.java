package com.jinghang.capital.core.service.listener.quota;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.dto.QuotaAdjustApplyDto;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.jinghang.common.util.JsonUtil;
import com.rabbitmq.client.Channel;
import java.nio.charset.StandardCharsets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 调额申请
 */
@Component
public class QuotaAdjustApplyListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(QuotaAdjustApplyListener.class);

    @Autowired
    private ManageService manageService;

    public QuotaAdjustApplyListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @RabbitListener(queues = RabbitConfig.Queues.QUOTA_ADJUST_APPLY)
    public void quotaAdjustApply(Message message, Channel channel) {

        String msg = new String(message.getBody(), StandardCharsets.UTF_8);
        try {

            QuotaAdjustApplyDto dto = JsonUtil.convertToObject(msg, QuotaAdjustApplyDto.class);
            logger.info("调额申请: {}", msg);
            manageService.quotaAdjustApply(dto);
        } catch (Exception e) {
            logger.error("调额申请 异常: {} ", msg, e);
        } finally {
            ackMsg(msg, message, channel);
        }
    }
}
