package com.jinghang.capital.core.enums;

/**
 * <AUTHOR>
 * @description: JointLoanFlagEnum  联合贷标识
 * @date 2024/11/7 14:55
 */
public enum JointLoanFlagEnum {
    WHOLLY_OWNED("WHOLLY_OWNED", "全资"),
    JOINT_LOAN("JOINT_LOAN", "联合贷款");
    private final String code;
    private final String msg;

    JointLoanFlagEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static JointLoanFlagEnum getByCode(String code) {
        for (JointLoanFlagEnum operateTypeEnum : JointLoanFlagEnum.values()) {
            if (operateTypeEnum.getCode().equals(code)) {
                return operateTypeEnum;
            }
        }
        return null;
    }

    public static String getMsgByCode(String code) {
        for (JointLoanFlagEnum operateTypeEnum : JointLoanFlagEnum.values()) {
            if (operateTypeEnum.getCode().equals(code)) {
                return operateTypeEnum.getMsg();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
