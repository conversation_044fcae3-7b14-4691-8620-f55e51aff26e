package com.jinghang.capital.core.service.listener;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.rabbitmq.client.Channel;
import java.nio.charset.StandardCharsets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class BatchRepayApplyListener extends AbstractListener {

    private static final Logger logger = LoggerFactory.getLogger(BatchRepayApplyListener.class);

    @Autowired
    private ManageService manageService;

    public BatchRepayApplyListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @RabbitListener(queues = RabbitConfig.Queues.BATCH_REPAY_APPLY)
    public void listenRepayResult(Message message, Channel channel) {
        String batchRepayId = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            logger.info("监听批量还款申请:{}", batchRepayId);
            manageService.batchRepayApply(batchRepayId);
        } catch (Exception e) {
            logger.error("批量还款申请异常: ", e);
            processException(batchRepayId, message, e, "批量还款申请异常", getMqService()::submitBatchRepayApply);
        } finally {
            ackMsg(batchRepayId, message, channel);
        }
    }
}
