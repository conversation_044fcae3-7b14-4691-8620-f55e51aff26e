package com.jinghang.capital.core.banks;

import com.jinghang.capital.core.entity.AccountBankCard;
import com.jinghang.capital.core.entity.Credit;
import com.jinghang.capital.core.service.CommonService;
import com.jinghang.capital.core.service.credit.FinCreditService;
import com.jinghang.capital.core.vo.protocol.ProtocolSyncRltVo;
import com.jinghang.capital.core.vo.protocol.ProtocolSyncVo;
import com.jinghang.common.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2023/5/16
 */
public abstract class AbstractBankProtocolService implements BankProtocolSyncService {

    @Autowired
    private CommonService commonService;

    @Autowired
    private FinCreditService finCreditService;

    @Override
    public ProtocolSyncRltVo protocolSync(ProtocolSyncVo protocolSyncVo) {
        Credit credit = finCreditService.getCredit(protocolSyncVo.getCreditId());
        String oldCardId = StringUtil.isNotBlank(credit.getCardId()) ? credit.getCardId() : "";
        // 入库
        AccountBankCard accountBankCard = commonService.syncProtocol(credit, protocolSyncVo);
        // 同步资方
        syncBank(credit, accountBankCard, oldCardId);

        return new ProtocolSyncRltVo(accountBankCard.getId());
    }

    public CommonService getCommonService() {
        return commonService;
    }

    public FinCreditService getFinCreditService() {
        return finCreditService;
    }

    public abstract void syncBank(Credit credit, AccountBankCard accountBankCard, String oldCardId);
}
