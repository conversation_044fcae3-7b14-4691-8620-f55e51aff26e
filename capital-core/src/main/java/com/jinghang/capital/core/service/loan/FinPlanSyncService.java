package com.jinghang.capital.core.service.loan;


import com.jinghang.capital.core.convert.entity.PlanConvert;
import com.jinghang.capital.core.entity.ReconciliationFile;
import com.jinghang.capital.core.enums.FileMode;
import com.jinghang.capital.core.enums.SyncState;
import com.jinghang.capital.core.exception.BizErrorCode;
import com.jinghang.capital.core.exception.BizException;
import com.jinghang.capital.core.repository.ReconciliationFileRepository;
import com.jinghang.capital.core.service.FileService;
import com.jinghang.capital.core.vo.repay.PlanPushVo;
import com.jinghang.capital.core.vo.repay.PlanSyncVo;
import com.jinghang.common.http.HttpFramework;
import com.jinghang.common.http.exception.HttpException;
import com.jinghang.common.util.HttpUtil;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/5/18
 */
@Service
public class FinPlanSyncService {
    private static final Logger logger = LoggerFactory.getLogger(FinPlanSyncService.class);

    private ReconciliationFileRepository reconciliationFileRepository;

    @Value(value = "${oss.bucket.name}")
    private String ossBucket;

    @Value(value = "${asset.upload.oss.key.path:fin/assset_tc/}")
    private String ossPath;

    private static final String REC_SPLIT_CHAR = String.valueOf('_');

    @Autowired
    private FileService fileService;

    public FinPlanSyncService(ReconciliationFileRepository reconciliationFileRepository) {
        this.reconciliationFileRepository = reconciliationFileRepository;
    }

    public PlanPushVo getRecFile(String recFileId) {
        ReconciliationFile reconciliationFile = reconciliationFileRepository.findById(recFileId)
                .orElseThrow(() -> new BizException(BizErrorCode.REC_NOT_FOUND));
        return PlanConvert.INSTANCE.toVo(reconciliationFile);
    }

    public void genGuaranteePlanRecord(PlanSyncVo planSyncVo) {
        ReconciliationFile reconciliationFile = PlanConvert.INSTANCE.toRecEntity(planSyncVo);
        // 文件名
        reconciliationFile.setFileName(planSyncVo.getFileName().split(REC_SPLIT_CHAR)[0] + REC_SPLIT_CHAR + planSyncVo.getFileName().split(REC_SPLIT_CHAR)[1]);
        reconciliationFile.setFileDate(LocalDate.parse(planSyncVo.getFileName().split(REC_SPLIT_CHAR)[2].split("\\.")[0], DateTimeFormatter.BASIC_ISO_DATE));
        // 资产方传过来的
        reconciliationFile.setInputUrl(planSyncVo.getFileUrl());

        try {
            // 转存oss
            String ossKey = ossPath + planSyncVo.getFileName();
            HttpUtil.getWithStream(HttpFramework.HTTPCLIENT5, planSyncVo.getFileUrl(), inputStream -> {
                fileService.uploadOss(ossBucket, ossKey, inputStream);
            });
            reconciliationFile.setSourceOssBucket(ossBucket);
            reconciliationFile.setSourceOssKey(ossKey);
            reconciliationFile.setSourceOssUrl(fileService.getOssUrl(ossBucket, ossKey));
        } catch (HttpException e) {
            throw new RuntimeException(e);
        }

        reconciliationFile.setFileType(planSyncVo.getType());
        reconciliationFile.setSyncState(SyncState.N);
        reconciliationFile.setMode(FileMode.OSS);

        reconciliationFileRepository.save(reconciliationFile);
    }
}
