package com.jinghang.capital.core.entity.fpd;


import com.jinghang.capital.core.entity.BaseEntity;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.service.repay.enums.FpdLoanStatus;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.math.BigDecimal;

/**
 * fpd借据明细表;
 *
 * <AUTHOR> http://www.chiner.pro
 * @date : 2025-3-29
 */
@Entity
@Table(name = "fpd_detection_loan")
public class FpdDetectionLoan extends BaseEntity {

    /**
     * fpd检测表ID
     */
    private String fpdDetectionId;
    /**
     * 资方渠道
     */
    @Enumerated(EnumType.STRING)
    private BankChannel channel;
    /**
     * 批次序号
     */
    private Integer batchIndex;
    /**
     * 借据ID
     */
    private String loanId;
    /**
     * 放款金额
     */
    private BigDecimal loanAmt;
    /**
     * fpd借据状态
     */
    @Enumerated(EnumType.STRING)
    private FpdLoanStatus fpdLoanStatus;

    public String getFpdDetectionId() {
        return fpdDetectionId;
    }

    public void setFpdDetectionId(String fpdDetectionId) {
        this.fpdDetectionId = fpdDetectionId;
    }

    public BankChannel getChannel() {
        return channel;
    }

    public void setChannel(BankChannel channel) {
        this.channel = channel;
    }

    public Integer getBatchIndex() {
        return batchIndex;
    }

    public void setBatchIndex(Integer batchIndex) {
        this.batchIndex = batchIndex;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public BigDecimal getLoanAmt() {
        return loanAmt;
    }

    public void setLoanAmt(BigDecimal loanAmt) {
        this.loanAmt = loanAmt;
    }

    public FpdLoanStatus getFpdLoanStatus() {
        return fpdLoanStatus;
    }

    public void setFpdLoanStatus(FpdLoanStatus fpdLoanStatus) {
        this.fpdLoanStatus = fpdLoanStatus;
    }
}
