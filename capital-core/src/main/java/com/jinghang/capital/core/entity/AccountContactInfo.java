package com.jinghang.capital.core.entity;

import com.jinghang.capital.core.enums.Relation;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.io.Serial;


/**
 * 用户联系人表
 */
@Entity
@Table(name = "account_contact_info")
public class AccountContactInfo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = -8732590737599019860L;

    /**
     * 用户id
     */
    private String accountId;
    /**
     * 联系人姓名
     */
    private String name;
    /**
     * 联系人关系
     */
    @Enumerated(EnumType.STRING)
    private Relation relation;
    /**
     * 联系人手机号
     */
    private String phone;


    /**
     * 用户id
     */
    public String getAccountId() {
        return this.accountId;
    }

    /**
     * 用户id
     */
    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    /**
     * 联系人姓名
     */
    public String getName() {
        return this.name;
    }

    /**
     * 联系人姓名
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 联系人关系
     */
    public Relation getRelation() {
        return this.relation;
    }

    /**
     * 联系人关系
     */
    public void setRelation(Relation relation) {
        this.relation = relation;
    }

    /**
     * 联系人手机号
     */
    public String getPhone() {
        return this.phone;
    }

    /**
     * 联系人手机号
     */
    public void setPhone(String phone) {
        this.phone = phone;
    }

    @Override
    public String prefix() {
        return "AC";
    }

}
