package com.jinghang.capital.core.service.listener.covenant;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.rabbitmq.client.Channel;
import java.nio.charset.StandardCharsets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 放款后下载协议文件（特殊类型--以日期为单位整体下载）
 */
@Component
public class CovenantDownloadAgendaListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(CovenantDownloadAgendaListener.class);

    @Autowired
    private ManageService manageService;

    public CovenantDownloadAgendaListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @RabbitListener(queues = RabbitConfig.Queues.COVENANT_DOWNLOAD_AGENDA)
    public void downloadAgendaFile(Message message, Channel channel) {
        String loanId = new String(message.getBody(), StandardCharsets.UTF_8);
        logger.info("放款后下载协议文件（特殊类型--以日期为单位整体下载）: {}", loanId);

        try {
            manageService.covenantDownloadAgenda(loanId);
        } catch (Exception e) {
            logger.error("放款后下载协议文件（特殊类型--以日期为单位整体下载） 异常: {} ", loanId, e);
        } finally {
            ackMsg(loanId, message, channel);
        }
    }
}
