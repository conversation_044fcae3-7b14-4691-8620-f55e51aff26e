package com.jinghang.capital.core.entity;

import com.jinghang.capital.api.dto.FundingModel;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.io.Serial;

/**
 * 湖消 授信流水
 *
 * @Author: Lior
 * @CreateTime: 2025/7/8 19:26
 */
@Entity
@Table(name = "hxbk_credit_flow")
public class HXBKCreditFlow extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 7958567977554320790L;

    /**
     * 授信申请id
     */
    private String creditId;
    /**
     * 平台业务流水号
     */
    private String custId;
    /**
     * 客户编号（资方授信响应）
     */
    private String customNo;
    /**
     * 资方授信流水号
     */
    private String creditNo;
    /**
     * 资方借据号
     */
    private String loanNo;
    /**
     * 资方放款流水号
     */
    private String loanSeq;
    /**
     * 资方还款授权码(试算时返回,还款申请用)
     */
    private String repaymentCode;

    /**
     * 出资模式：ALONE-单独出资,CONFLATE-联合出资
     */
    @Enumerated(EnumType.STRING)
    private FundingModel fundingModel;

    public String getRepaymentCode() {
        return repaymentCode;
    }

    public void setRepaymentCode(String repaymentCode) {
        this.repaymentCode = repaymentCode;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public String getLoanSeq() {
        return loanSeq;
    }

    public void setLoanSeq(String loanSeq) {
        this.loanSeq = loanSeq;
    }

    public FundingModel getFundingModel() {
        return fundingModel;
    }

    public void setFundingModel(FundingModel fundingModel) {
        this.fundingModel = fundingModel;
    }

    public String getCreditId() {
        return creditId;
    }

    public void setCreditId(String creditId) {
        this.creditId = creditId;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getCreditNo() {
        return creditNo;
    }

    public void setCreditNo(String creditNo) {
        this.creditNo = creditNo;
    }

    public String getCustomNo() {
        return customNo;
    }

    public void setCustomNo(String customNo) {
        this.customNo = customNo;
    }
}
