package com.jinghang.capital.core.controller;


import com.jinghang.capital.api.CreditService;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.bank.BankResultBackDto;
import com.jinghang.capital.api.dto.credit.CreditApplyDto;
import com.jinghang.capital.api.dto.credit.CreditQueryDto;
import com.jinghang.capital.api.dto.credit.CreditResultDto;
import com.jinghang.capital.api.dto.credit.ExtInfoDto;
import com.jinghang.capital.api.dto.credit.PreCreditApplyDto;
import com.jinghang.capital.api.dto.credit.PreCreditApplyResultDto;
import com.jinghang.capital.api.dto.credit.RecreditApplyDto;
import com.jinghang.capital.core.convert.apivo.ApiCreditConvert;
import com.jinghang.capital.core.convert.apivo.BankCallBackConvert;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MockService;
import com.jinghang.capital.core.vo.bank.BankResultBackVo;
import com.jinghang.capital.core.vo.credit.CreditApplyVo;
import com.jinghang.capital.core.vo.credit.CreditQueryVo;
import com.jinghang.capital.core.vo.credit.CreditResultVo;
import com.jinghang.capital.core.vo.credit.ExtInfoVo;
import com.jinghang.capital.core.vo.credit.PreCreditApplyResultVo;
import com.jinghang.capital.core.vo.credit.PreCreditApplyVo;
import com.jinghang.capital.core.vo.credit.RecreditApplyVo;
import com.jinghang.common.util.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("credit")
public class CreditController implements CreditService {
    private static final Logger logger = LoggerFactory.getLogger(CreditController.class);

    private final ManageService manageService;

    @Autowired
    private MockService mockService;

    @Value("${sms.mock.qhyp}")
    private Boolean mock;

    @Autowired
    public CreditController(ManageService manageService) {
        this.manageService = manageService;
    }

    /**
     * 授信预审核（国民信托专用）
     */
    @Override
    public RestResult<PreCreditApplyResultDto> preCredit(PreCreditApplyDto preCreditApply) {
        if (mock) {
            return mockService.preCredit(preCreditApply);
        }
        logger.info("begin preCredit: preCreditApply: {}", JsonUtil.toJsonString(preCreditApply));
        PreCreditApplyVo preCreditApplyVo = ApiCreditConvert.INSTANCE.toPreCreditApplyVo(preCreditApply);
        PreCreditApplyResultVo resultVo = manageService.preCredit(preCreditApplyVo);
        PreCreditApplyResultDto resultDto = ApiCreditConvert.INSTANCE.toPreCreditApplyDto(resultVo);
        return RestResult.success(resultDto);
    }

    @Override
    public RestResult<CreditResultDto> credit(CreditApplyDto<ExtInfoDto> creditApplyDto) {
        if (mock) {
            return mockService.credit(creditApplyDto);
        }

        logger.info("begin credit apply: outerCreditId: {}, product: {}, creditAmt: {}, loanAmt: {}",
                creditApplyDto.getSysId(), creditApplyDto.getProduct(), creditApplyDto.getCreditAmt(), creditApplyDto.getLoanAmt());
        CreditApplyVo<ExtInfoVo> creditApplyVo = ApiCreditConvert.INSTANCE.toCreditApplyVo(creditApplyDto);

        CreditResultVo resultVo = manageService.creditApply(creditApplyVo);
        CreditResultDto resultDto = ApiCreditConvert.INSTANCE.toCreditResultDto(resultVo);
        return RestResult.success(resultDto);
    }


    @Override
    public RestResult<CreditResultDto> recredit(RecreditApplyDto recreditApply) {
        logger.info("begin recredit query: recreditApply: {}", JsonUtil.toJsonString(recreditApply));

        RecreditApplyVo recreditApplyVo = ApiCreditConvert.INSTANCE.toRecreditApplyVo(recreditApply);
        CreditResultVo resultVo = manageService.recreditApply(recreditApplyVo);
        CreditResultDto resultDto = ApiCreditConvert.INSTANCE.toCreditResultDto(resultVo);
        return RestResult.success(resultDto);
    }


    @Override
    public RestResult<CreditResultDto> queryResult(CreditQueryDto creditQuery) {
        if (mock) {
            return mockService.queryResult(creditQuery);
        }

        logger.info("begin credit query: outerCreditId: {}", creditQuery.getSysId());

        CreditQueryVo creditApplyVo = ApiCreditConvert.INSTANCE.toCreditQueryVo(creditQuery);
        CreditResultVo resultVo = manageService.creditQuery(creditApplyVo);
        CreditResultDto creditDto = ApiCreditConvert.INSTANCE.toCreditResultDto(resultVo);
        return RestResult.success(creditDto);
    }


    /**
     * 风控失败，通知core
     *
     * @param creditApplyDto 授信申请记录dto
     * @return
     */
    @Override
    public RestResult<CreditResultDto> failedNotify(CreditApplyDto<ExtInfoDto> creditApplyDto) {


        logger.info("begin risk failed credit apply: outerCreditId: {}, product: {}", creditApplyDto.getSysId(), creditApplyDto.getProduct());
        CreditApplyVo<ExtInfoVo> creditApplyVo = ApiCreditConvert.INSTANCE.toCreditApplyVo(creditApplyDto);

        CreditResultVo resultVo = manageService.creditRiskFailedNotify(creditApplyVo);
        CreditResultDto resultDto = ApiCreditConvert.INSTANCE.toCreditResultDto(resultVo);
        return RestResult.success(resultDto);
    }

    /**
     * 授信结果回调
     *
     * @param bankResultBackDto
     * @return
     */
    @Override
    public RestResult<BankResultBackDto> creditResultBack(BankResultBackDto bankResultBackDto) {
        logger.info("授信结果回调入参：{}", JsonUtil.toJsonString(bankResultBackDto));
        BankResultBackVo vo = BankCallBackConvert.INSTANCE.toBankResultBackVo(bankResultBackDto);
        String callbackResStr = manageService.creditResultCallback(vo.getBankChannel(), vo.getJson());
        bankResultBackDto.setJson(callbackResStr);
        logger.info("授信结果回调出参：{}", JsonUtil.toJsonString(bankResultBackDto));
        return RestResult.success(bankResultBackDto);
    }
}
