package com.jinghang.capital.core.service;

import com.jinghang.capital.core.config.RabbitConfig;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import org.springframework.amqp.core.MessageBuilder;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

public class MqService {

    /**
     * 60秒
     */
    private static final int DEFAULT_TTL = 60000;

    /**
     * 30秒
     */
    private static final int DEFAULT_SHORT_TTL = 30000;

    private static final int OFFLINE_DEFAULT_TTL = 600000;

    /**
     * 5秒
     */
    private static final int FB_ADJUST_TTL = 5000;

    /**
     * 10秒
     */
    private static final int SIGNATURE_NEW_QUERY_TTL = 10000;

    private static final int BATCH_REPAY_TTL = 80000;

    private static final int GMXT_CREDIT_QUERY_TTL = 15000;

    private static final int GMXT_CONTRACT_QUERY_TTL = 30000;

    private static final int GMXT_LOAN_QUERY_TTL = 35000;

    private static final int SUBSTITUTE_WAIT_TTL = 10000;

    private static final int CLAIM_AFTER_NOTIFY_TTL = 5000;

    private static final int TRUST_FILE_SIGN_QUERY_TTL = 120000;

    private final RabbitTemplate rabbitTemplate;

    public MqService(RabbitTemplate rabbitTemplate) {
        this.rabbitTemplate = rabbitTemplate;
    }

    public void submitGmxtCreditResultQueryDelay(String creditId) {
        submit(creditId, null, RabbitConfig.Exchanges.CREDIT,
                RabbitConfig.RoutingKeys.GMXT_CREDIT_QUERY_DELAY, GMXT_CREDIT_QUERY_TTL);
    }

    public void submitCreditResultQueryDelay(String creditId) {
        submitCreditResultQueryDelay(creditId, null);
    }

    public void submitCreditResultQueryDelay(String creditId, Map<String, Object> headers) {
        submit(creditId, headers, RabbitConfig.Exchanges.CREDIT,
                RabbitConfig.RoutingKeys.CREDIT_QUERY_DELAY, DEFAULT_TTL);
    }

    public void submitCreditApply(String creditId) {
        submit(creditId, null, RabbitConfig.Exchanges.CREDIT,
                RabbitConfig.RoutingKeys.CREDIT_STAGE_UPLOAD, null);
    }

    public void submitCreditApplyDelay(String creditId) {
        submitCreditApplyDelay(creditId, null);
    }

    public void submitCreditApplyDelay(String creditId, Map<String, Object> headers) {
        submit(creditId, headers, RabbitConfig.Exchanges.CREDIT,
                RabbitConfig.RoutingKeys.CREDIT_STAGE_UPLOAD_DELAY, DEFAULT_TTL);
    }


    public void submitCreditFailedNotify(String failedCreditId) {
        submit(failedCreditId, null, RabbitConfig.Exchanges.CREDIT,
                RabbitConfig.RoutingKeys.CREDIT_FAILED_NOTIFY, null);
    }

    public void submitCreditFailedNotifyDelay(String failedCreditId) {
        submitCreditFailedNotifyDelay(failedCreditId, null);
    }

    public void submitCreditFailedNotifyDelay(String failedCreditId, Map<String, Object> headers) {
        submit(failedCreditId, headers, RabbitConfig.Exchanges.CREDIT,
                RabbitConfig.RoutingKeys.CREDIT_FAILED_NOTIFY_DELAY, DEFAULT_TTL);
    }


    public void submitGmxtLoanResultQueryDelay(String loanId) {
        submit(loanId, null, RabbitConfig.Exchanges.LOAN,
                RabbitConfig.RoutingKeys.GMXT_LOAN_QUERY_DELAY, GMXT_LOAN_QUERY_TTL);
    }

    public void submitLoanResultQueryDelay(String loanId) {
        submitLoanResultQueryDelay(loanId, null);
    }

    public void submitLoanResultQueryDelay(String loanId, Map<String, Object> headers) {
        submit(loanId, headers, RabbitConfig.Exchanges.LOAN,
                RabbitConfig.RoutingKeys.LOAN_QUERY_DELAY, DEFAULT_TTL);
    }

    public void submitLoanApply(String loanId) {
        submit(loanId, null, RabbitConfig.Exchanges.LOAN,
                RabbitConfig.RoutingKeys.LOAN_APPLY, null);
    }

    public void submitLoanApplyDelay(String loanId) {
        submitLoanApplyDelay(loanId, null);
    }

    public void submitLoanApplyDelay(String loanId, Map<String, Object> headers) {
        submit(loanId, headers, RabbitConfig.Exchanges.LOAN,
                RabbitConfig.RoutingKeys.LOAN_APPLY_DELAY, DEFAULT_TTL);
    }

    public void submitLoanReplanResultQueryDelay(String loanId) {
        submitLoanReplanResultQueryDelay(loanId, null);
    }

    public void submitLoanReplanResultQueryDelay(String loanId, Map<String, Object> headers) {
        submit(loanId, headers, RabbitConfig.Exchanges.LOAN,
                RabbitConfig.RoutingKeys.LOAN_REPLAN_QUERY_DELAY, DEFAULT_TTL);
    }


    public void submitLoanReplanSyncDelay(String loanId) {
        submitLoanReplanSyncDelay(loanId, null);
    }

    public void submitLoanReplanSyncDelay(String loanId, Map<String, Object> headers) {
        submit(loanId, headers, RabbitConfig.Exchanges.LOAN,
                RabbitConfig.RoutingKeys.LOAN_REPLAN_SYNC_DELAY, DEFAULT_TTL);
    }


    public void submitQinjiaFileUploadDelay(String loanId) {
        submitQinjiaFileUploadDelay(loanId, null);
    }

    public void submitQinjiaFileUploadDelay(String loanId, Map<String, Object> headers) {
        submit(loanId, headers, RabbitConfig.Exchanges.LOAN,
                RabbitConfig.RoutingKeys.QINJIA_FILE_UPLOAD_DELAY, DEFAULT_TTL);
    }

    public void submitGmxtUsingLettersQueryDelay(String creditId) {
        submitGmxtUsingLettersQueryDelay(creditId, null);
    }

    public void submitGmxtUsingLettersQueryDelay(String creditId, Map<String, Object> headers) {
        submit(creditId, headers, RabbitConfig.Exchanges.LOAN,
                RabbitConfig.RoutingKeys.GMXT_USING_LETTERS_QUERY_DELAY, GMXT_CREDIT_QUERY_TTL);
    }

    public void submitRepayQueryDelay(String repayId) {
        submitRepayQueryDelay(repayId, null);
    }

    public void submitRepayQueryDelay(String repayId, Map<String, Object> headers) {
        submit(repayId, headers, RabbitConfig.Exchanges.REPAY, RabbitConfig.RoutingKeys.REPAY_QUERY_DELAY, DEFAULT_TTL);
    }

    public void submitRepayQueryDelay(String chargeId, Map<String, Object> headers, Integer ddl) {
        submit(chargeId, headers, RabbitConfig.Exchanges.REPAY, RabbitConfig.RoutingKeys.REPAY_QUERY_DELAY, ddl);
    }

    public void submitClaimAfterNotify(String claimAfterNotifyId) {
        submit(claimAfterNotifyId, null, RabbitConfig.Exchanges.REPAY, RabbitConfig.RoutingKeys.CLAIM_AFTER_NOTIFY, null);
    }

    public void submitClaimAfterNotifyDelay(String claimAfterNotifyId) {
        submitClaimAfterNotifyDelay(claimAfterNotifyId, null);
    }

    public void submitClaimAfterNotifyDelay(String claimAfterNotifyId, Map<String, Object> headers) {
        submit(claimAfterNotifyId, headers, RabbitConfig.Exchanges.REPAY, RabbitConfig.RoutingKeys.CLAIM_AFTER_NOTIFY_DELAY_RK, CLAIM_AFTER_NOTIFY_TTL);
    }


    public void submitClaimApply(String bankLoanReplanId) {
        submit(bankLoanReplanId, null, RabbitConfig.Exchanges.REPAY, RabbitConfig.RoutingKeys.CLAIM_APPLY, null);
    }

    public void submitClaimApplyDelay(String bankLoanReplanId) {
        submitClaimApplyDelay(bankLoanReplanId, null);
    }

    public void submitClaimApplyDelay(String bankLoanReplanId, Map<String, Object> headers) {
        submit(bankLoanReplanId, headers, RabbitConfig.Exchanges.REPAY, RabbitConfig.RoutingKeys.CLAIM_APPLY_DELAY, SUBSTITUTE_WAIT_TTL);
    }

    public void submitClaimQueryDelay(String bankLoanReplanId) {
        submitClaimQueryDelay(bankLoanReplanId, null, DEFAULT_TTL);
    }

    public void submitClaimQueryDelay(String bankLoanReplanId, Map<String, Object> headers, Integer ddl) {
        submit(bankLoanReplanId, headers, RabbitConfig.Exchanges.REPAY, RabbitConfig.RoutingKeys.CLAIM_QUERY_DELAY, ddl);
    }

    public void submitClaimQueryDelay(String bankLoanReplanId, Map<String, Object> headers) {
        submit(bankLoanReplanId, headers, RabbitConfig.Exchanges.REPAY, RabbitConfig.RoutingKeys.CLAIM_QUERY_DELAY, DEFAULT_TTL);
    }


    public void submitFlowClaimApply(String claimId) {
        submit(claimId, null, RabbitConfig.Exchanges.CLAIM, RabbitConfig.RoutingKeys.FLOW_CLAIM_APPLY, null);
    }

    public void submitFlowClaimApplyDelay(String claimId) {
        submitFlowClaimApplyDelay(claimId, null);
    }

    public void submitFlowClaimApplyDelay(String claimId, Map<String, Object> headers) {
        submit(claimId, headers, RabbitConfig.Exchanges.CLAIM, RabbitConfig.RoutingKeys.FLOW_CLAIM_APPLY_DELAY, SUBSTITUTE_WAIT_TTL);
    }

    public void submitFlowClaimQueryDelay(String claimId) {
        submitFlowClaimQueryDelay(claimId, null, DEFAULT_TTL);
    }

    public void submitFlowClaimQueryDelay(String claimId, Map<String, Object> headers, Integer ddl) {
        submit(claimId, headers, RabbitConfig.Exchanges.CLAIM, RabbitConfig.RoutingKeys.FLOW_CLAIM_QUERY_DELAY, ddl);
    }

    public void submitFlowClaimQueryDelay(String claimId, Map<String, Object> headers) {
        submit(claimId, headers, RabbitConfig.Exchanges.CLAIM, RabbitConfig.RoutingKeys.FLOW_CLAIM_QUERY_DELAY, DEFAULT_TTL);
    }

    public void submitAdjustCreditQuery(String adjustCreditId) {
        submit(adjustCreditId, null, RabbitConfig.Exchanges.CREDIT,
                RabbitConfig.RoutingKeys.CREDIT_ADJUST_QUERY, null);
    }

    public void submitAdjustCreditQueryDelay(String adjustCreditId) {
        submit(adjustCreditId, null, RabbitConfig.Exchanges.CREDIT,
                RabbitConfig.RoutingKeys.CREDIT_ADJUST_QUERY_DELAY, FB_ADJUST_TTL);
    }

    public void submitAdjustCreditQueryDelay(String adjustCreditId, Map<String, Object> headers) {
        submit(adjustCreditId, headers, RabbitConfig.Exchanges.CREDIT,
                RabbitConfig.RoutingKeys.CREDIT_ADJUST_QUERY_DELAY, FB_ADJUST_TTL);
    }


    public void submitCreditNewAdjustQuery(String adjustCreditMsg) {
        submit(adjustCreditMsg, null, RabbitConfig.Exchanges.CREDIT,
                RabbitConfig.RoutingKeys.CREDIT_NEW_ADJUST_QUERY, null);
    }

    public void submitCreditNewAdjustQueryDelay(String adjustCreditMsg) {
        submitCreditNewAdjustQueryDelay(adjustCreditMsg, null);
    }

    public void submitCreditNewAdjustQueryDelay(String adjustCreditMsg, Map<String, Object> headers) {
        submit(adjustCreditMsg, headers, RabbitConfig.Exchanges.CREDIT,
                RabbitConfig.RoutingKeys.CREDIT_NEW_ADJUST_QUERY_DELAY, FB_ADJUST_TTL);
    }


    public void signatureQueryDelay(String signId) {
        signatureQueryDelay(signId, null);
    }

    public void signatureQueryDelay(String signId, Map<String, Object> headers) {
        submit(signId, headers, RabbitConfig.Exchanges.SIGNATURE, RabbitConfig.RoutingKeys.SIGNATURE_QUERY_DELAY, DEFAULT_TTL);
    }

    public void signatureQuery(String signId) {
        submit(signId, null, RabbitConfig.Exchanges.SIGNATURE, RabbitConfig.RoutingKeys.SIGNATURE_QUERY, null);
    }

    // new
    public void signatureNewQueryDelay(String signId) {
        signatureNewQueryDelay(signId, null);
    }

    public void signatureNewQueryDelay(String signId, Map<String, Object> headers) {
        submit(signId, headers, RabbitConfig.Exchanges.SIGNATURE, RabbitConfig.RoutingKeys.SIGNATURE_NEW_QUERY_DELAY, SIGNATURE_NEW_QUERY_TTL);
    }

    public void signatureNewQuery(String signId) {
        submit(signId, null, RabbitConfig.Exchanges.SIGNATURE, RabbitConfig.RoutingKeys.SIGNATURE_NEW_QUERY, null);
    }

    public void signatureApplyDelay(String signId) {
        signatureApplyDelay(signId, null);
    }

    public void signatureApplyDelay(String signId, Map<String, Object> headers) {
        submit(signId, headers, RabbitConfig.Exchanges.SIGNATURE, RabbitConfig.RoutingKeys.SIGNATURE_APPLY_DELAY, DEFAULT_TTL);
    }

    public void signatureApply(String signId) {
        submit(signId, null, RabbitConfig.Exchanges.SIGNATURE, RabbitConfig.RoutingKeys.SIGNATURE_APPLY, null);
    }


    // contract mq

    public void submitContractDownload(String contractDto) {
        submit(contractDto, null, RabbitConfig.Exchanges.CONTRACT, RabbitConfig.RoutingKeys.CONTRACT_DOWNLOAD, null);
    }

    public void submitGmxtContractDownloadOtherDelay(String contractDto) {
        submit(contractDto, null, RabbitConfig.Exchanges.CONTRACT, RabbitConfig.RoutingKeys.GMXT_CONTRACT_DOWNLOAD_DELAY, GMXT_CONTRACT_QUERY_TTL);

    }

    public void submitContractDownloadDelay(String contractDto) {
        submitContractDownloadDelay(contractDto, null);
    }

    public void submitContractDownloadDelay(String contractDto, Map<String, Object> headers) {
        submitContractDownloadDelay(contractDto, headers, DEFAULT_TTL);
    }

    public void submitContractDownloadDelay(String contractDto, Map<String, Object> headers, Integer ddl) {
        submit(contractDto, headers, RabbitConfig.Exchanges.CONTRACT, RabbitConfig.RoutingKeys.CONTRACT_DOWNLOAD_DELAY, ddl);
    }


    public void submitContractUpload(String contractDto) {
        submit(contractDto, null, RabbitConfig.Exchanges.CONTRACT, RabbitConfig.RoutingKeys.CONTRACT_UPLOAD, null);
    }


    public void submitGmxtContractUploadDelay(String contractDto) {
        submit(contractDto, null, RabbitConfig.Exchanges.CONTRACT, RabbitConfig.RoutingKeys.GMXT_CONTRACT_UPLOAD_DELAY, SIGNATURE_NEW_QUERY_TTL);
    }

    public void submitContractUploadDelay(String contractDto) {
        submitContractUploadDelay(contractDto, null);
    }

    public void submitContractUploadDelay(String contractDto, Map<String, Object> headers) {
        submitContractUploadDelay(contractDto, headers, DEFAULT_SHORT_TTL);
    }

    public void submitContractUploadDelay(String contractDto, Map<String, Object> headers, Integer ddl) {
        submit(contractDto, headers, RabbitConfig.Exchanges.CONTRACT, RabbitConfig.RoutingKeys.CONTRACT_UPLOAD_DELAY, ddl);
    }

    /**
     * 资方电子签章查询
     */
    public void submitBankSignQuery(String contractDto) {
        submit(contractDto, null, RabbitConfig.Exchanges.CONTRACT, RabbitConfig.RoutingKeys.BANK_SIGN_QUERY, null);
    }

    public void submitBankSignQueryDelay(String contractDto) {
        submitBankSignQueryDelay(contractDto, null);
    }

    public void submitBankSignQueryDelay(String contractDto, Map<String, Object> headers) {
        submitBankSignQueryDelay(contractDto, headers, DEFAULT_TTL);
    }

    public void submitBankSignQueryDelay(String contractDto, Map<String, Object> headers, Integer ddl) {
        submit(contractDto, headers, RabbitConfig.Exchanges.CONTRACT, RabbitConfig.RoutingKeys.BANK_SIGN_QUERY_DELAY, ddl);
    }

    public void submitLoanUploadImage(String accountId) {
        submit(accountId, null, RabbitConfig.Exchanges.LOAN, RabbitConfig.RoutingKeys.LOAN_UPLOAD_IMAGE, null);
    }

    public void submitLoanUploadImageDelay(String accountId, Map<String, Object> headers) {
        submit(accountId, headers, RabbitConfig.Exchanges.LOAN, RabbitConfig.RoutingKeys.LOAN_UPLOAD_IMAGE_DELAY, DEFAULT_TTL);
    }

    /**
     * 扣款请求消息
     * 华通先扣款，再还款， 消息拆分
     *
     * @param deductId 扣款ID
     * @param headers
     */
    public void submitRepayQueryDeductDelay(String deductId, Map<String, Object> headers) {
        submit(deductId, headers, RabbitConfig.Exchanges.REPAY, RabbitConfig.RoutingKeys.REPAY_DEDUCT_QUERY_DELAY, DEFAULT_TTL);
    }

    /**
     * 还款请求消息
     * 华通先扣款，再还款， 消息拆分
     *
     * @param repayId 对资还款ID
     * @param headers
     */
    public void submitRepayApply(String repayId, Map<String, Object> headers) {
        submit(repayId, headers, RabbitConfig.Exchanges.REPAY, RabbitConfig.RoutingKeys.REPAY_APPLY, null);
    }

    public void submitBatchRepayApply(String repayId, Map<String, Object> headers) {
        submit(repayId, headers, RabbitConfig.Exchanges.REPAY, RabbitConfig.RoutingKeys.BATCH_REPAY_APPLY, null);
    }

    public void submitBatchRepayApply(String repayId) {
        submit(repayId, null, RabbitConfig.Exchanges.REPAY, RabbitConfig.RoutingKeys.BATCH_REPAY_APPLY, null);
    }

    public void submitBatchRepayQueryDelay(String repayId) {
        submitBatchRepayQueryDelay(repayId, null);
    }

    public void submitBatchRepayQueryDelay(String repayId, Map<String, Object> headers) {
        submit(repayId, headers, RabbitConfig.Exchanges.REPAY,
                RabbitConfig.RoutingKeys.BATCH_REPAY_QUERY_DELAY, BATCH_REPAY_TTL);
    }


    public void submitGmxtTrustRepayApply(String repayId, Map<String, Object> headers) {
        submit(repayId, headers, RabbitConfig.Exchanges.REPAY, RabbitConfig.RoutingKeys.GMXT_TRUST_REPAY_APPLY, null);
    }

    public void submitGmxtTrustRepayApply(String repayId) {
        submit(repayId, null, RabbitConfig.Exchanges.REPAY, RabbitConfig.RoutingKeys.GMXT_TRUST_REPAY_APPLY, null);
    }

    public void submitGmxtTrustRepayQueryDelay(String repayId) {
        submitGmxtTrustRepayQueryDelay(repayId, null);
    }

    public void submitGmxtTrustRepayQueryDelay(String repayId, Map<String, Object> headers) {
        submit(repayId, headers, RabbitConfig.Exchanges.REPAY,
                RabbitConfig.RoutingKeys.GMXT_TRUST_REPAY_QUERY_DELAY, DEFAULT_TTL);
    }


    /**
     * 对资线下还款通知
     *
     * @param customRepayId
     */
    public void submitRepayBankNotify(String customRepayId) {
        submit(customRepayId, null, RabbitConfig.Exchanges.REPAY, RabbitConfig.RoutingKeys.REPAY_BANK_NOTIFY_RK, null);
    }

    public void submitRepayBankNotifyDelay(String customRepayId, Map<String, Object> headers) {
        submit(customRepayId, headers, RabbitConfig.Exchanges.REPAY, RabbitConfig.RoutingKeys.REPAY_BANK_NOTIFY_DELAY_RK, OFFLINE_DEFAULT_TTL);
    }

    public void submitSubstituteBankApply(String substituteId) {
        submit(substituteId, null, RabbitConfig.Exchanges.REPAY, RabbitConfig.RoutingKeys.SUBSTITUTE_BANK_APPLY_RK, null);
    }

    public void submitSubstituteBankApplyDelay(String substituteId) {
        submit(substituteId, null, RabbitConfig.Exchanges.REPAY, RabbitConfig.RoutingKeys.SUBSTITUTE_BANK_APPLY_DELAY_RK, SUBSTITUTE_WAIT_TTL);
    }


    /**
     * 贷后文件上传通知
     *
     * @param creditId 授信id
     */
    public void submitUploadFileNotifyQuery(String creditId) {
        submit(creditId, null, RabbitConfig.Exchanges.LOAN,
                RabbitConfig.RoutingKeys.FILE_UPLOAD_NOTIFY, null);
    }

    /**
     * 贷后文件上传通知
     *
     * @param creditId 授信id
     */
    public void submitUploadFileNotifyQueryDelay(String creditId) {
        submitUploadFileNotifyQueryDelay(creditId, null);
    }

    public void submitUploadFileNotifyQueryDelay(String creditId, Map<String, Object> headers) {
        submit(creditId, headers, RabbitConfig.Exchanges.LOAN,
                RabbitConfig.RoutingKeys.FILE_UPLOAD_NOTIFY_DELAY, DEFAULT_TTL);
    }

    public void submitZyebankBindChangeDelay(String changeBingCardReq) {
        submit(changeBingCardReq, null, RabbitConfig.Exchanges.REPAY,
                RabbitConfig.RoutingKeys.ZYEBANK_BIND_CHANGE_DELAY, DEFAULT_TTL);
    }

    public void loanAfterAgreementSign(String loanId) {
        submit(loanId, null, RabbitConfig.Exchanges.LOAN, RabbitConfig.RoutingKeys.LOAN_AFTER_AGREEMENT_SIGN, null);
    }

    public void submitLoanAfterAgreementSignQueryDelay(String loanId) {
        submitLoanAfterAgreementSignQueryDelay(loanId, null);
    }

    public void submitLoanAfterAgreementSignQueryDelay(String loanId, Map<String, Object> headers) {
        submit(loanId, headers, RabbitConfig.Exchanges.LOAN,
                RabbitConfig.RoutingKeys.LOAN_AFTER_AGREEMENT_SIGN_DELAY, DEFAULT_TTL);
    }

    public void submitChangeTrustPlanDelay(String body) {
        submit(body, null, RabbitConfig.Exchanges.TRUST,
                RabbitConfig.RoutingKeys.TRUST_PLAN_CHANGE_DELAY, FB_ADJUST_TTL);
    }

    /**
     * 代付结果查询
     */
    public void submitDefrayQueryDelay(String defrayId) {
        submitDefrayQueryDelay(defrayId, null);
    }

    public void submitDefrayQueryDelay(String defrayId, Map<String, Object> headers) {
        submit(defrayId, headers, RabbitConfig.Exchanges.REPAY,
                RabbitConfig.RoutingKeys.DEFRAY_QUERY_DELAY, DEFAULT_TTL);
    }

    /**
     * 放款后，下载借款合同
     *
     * @param loanId 借据编号
     */
    public void submitCovenantDownloadLoanContract(String loanId) {
        submit(loanId, null, RabbitConfig.Exchanges.COVENANT, RabbitConfig.RoutingKeys.COVENANT_DOWNLOAD_LOAN_CONTRACT, null);
    }

    /**
     * 放款后，下载借款凭证
     *
     * @param loanId 借据编号
     */
    public void submitCovenantDownloadLoanVoucher(String loanId) {
        submit(loanId, null, RabbitConfig.Exchanges.COVENANT, RabbitConfig.RoutingKeys.COVENANT_DOWNLOAD_LOAN_VOUCHER, null);
    }

    /**
     * 放款后，下载协议文件（除借款合同、借款凭证外所有文件）
     *
     * @param loanId 借据编号
     */
    public void submitCovenantDownloadOther(String loanId) {
        submit(loanId, null, RabbitConfig.Exchanges.COVENANT, RabbitConfig.RoutingKeys.COVENANT_DOWNLOAD_OTHER, null);
    }

    /**
     * 放款后，下载协议文件（特殊类型--以日期为单位整体下载）
     *
     * @param downloadAgendaJson json数据
     */
    public void submitCovenantDownloadAgenda(String downloadAgendaJson) {
        submit(downloadAgendaJson, null, RabbitConfig.Exchanges.COVENANT, RabbitConfig.RoutingKeys.COVENANT_DOWNLOAD_AGENDA, null);
    }

    /**
     * 下载需申请下载的协议文件--申请
     *
     * @param json json
     */
    public void submitCovenantDownloadAsyncApply(String json) {
        submit(json, null, RabbitConfig.Exchanges.COVENANT, RabbitConfig.RoutingKeys.COVENANT_DOWNLOAD_ASYNC_APPLY, null);
    }

    /**
     * 下载需申请下载的协议文件--查询申请结果
     *
     * @param applyId 申请ID
     */
    public void submitCovenantDownloadAsyncQuery(String applyId) {
        submit(applyId, null, RabbitConfig.Exchanges.COVENANT, RabbitConfig.RoutingKeys.COVENANT_DOWNLOAD_ASYNC_QUERY, null);
    }

    /**
     * 下载需申请下载的协议文件--查询申请结果（延迟消息）
     *
     * @param applyId 申请ID
     */
    public void submitCovenantDownloadAsyncQueryDelay(String applyId) {
        submit(applyId, null, RabbitConfig.Exchanges.COVENANT,
                RabbitConfig.RoutingKeys.COVENANT_DOWNLOAD_ASYNC_QUERY_DELAY, DEFAULT_TTL);
    }

    /**
     * 额度查询
     *
     * @param json json
     */
    public void submitQuotaQuery(String json) {
        submit(json, null, RabbitConfig.Exchanges.QUOTA, RabbitConfig.RoutingKeys.QUOTA_QUERY, null);
    }

    /**
     * 额度查询延迟
     *
     * @param json json
     */
    public void submitQuotaQueryDelay(String json) {
        submit(json, null, RabbitConfig.Exchanges.QUOTA, RabbitConfig.RoutingKeys.QUOTA_QUERY, DEFAULT_TTL);
    }

    /**
     * 额度查询延迟
     *
     * @param json json
     */
    public void submitQuotaQueryDelay(String json, Map<String, Object> headers) {
        submit(json, headers, RabbitConfig.Exchanges.QUOTA, RabbitConfig.RoutingKeys.QUOTA_QUERY, DEFAULT_TTL);
    }

    /**
     * 调额申请
     *
     * @param adjustId adjustId
     */
    public void submitQuotaAdjustApply(String json) {
        submit(json, null, RabbitConfig.Exchanges.QUOTA, RabbitConfig.RoutingKeys.QUOTA_ADJUST_APPLY, null);
    }

    /**
     * 调额申请延迟
     *
     * @param adjustId adjustId
     */
    public void submitQuotaAdjustApplyDelay(String json) {
        submit(json, null, RabbitConfig.Exchanges.QUOTA, RabbitConfig.RoutingKeys.QUOTA_ADJUST_APPLY_DELAY, DEFAULT_TTL);
    }

    /**
     * 调额申请查询
     *
     * @param adjustId adjustId
     */
    public void submitQuotaAdjustResultQuery(String adjustId) {
        submit(adjustId, null, RabbitConfig.Exchanges.QUOTA, RabbitConfig.RoutingKeys.QUOTA_ADJUST_RESULT_QUERY, null);
    }

    /**
     * 调额申请查询延迟
     *
     * @param adjustId adjustId
     */
    public void submitQuotaAdjustResultQueryDelay(String adjustId) {
        submit(adjustId, null, RabbitConfig.Exchanges.QUOTA, RabbitConfig.RoutingKeys.QUOTA_ADJUST_RESULT_QUERY_DELAY, DEFAULT_TTL);
    }

    /**
     * 调额结果回调
     *
     * @param adjustId adjustId
     */
    public void submitQuotaAdjustResultCallback(String adjustId) {
        submit(adjustId, null, RabbitConfig.Exchanges.QUOTA, RabbitConfig.RoutingKeys.QUOTA_ADJUST_RESULT_CALLBACK, null);
    }

    /**
     * 用户信息更新申请
     *
     * @param json json
     */
    public void submitUserInfoUpdateApply(String json) {
        submit(json, null, RabbitConfig.Exchanges.CREDIT, RabbitConfig.RoutingKeys.USER_INFO_UPDATE_APPLY, null);
    }

    /**
     * 用户信息更新结果查询
     *
     * @param json json
     */
    public void submitUserInfoUpdateQuery(String json) {
        submit(json, null, RabbitConfig.Exchanges.CREDIT, RabbitConfig.RoutingKeys.USER_INFO_UPDATE_QUERY, null);
    }

    /**
     * 用户信息更新结果查询延迟
     *
     * @param json json
     */
    public void submitUserInfoUpdateQueryDelay(String json) {
        submit(json, null, RabbitConfig.Exchanges.CREDIT, RabbitConfig.RoutingKeys.USER_INFO_UPDATE_QUERY_DELAY, DEFAULT_TTL);
    }

    /**
     * 延迟查询信托签章文件
     *
     * @param json
     */
    public void submitTrustFileQueryDelay(String json) {
        submitTrustFileQueryDelay(json, null);
    }

    public void submitTrustFileQueryDelay(String json, Map<String, Object> headers) {
        submit(json, headers, RabbitConfig.Exchanges.TRUST, RabbitConfig.RoutingKeys.TRUST_FILE_SIGN_QUERY_DELAY, TRUST_FILE_SIGN_QUERY_TTL);
    }

    /**
     * FPD借据代还处理
     *
     * @param fpdDetectionLoanId
     */
    public void fpdSubstitute(String fpdDetectionLoanId) {
        submit(fpdDetectionLoanId, null, RabbitConfig.Exchanges.REPAY, RabbitConfig.RoutingKeys.FPD_SUBSTITUTE, null);
    }

    private void submit(String body, Map<String, Object> headers, String exchange, String routingKey, Integer delay) {
        MessageBuilder builder = MessageBuilder.withBody(body.getBytes(StandardCharsets.UTF_8));
        if (delay != null) {
            builder.setExpiration(String.valueOf(delay));
        }
        if (headers != null) {
            headers.forEach(builder::setHeader);
        }
        rabbitTemplate.convertAndSend(exchange, routingKey, builder.build());
    }
}
