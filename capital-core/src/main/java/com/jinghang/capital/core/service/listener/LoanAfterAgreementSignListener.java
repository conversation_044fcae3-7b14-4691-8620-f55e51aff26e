package com.jinghang.capital.core.service.listener;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.rabbitmq.client.Channel;
import java.nio.charset.StandardCharsets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 放款后协议签署
 */
@Component
public class LoanAfterAgreementSignListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(LoanAfterAgreementSignListener.class);

    public LoanAfterAgreementSignListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @Autowired
    private ManageService manageService;

    @RabbitListener(queues = RabbitConfig.Queues.LOAN_AFTER_AGREEMENT_SIGN)
    public void loanAfterAgreementSign(Message message, Channel channel) {
        String loanId = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            logger.info("listener loan after agreement sign, loanId: {}", loanId);
            // manageService
            manageService.loanAfterAgreementSign(loanId);
        } catch (Exception e) {
            logger.error("listener loan after agreement sign error, loanId: {}", loanId, e);
        } finally {
            ackMsg(loanId, message, channel);
        }
    }
}
