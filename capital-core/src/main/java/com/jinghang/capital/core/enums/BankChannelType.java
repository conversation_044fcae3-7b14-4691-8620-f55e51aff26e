package com.jinghang.capital.core.enums;

/**
 * 资方业务类型
 * <p>目前针对富民固收(F_BANK_V2) 做区分</p>
 */
public enum BankChannelType {

    /**
     * 旧模式V1
     */
    TYPE_V1,

    /**
     * 新模式 V2
     */
    TYPE_V2;


    public static BankChannelType findByName(String typeName) {
        if (typeName == null) {
            return TYPE_V1;
        }
        BankChannelType[] types = BankChannelType.values();
        for (BankChannelType channelType : types) {
            if (channelType.name().equalsIgnoreCase(typeName)) {
                return channelType;
            }
        }
        return TYPE_V1;
    }
}
