package com.jinghang.capital.core.entity;


import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.service.limiter.enums.RateLimiterFunction;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

/**
 * 限流器配置表;
 *
 * <AUTHOR> http://www.chiner.pro
 * @date : 2025-4-3
 */
@Entity
@Table(name = "rate_limiter_config")
public class RateLimiterConfig extends BaseEntity {

    /**
     * 资方渠道
     */
    @Enumerated(EnumType.STRING)
    private BankChannel channel;
    /**
     * 限流器功能
     */
    @Enumerated(EnumType.STRING)
    private RateLimiterFunction limiterFunction;
    /**
     * 限流器名称（限流器的key）
     */
    private String name;
    /**
     * 限制频率（每秒次数）
     */
    private Long rate;
    /**
     * 限流器描述
     */
    private String limiterDesc;

    public BankChannel getChannel() {
        return channel;
    }

    public void setChannel(BankChannel channel) {
        this.channel = channel;
    }

    public RateLimiterFunction getLimiterFunction() {
        return limiterFunction;
    }

    public void setLimiterFunction(RateLimiterFunction limiterFunction) {
        this.limiterFunction = limiterFunction;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getRate() {
        return rate;
    }

    public void setRate(Long rate) {
        this.rate = rate;
    }

    public String getLimiterDesc() {
        return limiterDesc;
    }

    public void setLimiterDesc(String limiterDesc) {
        this.limiterDesc = limiterDesc;
    }

    protected String prefix() {
        return "RLC";
    }
}
