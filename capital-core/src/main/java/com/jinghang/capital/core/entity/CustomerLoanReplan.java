package com.jinghang.capital.core.entity;


import com.jinghang.capital.core.enums.RepayMode;
import com.jinghang.capital.core.enums.RepayPurpose;
import com.jinghang.capital.core.enums.RepayStatus;
import com.jinghang.capital.core.enums.RepayType;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 对客实还款计划表
 */
@Entity
@Table(name = "customer_loan_replan")
public class CustomerLoanReplan extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 8838640380061260685L;

    /**
     * 借款id
     */
    private String loanId;
    /**
     * 资方渠道
     */
    private String channel;
    /**
     * 期数
     */
    private Integer period;
    /**
     * 对客还款状态;current, clear
     */
    @Enumerated(EnumType.STRING)
    private RepayStatus repayStatus;
    /**
     * 实还时间
     */
    private LocalDateTime actRepayTime;
    /**
     * 实还总金额
     */
    private BigDecimal actTotalAmt;
    /**
     * 实还本金
     */
    private BigDecimal actPrincipalAmt;
    /**
     * 实还利息
     */
    private BigDecimal actInterestAmt;
    /**
     * 实还罚息
     */
    private BigDecimal actPenaltyAmt;
    /**
     * 实还平台罚息（对客罚息-对资罚息）
     */
    private BigDecimal actPlatformPenaltyAmt;
    /**
     * 实还违约金
     */
    private BigDecimal actBreachAmt;
    /**
     * 实还融担费用
     */
    private BigDecimal actGuaranteeAmt;

    /**
     * 实还咨询费
     */
    private BigDecimal actConsultAmt;


    /**
     * 还款类型
     */
    @Enumerated(EnumType.STRING)
    private RepayType repayType;
    /**
     * 还款模式;online,offline
     */
    @Enumerated(EnumType.STRING)
    private RepayMode repayMode;
    /**
     * 还款目的
     */
    @Enumerated(EnumType.STRING)
    private RepayPurpose repayPurpose;


    /**
     * 借款id
     */
    public String getLoanId() {
        return this.loanId;
    }

    /**
     * 借款id
     */
    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    /**
     * 资方渠道
     */
    public String getChannel() {
        return this.channel;
    }

    /**
     * 资方渠道
     */
    public void setChannel(String channel) {
        this.channel = channel;
    }

    /**
     * 期数
     */
    public Integer getPeriod() {
        return this.period;
    }

    /**
     * 期数
     */
    public void setPeriod(Integer period) {
        this.period = period;
    }

    /**
     * 对客还款状态;current, clear
     */
    public RepayStatus getRepayStatus() {
        return this.repayStatus;
    }

    /**
     * 对客还款状态;current, clear
     */
    public void setRepayStatus(RepayStatus repayStatus) {
        this.repayStatus = repayStatus;
    }

    /**
     * 实还时间
     */
    public LocalDateTime getActRepayTime() {
        return this.actRepayTime;
    }

    /**
     * 实还时间
     */
    public void setActRepayTime(LocalDateTime actRepayTime) {
        this.actRepayTime = actRepayTime;
    }

    /**
     * 实还总金额
     */
    public BigDecimal getActTotalAmt() {
        return this.actTotalAmt;
    }

    /**
     * 实还总金额
     */
    public void setActTotalAmt(BigDecimal actTotalAmt) {
        this.actTotalAmt = actTotalAmt;
    }

    /**
     * 实还本金
     */
    public BigDecimal getActPrincipalAmt() {
        return this.actPrincipalAmt;
    }

    /**
     * 实还本金
     */
    public void setActPrincipalAmt(BigDecimal actPrincipalAmt) {
        this.actPrincipalAmt = actPrincipalAmt;
    }

    /**
     * 实还利息
     */
    public BigDecimal getActInterestAmt() {
        return this.actInterestAmt;
    }

    /**
     * 实还利息
     */
    public void setActInterestAmt(BigDecimal actInterestAmt) {
        this.actInterestAmt = actInterestAmt;
    }

    /**
     * 实还罚息
     */
    public BigDecimal getActPenaltyAmt() {
        return this.actPenaltyAmt;
    }

    /**
     * 实还罚息
     */
    public void setActPenaltyAmt(BigDecimal actPenaltyAmt) {
        this.actPenaltyAmt = actPenaltyAmt;
    }

    public BigDecimal getActPlatformPenaltyAmt() {
        return actPlatformPenaltyAmt;
    }

    public void setActPlatformPenaltyAmt(BigDecimal actPlatformPenaltyAmt) {
        this.actPlatformPenaltyAmt = actPlatformPenaltyAmt;
    }

    /**
     * 实还违约金
     */
    public BigDecimal getActBreachAmt() {
        return this.actBreachAmt;
    }

    /**
     * 实还违约金
     */
    public void setActBreachAmt(BigDecimal actBreachAmt) {
        this.actBreachAmt = actBreachAmt;
    }

    /**
     * 实还融担费用
     */
    public BigDecimal getActGuaranteeAmt() {
        return this.actGuaranteeAmt;
    }

    /**
     * 实还融担费用
     */
    public void setActGuaranteeAmt(BigDecimal actGuaranteeAmt) {
        this.actGuaranteeAmt = actGuaranteeAmt;
    }


    public BigDecimal getActConsultAmt() {
        return actConsultAmt;
    }

    public void setActConsultAmt(BigDecimal actConsultAmt) {
        this.actConsultAmt = actConsultAmt;
    }

    /**
     * 还款类型
     */
    public RepayType getRepayType() {
        return this.repayType;
    }

    /**
     * 还款类型
     */
    public void setRepayType(RepayType repayType) {
        this.repayType = repayType;
    }

    /**
     * 还款模式;online,offline
     */
    public RepayMode getRepayMode() {
        return this.repayMode;
    }

    /**
     * 还款模式;online,offline
     */
    public void setRepayMode(RepayMode repayMode) {
        this.repayMode = repayMode;
    }

    /**
     * 还款目的
     */
    public RepayPurpose getRepayPurpose() {
        return this.repayPurpose;
    }

    /**
     * 还款目的
     */
    public void setRepayPurpose(RepayPurpose repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    @Override
    public String prefix() {
        return "CLR";
    }
}
