package com.jinghang.capital.core.entity;


import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.BusinessSource;
import com.jinghang.capital.core.enums.FileType;
import com.jinghang.capital.core.enums.ProcessStatus;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.time.LocalDateTime;

/**
 * 合同下载失败记录表;
 *
 * <AUTHOR> http://www.chiner.pro
 * @date : 2024-11-28
 */
@Entity
@Table(name = "covenant_download_fail_record")
public class CovenantDownloadFailRecord extends BaseEntity {

    /**
     * 资方渠道
     */
    @Enumerated(EnumType.STRING)
    private BankChannel channel;
    /**
     * 借据id
     */
    private String loanId;
    /**
     * 期数
     */
    private Integer period;
    /**
     * 文件类型
     */
    @Enumerated(EnumType.STRING)
    private FileType fileType;
    /**
     * 业务来源
     */
    @Enumerated(EnumType.STRING)
    private BusinessSource businessSource;
    /**
     * 相关业务表id
     */
    private String businessId;
    /**
     * 失败时间
     */
    private LocalDateTime failedTime;
    /**
     * 重试状态
     */
    @Enumerated(EnumType.STRING)
    private ProcessStatus retryStatus;
    /**
     * 重试时间
     */
    private LocalDateTime retryTime;

    public BankChannel getChannel() {
        return channel;
    }

    public void setChannel(BankChannel channel) {
        this.channel = channel;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public FileType getFileType() {
        return fileType;
    }

    public void setFileType(FileType fileType) {
        this.fileType = fileType;
    }

    public BusinessSource getBusinessSource() {
        return businessSource;
    }

    public void setBusinessSource(BusinessSource businessSource) {
        this.businessSource = businessSource;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public LocalDateTime getFailedTime() {
        return failedTime;
    }

    public void setFailedTime(LocalDateTime failedTime) {
        this.failedTime = failedTime;
    }

    public ProcessStatus getRetryStatus() {
        return retryStatus;
    }

    public void setRetryStatus(ProcessStatus retryStatus) {
        this.retryStatus = retryStatus;
    }

    public LocalDateTime getRetryTime() {
        return retryTime;
    }

    public void setRetryTime(LocalDateTime retryTime) {
        this.retryTime = retryTime;
    }
}
