package com.jinghang.capital.core.service.listener.quota;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.rabbitmq.client.Channel;
import java.nio.charset.StandardCharsets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 调额结果回调
 */
@Component
public class QuotaAdjustResultCallbackListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(QuotaAdjustResultCallbackListener.class);

    @Autowired
    private ManageService manageService;

    public QuotaAdjustResultCallbackListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @RabbitListener(queues = RabbitConfig.Queues.QUOTA_ADJUST_RESULT_CALLBACK)
    public void quotaAdjustResultCallback(Message message, Channel channel) {
        String adjustId = new String(message.getBody(), StandardCharsets.UTF_8);
        logger.info("调额结果回调: {}", adjustId);
        try {
            manageService.quotaAdjustResultCallback(adjustId);
        } catch (Exception e) {
            logger.error("调额结果回调 异常: {} ", adjustId, e);
        } finally {
            ackMsg(adjustId, message, channel);
        }
    }
}
