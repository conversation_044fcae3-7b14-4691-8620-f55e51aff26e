package com.jinghang.capital.core.entity;


import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.WhetherState;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 资方配置表
 */
@Entity
@Table(name = "bank_config")
public class BankConfig extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 5697556825676213023L;

    /**
     * 资方渠道
     */
    @Enumerated(EnumType.STRING)
    private BankChannel channel;

    /**
     * 融担公司
     */
    @Enumerated(EnumType.STRING)
    private GuaranteeCompany guaranteeCompany;

    /**
     * 是否启用: Y/N
     *
     */
    @Enumerated(EnumType.STRING)
    private WhetherState enabled;

    /**
     * 代偿天数
     *
     */
    private Integer claimDays;

    /**
     * 代还提前天数
     *
     */
    private Integer substituteAdvanceDays;

    /**
     * 是否接受预充值
     *
     */
    private String acceptPreRecharge;

    /**
     * 倒数第几期不代还
     *
     */
    private Integer lastPeriodUnsubstituted;

    /**
     * 连续回购规则
     *
     */
    private Integer consecutiveCompensations;

    /**
     * 累计回购规则
     *
     */
    private Integer cumulativeCompensations;

    /**
     * 逾期回购规则
     *
     */
    private Integer fullCompensation;

    /**
     * 授信黑暗期开始时间
     *
     */
    private LocalDateTime creditStartTime;

    /**
     * 授信黑暗期结束时间
     *
     */
    private LocalDateTime creditEndTime;

    /**
     * 放款黑暗期开始时间
     *
     */
    private LocalDateTime loanStartTime;

    /**
     * 放款黑暗期结束时间
     *
     */
    private LocalDateTime loanEndTime;

    /**
     * 还款黑暗期开始时间
     *
     */
    private LocalDateTime repayStartTime;

    /**
     * 还款黑暗期结束时间
     *
     */
    private LocalDateTime repayEndTime;

    /**
     * 最小年龄限制
     *
     */
    private Integer minAgeLimit;

    /**
     * 最大年龄限制
     *
     */
    private Integer maxAgeLimit;

    /**
     * 地域限制
     *
     */
    private String regionLimit;

    public BankChannel getChannel() {
        return channel;
    }

    public void setChannel(BankChannel channel) {
        this.channel = channel;
    }

    public GuaranteeCompany getGuaranteeCompany() {
        return guaranteeCompany;
    }

    public void setGuaranteeCompany(GuaranteeCompany guaranteeCompany) {
        this.guaranteeCompany = guaranteeCompany;
    }

    public WhetherState getEnabled() {
        return enabled;
    }

    public void setEnabled(WhetherState enabled) {
        this.enabled = enabled;
    }

    public Integer getClaimDays() {
        return claimDays;
    }

    public void setClaimDays(Integer claimDays) {
        this.claimDays = claimDays;
    }

    public Integer getSubstituteAdvanceDays() {
        return substituteAdvanceDays;
    }

    public void setSubstituteAdvanceDays(Integer substituteAdvanceDays) {
        this.substituteAdvanceDays = substituteAdvanceDays;
    }

    public String getAcceptPreRecharge() {
        return acceptPreRecharge;
    }

    public void setAcceptPreRecharge(String acceptPreRecharge) {
        this.acceptPreRecharge = acceptPreRecharge;
    }

    public Integer getLastPeriodUnsubstituted() {
        return lastPeriodUnsubstituted;
    }

    public void setLastPeriodUnsubstituted(Integer lastPeriodUnsubstituted) {
        this.lastPeriodUnsubstituted = lastPeriodUnsubstituted;
    }

    public Integer getConsecutiveCompensations() {
        return consecutiveCompensations;
    }

    public void setConsecutiveCompensations(Integer consecutiveCompensations) {
        this.consecutiveCompensations = consecutiveCompensations;
    }

    public Integer getCumulativeCompensations() {
        return cumulativeCompensations;
    }

    public void setCumulativeCompensations(Integer cumulativeCompensations) {
        this.cumulativeCompensations = cumulativeCompensations;
    }

    public Integer getFullCompensation() {
        return fullCompensation;
    }

    public void setFullCompensation(Integer fullCompensation) {
        this.fullCompensation = fullCompensation;
    }

    public LocalDateTime getCreditStartTime() {
        return creditStartTime;
    }

    public void setCreditStartTime(LocalDateTime creditStartTime) {
        this.creditStartTime = creditStartTime;
    }

    public LocalDateTime getCreditEndTime() {
        return creditEndTime;
    }

    public void setCreditEndTime(LocalDateTime creditEndTime) {
        this.creditEndTime = creditEndTime;
    }

    public LocalDateTime getLoanStartTime() {
        return loanStartTime;
    }

    public void setLoanStartTime(LocalDateTime loanStartTime) {
        this.loanStartTime = loanStartTime;
    }

    public LocalDateTime getLoanEndTime() {
        return loanEndTime;
    }

    public void setLoanEndTime(LocalDateTime loanEndTime) {
        this.loanEndTime = loanEndTime;
    }

    public LocalDateTime getRepayStartTime() {
        return repayStartTime;
    }

    public void setRepayStartTime(LocalDateTime repayStartTime) {
        this.repayStartTime = repayStartTime;
    }

    public LocalDateTime getRepayEndTime() {
        return repayEndTime;
    }

    public void setRepayEndTime(LocalDateTime repayEndTime) {
        this.repayEndTime = repayEndTime;
    }

    public Integer getMinAgeLimit() {
        return minAgeLimit;
    }

    public void setMinAgeLimit(Integer minAgeLimit) {
        this.minAgeLimit = minAgeLimit;
    }

    public Integer getMaxAgeLimit() {
        return maxAgeLimit;
    }

    public void setMaxAgeLimit(Integer maxAgeLimit) {
        this.maxAgeLimit = maxAgeLimit;
    }

    public String getRegionLimit() {
        return regionLimit;
    }

    public void setRegionLimit(String regionLimit) {
        this.regionLimit = regionLimit;
    }
}
