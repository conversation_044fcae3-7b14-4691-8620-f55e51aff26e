package com.jinghang.capital.core.controller;

import com.jinghang.capital.api.PlanService;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.plan.BankPlanQueryDto;
import com.jinghang.capital.api.dto.plan.BankPlanQueryRltDto;
import com.jinghang.capital.api.dto.plan.PlanOverdueDto;
import com.jinghang.capital.api.dto.plan.PlanSyncDto;
import com.jinghang.capital.api.dto.plan.PlanSyncRltDto;
import com.jinghang.capital.api.dto.repay.PlanDto;
import com.jinghang.capital.core.convert.apivo.ApiPlanConvert;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.vo.repay.BankPlanQueryRltVo;
import com.jinghang.capital.core.vo.repay.BankPlanQueryVo;
import com.jinghang.capital.core.vo.repay.PlanOverdueVo;
import com.jinghang.capital.core.vo.repay.PlanSyncVo;
import com.jinghang.capital.core.vo.repay.PlanVo;
import com.jinghang.common.util.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023/5/17
 */
@RestController
@RequestMapping("plan")
public class PlanController implements PlanService {

    private static final Logger logger = LoggerFactory.getLogger(PlanController.class);

    private final ManageService manageService;

    @Autowired
    public PlanController(ManageService manageService) {
        this.manageService = manageService;
    }

    @Override
    public RestResult<PlanSyncRltDto> sync(PlanSyncDto planSyncDto) {
        logger.info("渠道方同步融担费还款计划:{}-{}, {}", planSyncDto.getProduct(), planSyncDto.getBankChannel(), planSyncDto.getFileName());
        PlanSyncVo planSyncVo = ApiPlanConvert.INSTANCE.toVo(planSyncDto);
        manageService.planSync(planSyncVo);
        return RestResult.success(null);
    }

    @Override
    public RestResult<PlanSyncRltDto> push(String id) {
        manageService.planPush(id);
        return RestResult.success(null);
    }

    @Override
    public RestResult<Void> detailPush(PlanDto planDetailDto) {

        logger.info("渠道方同步融担费还款计划:{}", JsonUtil.toJsonString(planDetailDto));

        PlanVo planVo = ApiPlanConvert.INSTANCE.toVo(planDetailDto);

        manageService.planPushDetail(planDetailDto.getLoanId(), planDetailDto.getSysLoanId(), planVo);
        return RestResult.success(null);
    }

    @Override
    public RestResult<Void> planOverdue(PlanOverdueDto trailDto) {
        logger.info("gmxt plan overdue:{}", JsonUtil.toJsonString(trailDto));
        PlanOverdueVo planOverdueVo = ApiPlanConvert.INSTANCE.toVo(trailDto);
        manageService.planOverdue(planOverdueVo);
        return RestResult.success(null);
    }

    @Override
    public RestResult<BankPlanQueryRltDto> bankPlanQuery(BankPlanQueryDto planQueryDto) {
        logger.info("bank plan query:{}", JsonUtil.toJsonString(planQueryDto));
        BankPlanQueryVo planOverdueVo = ApiPlanConvert.INSTANCE.toVo(planQueryDto);
        BankPlanQueryRltVo planQueryRltVo = manageService.bankPlanQuery(planOverdueVo);
        BankPlanQueryRltDto planQueryRltDto = ApiPlanConvert.INSTANCE.toDto(planQueryRltVo);
        return RestResult.success(planQueryRltDto);
    }
}
