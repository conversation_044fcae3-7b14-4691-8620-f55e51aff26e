package com.jinghang.capital.core.repository;


import com.jinghang.capital.api.dto.DefrayType;
import com.jinghang.capital.core.entity.DefrayRecord;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.ProcessStatus;
import java.time.LocalDate;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;

public interface DefrayRecordRepository extends JpaRepository<DefrayRecord, String> {
    Optional<DefrayRecord> findByChannelAndDefrayTypeAndRecordDate(BankChannel bankChannel, DefrayType defrayType, LocalDate recordDate);

    Optional<DefrayRecord> findByChannelAndDefrayTypeAndRecordDateAndStatus(BankChannel bankChannel, DefrayType defrayType,
                                                                            LocalDate recordDate, ProcessStatus status);
}
