package com.jinghang.capital.core.entity;

import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.enums.PushStatus;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 对资代还记录;
 */
@Entity
@Table(name = "bank_batch_substitute_record")
public class BankBatchSubstituteRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 3695531764159221737L;

    /**
     * 借款id
     *
     */
    private String loanId;

    /**
     * 期数
     *
     */
    private Integer period;

    /**
     * 资方渠道
     */
    @Enumerated(EnumType.STRING)
    private BankChannel channel;

    /**
     * 融担公司
     *
     */
    @Enumerated(EnumType.STRING)
    private GuaranteeCompany guaranteeCompany;

    /**
     * 代还状态
     */
    @Enumerated(EnumType.STRING)
    private ProcessStatus substituteStatus;

    /**
     * 代还推送状态
     *
     */
    @Enumerated(EnumType.STRING)
    private PushStatus pushStatus;

    /**
     * 应还日期
     *
     */
    private LocalDateTime repayDate;

    /**
     * 应代偿日期
     *
     */
    private LocalDate claimDate;

    /**
     * 应代还推送时间
     */
    private LocalDate pushDate;

    /**
     * 实际代还推送时间
     *
     */
    private LocalDateTime actPushTime;

    /**
     * 已累计代偿次数
     *
     */
    private Integer totalCompensations;

    /**
     * 已连续代偿次数
     *
     */
    private Integer consecutiveCompensations;

    /**
     * 应还总金额
     *
     */
    private BigDecimal totalAmt;

    /**
     * 应还本金
     *
     */
    private BigDecimal principalAmt;

    /**
     * 应还利息
     *
     */
    private BigDecimal interestAmt;

    /**
     * 应还罚息
     *
     */
    private BigDecimal penaltyAmt;

    /**
     * 应还融担费
     *
     */
    private BigDecimal guaranteeAmt;

    /**
     * 应还咨询费
     *
     */
    private BigDecimal consultAmt;

    /**
     * 应还违约金
     *
     */
    private BigDecimal breachAmt;

    /**
     * 实还总金额
     *
     */
    private BigDecimal actTotalAmt;

    /**
     * 实还本金
     *
     */
    private BigDecimal actPrincipalAmt;

    /**
     * 实还利息
     *
     */
    private BigDecimal actInterestAmt;

    /**
     * 实还罚息
     *
     */
    private BigDecimal actPenaltyAmt;

    /**
     * 实还融担费
     *
     */
    private BigDecimal actGuaranteeAmt;

    /**
     * 实还咨询费
     *
     */
    private BigDecimal actConsultAmt;

    /**
     * 实还违约金
     *
     */
    private BigDecimal actBreachAmt;

    /**
     * 实际还款时间
     *
     */
    private LocalDateTime actRepayTime;

    /**
     * 银行扣款流水号
     *
     */
    private String bankSerial;

    /**
     * 银行侧还款申请流水号
     *
     */
    private String bankBizId;

    /**
     * 失败原因
     *
     */
    private String failReason;

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public BankChannel getChannel() {
        return channel;
    }

    public void setChannel(BankChannel channel) {
        this.channel = channel;
    }

    public GuaranteeCompany getGuaranteeCompany() {
        return guaranteeCompany;
    }

    public void setGuaranteeCompany(GuaranteeCompany guaranteeCompany) {
        this.guaranteeCompany = guaranteeCompany;
    }

    public ProcessStatus getSubstituteStatus() {
        return substituteStatus;
    }

    public void setSubstituteStatus(ProcessStatus substituteStatus) {
        this.substituteStatus = substituteStatus;
    }

    public PushStatus getPushStatus() {
        return pushStatus;
    }

    public void setPushStatus(PushStatus pushStatus) {
        this.pushStatus = pushStatus;
    }

    public LocalDateTime getRepayDate() {
        return repayDate;
    }

    public void setRepayDate(LocalDateTime repayDate) {
        this.repayDate = repayDate;
    }

    public LocalDate getClaimDate() {
        return claimDate;
    }

    public void setClaimDate(LocalDate claimDate) {
        this.claimDate = claimDate;
    }

    public LocalDate getPushDate() {
        return pushDate;
    }

    public void setPushDate(LocalDate pushDate) {
        this.pushDate = pushDate;
    }

    public LocalDateTime getActPushTime() {
        return actPushTime;
    }

    public void setActPushTime(LocalDateTime actPushTime) {
        this.actPushTime = actPushTime;
    }

    public Integer getTotalCompensations() {
        return totalCompensations;
    }

    public void setTotalCompensations(Integer totalCompensations) {
        this.totalCompensations = totalCompensations;
    }

    public Integer getConsecutiveCompensations() {
        return consecutiveCompensations;
    }

    public void setConsecutiveCompensations(Integer consecutiveCompensations) {
        this.consecutiveCompensations = consecutiveCompensations;
    }

    public BigDecimal getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(BigDecimal totalAmt) {
        this.totalAmt = totalAmt;
    }

    public BigDecimal getPrincipalAmt() {
        return principalAmt;
    }

    public void setPrincipalAmt(BigDecimal principalAmt) {
        this.principalAmt = principalAmt;
    }

    public BigDecimal getInterestAmt() {
        return interestAmt;
    }

    public void setInterestAmt(BigDecimal interestAmt) {
        this.interestAmt = interestAmt;
    }

    public BigDecimal getPenaltyAmt() {
        return penaltyAmt;
    }

    public void setPenaltyAmt(BigDecimal penaltyAmt) {
        this.penaltyAmt = penaltyAmt;
    }

    public BigDecimal getGuaranteeAmt() {
        return guaranteeAmt;
    }

    public void setGuaranteeAmt(BigDecimal guaranteeAmt) {
        this.guaranteeAmt = guaranteeAmt;
    }

    public BigDecimal getConsultAmt() {
        return consultAmt;
    }

    public void setConsultAmt(BigDecimal consultAmt) {
        this.consultAmt = consultAmt;
    }

    public BigDecimal getBreachAmt() {
        return breachAmt;
    }

    public void setBreachAmt(BigDecimal breachAmt) {
        this.breachAmt = breachAmt;
    }

    public BigDecimal getActTotalAmt() {
        return actTotalAmt;
    }

    public void setActTotalAmt(BigDecimal actTotalAmt) {
        this.actTotalAmt = actTotalAmt;
    }

    public BigDecimal getActPrincipalAmt() {
        return actPrincipalAmt;
    }

    public void setActPrincipalAmt(BigDecimal actPrincipalAmt) {
        this.actPrincipalAmt = actPrincipalAmt;
    }

    public BigDecimal getActInterestAmt() {
        return actInterestAmt;
    }

    public void setActInterestAmt(BigDecimal actInterestAmt) {
        this.actInterestAmt = actInterestAmt;
    }

    public BigDecimal getActPenaltyAmt() {
        return actPenaltyAmt;
    }

    public void setActPenaltyAmt(BigDecimal actPenaltyAmt) {
        this.actPenaltyAmt = actPenaltyAmt;
    }

    public BigDecimal getActGuaranteeAmt() {
        return actGuaranteeAmt;
    }

    public void setActGuaranteeAmt(BigDecimal actGuaranteeAmt) {
        this.actGuaranteeAmt = actGuaranteeAmt;
    }

    public BigDecimal getActConsultAmt() {
        return actConsultAmt;
    }

    public void setActConsultAmt(BigDecimal actConsultAmt) {
        this.actConsultAmt = actConsultAmt;
    }

    public BigDecimal getActBreachAmt() {
        return actBreachAmt;
    }

    public void setActBreachAmt(BigDecimal actBreachAmt) {
        this.actBreachAmt = actBreachAmt;
    }

    public LocalDateTime getActRepayTime() {
        return actRepayTime;
    }

    public void setActRepayTime(LocalDateTime actRepayTime) {
        this.actRepayTime = actRepayTime;
    }

    public String getBankSerial() {
        return bankSerial;
    }

    public void setBankSerial(String bankSerial) {
        this.bankSerial = bankSerial;
    }

    public String getBankBizId() {
        return bankBizId;
    }

    public void setBankBizId(String bankBizId) {
        this.bankBizId = bankBizId;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    @Override
    public String prefix() {
        return "BBS";
    }
}
