package com.jinghang.capital.core.service.listener;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.jinghang.capital.core.service.agreement.AgreementService;
import com.rabbitmq.client.Channel;
import java.nio.charset.StandardCharsets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 新签章服务的查询监听
 */
@Component
public class AgreementNewQueryListener extends AbstractListener {

    private static final Logger logger = LoggerFactory.getLogger(AgreementNewQueryListener.class);

    public AgreementNewQueryListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @Autowired
    private AgreementService agreementService;

    @RabbitListener(queues = RabbitConfig.Queues.SIGNATURE_NEW_QUERY)
    public void listenNewSignQuery(Message message, Channel channel) {
        String signId = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            logger.info("监听新签章签章:{}", signId);
            agreementService.signNewQuery(signId);
        } catch (Exception e) {
            logger.error("sign-service query error", e);
            processException(signId, message, e, "查询新签章异常", getMqService()::signatureNewQueryDelay);
        } finally {
            ackMsg(signId, message, channel);
        }
    }
}
