package com.jinghang.capital.core.config;

import brave.Tracer;
import com.jinghang.capital.core.service.WarningService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.lang.Nullable;

@Configuration
public class WarningConfig {

    @Value("${warning.key.mq}")
    private String mqWarningKey;

    @Bean
    public WarningService mqWarningService(@Nullable Tracer tracer) {
        return new WarningService(mqWarningKey, tracer);
    }

}
