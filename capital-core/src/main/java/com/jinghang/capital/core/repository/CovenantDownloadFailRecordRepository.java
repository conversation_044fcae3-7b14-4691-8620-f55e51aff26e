package com.jinghang.capital.core.repository;


import com.jinghang.capital.core.entity.CovenantDownloadFailRecord;
import com.jinghang.capital.core.enums.FileType;
import com.jinghang.capital.core.enums.ProcessStatus;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

public interface CovenantDownloadFailRecordRepository extends JpaRepository<CovenantDownloadFailRecord, String> {

    List<CovenantDownloadFailRecord> findByLoanIdAndFileTypeAndRetryStatus(String loanId, FileType fileType, ProcessStatus retryStatus);

    List<CovenantDownloadFailRecord> findByLoanIdAndFileTypeAndRemarkAndRetryStatus(String loanId, FileType fileType, String remark, ProcessStatus retryStatus);
}
