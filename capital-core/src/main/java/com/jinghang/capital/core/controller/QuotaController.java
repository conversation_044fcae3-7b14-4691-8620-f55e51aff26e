package com.jinghang.capital.core.controller;

import com.jinghang.capital.api.QuotaService;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.bank.BankResultBackDto;
import com.jinghang.capital.core.convert.apivo.BankCallBackConvert;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.vo.bank.BankResultBackVo;
import com.jinghang.common.util.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("quota")
public class QuotaController implements QuotaService {

    private static final Logger logger = LoggerFactory.getLogger(QuotaController.class);

    private final ManageService manageService;

    @Autowired
    public QuotaController(ManageService manageService) {
        this.manageService = manageService;
    }

    /**
     * 调额结果回调
     *
     * @param bankResultBackDto
     * @return
     */
    @Override
    public RestResult<BankResultBackDto> upQuotaBack(BankResultBackDto bankResultBackDto) {
        logger.info("调额结果回调入参：{}", JsonUtil.toJsonString(bankResultBackDto));
        BankResultBackVo vo = BankCallBackConvert.INSTANCE.toBankResultBackVo(bankResultBackDto);
        String callbackResStr = manageService.bankQuotaAdjustResultCallback(vo.getBankChannel(), vo.getJson());
        bankResultBackDto.setJson(callbackResStr);
        logger.info("调额结果回调出参：{}", JsonUtil.toJsonString(bankResultBackDto));
        return RestResult.success(bankResultBackDto);
    }
}
