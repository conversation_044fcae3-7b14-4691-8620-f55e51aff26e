package com.jinghang.capital.core.entity.hxbk;

import com.jinghang.capital.core.entity.BaseEntity;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @作者 Mr.sandman
 * @时间 2025/07/09 14:42
 */
@Entity
@Table(name = "hxbk_recc_repay")
public class HXBKReccRepay extends BaseEntity implements Serializable {

    // 业务唯一ID
    private String reccId;

    // 系统loanID
    private String sysId;

    // 资金渠道标识
    private String channel;

    // 天枢平台贷款合同号
    private String contractNo;

    // 还款流水号（用于对账和关联）
    private String seqNo;

    // 平台服务费收费单号
    private String feeNo;

    // 还款类型(01主动还款/02批量扣款/03动账通知/04线下还款/05代偿/06其他)
    private String repayType;

    // 还款日期(yyyy-MM-dd HH:mm:ss)
    private String repayDate;

    // 总还款金额
    private BigDecimal repayAmt;

    // 实还本金金额
    private BigDecimal paidPrinAmt;

    // 实还利息金额
    private BigDecimal paidIntAmt;

    // 实还担保费金额
    private BigDecimal paidGuarIntAmt;

    // 实还逾期担保费金额
    private BigDecimal paidOvdGuarIntAmt;

    // 实还逾期本金罚息金额
    private BigDecimal paidOvdPrinPnltAmt;

    // 实还逾期利息罚息金额
    private BigDecimal paidOvdIntPnltAmt;

    // 实还违约金金额
    private BigDecimal paidOvdGuarIntPnltAmt;

    // 实还违约金金额
    private BigDecimal paidBreachAmt;

    // 提前还款手续费金额
    private BigDecimal preRepayFeeAmt;

    // 平台服务费金额
    private BigDecimal feeAmt;

    // 机构需要加工的特殊字段
    private String otherData;

    // 业务类型
    private String bsnType;

    // 对账状态
    private String reccStatus;

    public String getReccId() {
        return reccId;
    }

    public void setReccId(String reccId) {
        this.reccId = reccId;
    }

    public String getSysId() {
        return sysId;
    }

    public void setSysId(String sysId) {
        this.sysId = sysId;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public String getSeqNo() {
        return seqNo;
    }

    public void setSeqNo(String seqNo) {
        this.seqNo = seqNo;
    }

    public String getFeeNo() {
        return feeNo;
    }

    public void setFeeNo(String feeNo) {
        this.feeNo = feeNo;
    }

    public String getRepayType() {
        return repayType;
    }

    public void setRepayType(String repayType) {
        this.repayType = repayType;
    }

    public String getRepayDate() {
        return repayDate;
    }

    public void setRepayDate(String repayDate) {
        this.repayDate = repayDate;
    }

    public BigDecimal getRepayAmt() {
        return repayAmt;
    }

    public void setRepayAmt(BigDecimal repayAmt) {
        this.repayAmt = repayAmt;
    }

    public BigDecimal getPaidPrinAmt() {
        return paidPrinAmt;
    }

    public void setPaidPrinAmt(BigDecimal paidPrinAmt) {
        this.paidPrinAmt = paidPrinAmt;
    }

    public BigDecimal getPaidIntAmt() {
        return paidIntAmt;
    }

    public void setPaidIntAmt(BigDecimal paidIntAmt) {
        this.paidIntAmt = paidIntAmt;
    }

    public BigDecimal getPaidGuarIntAmt() {
        return paidGuarIntAmt;
    }

    public void setPaidGuarIntAmt(BigDecimal paidGuarIntAmt) {
        this.paidGuarIntAmt = paidGuarIntAmt;
    }

    public BigDecimal getPaidOvdGuarIntAmt() {
        return paidOvdGuarIntAmt;
    }

    public void setPaidOvdGuarIntAmt(BigDecimal paidOvdGuarIntAmt) {
        this.paidOvdGuarIntAmt = paidOvdGuarIntAmt;
    }

    public BigDecimal getPaidOvdPrinPnltAmt() {
        return paidOvdPrinPnltAmt;
    }

    public void setPaidOvdPrinPnltAmt(BigDecimal paidOvdPrinPnltAmt) {
        this.paidOvdPrinPnltAmt = paidOvdPrinPnltAmt;
    }

    public BigDecimal getPaidOvdIntPnltAmt() {
        return paidOvdIntPnltAmt;
    }

    public void setPaidOvdIntPnltAmt(BigDecimal paidOvdIntPnltAmt) {
        this.paidOvdIntPnltAmt = paidOvdIntPnltAmt;
    }

    public BigDecimal getPaidOvdGuarIntPnltAmt() {
        return paidOvdGuarIntPnltAmt;
    }

    public void setPaidOvdGuarIntPnltAmt(BigDecimal paidOvdGuarIntPnltAmt) {
        this.paidOvdGuarIntPnltAmt = paidOvdGuarIntPnltAmt;
    }

    public BigDecimal getPaidBreachAmt() {
        return paidBreachAmt;
    }

    public void setPaidBreachAmt(BigDecimal paidBreachAmt) {
        this.paidBreachAmt = paidBreachAmt;
    }

    public BigDecimal getPreRepayFeeAmt() {
        return preRepayFeeAmt;
    }

    public void setPreRepayFeeAmt(BigDecimal preRepayFeeAmt) {
        this.preRepayFeeAmt = preRepayFeeAmt;
    }

    public BigDecimal getFeeAmt() {
        return feeAmt;
    }

    public void setFeeAmt(BigDecimal feeAmt) {
        this.feeAmt = feeAmt;
    }

    public String getOtherData() {
        return otherData;
    }

    public void setOtherData(String otherData) {
        this.otherData = otherData;
    }

    public String getBsnType() {
        return bsnType;
    }

    public void setBsnType(String bsnType) {
        this.bsnType = bsnType;
    }

    public String getReccStatus() {
        return reccStatus;
    }

    public void setReccStatus(String reccStatus) {
        this.reccStatus = reccStatus;
    }
}
