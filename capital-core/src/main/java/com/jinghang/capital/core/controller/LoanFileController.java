package com.jinghang.capital.core.controller;

import com.jinghang.capital.api.LoanFileService;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.file.FileDailyProcessDto;
import com.jinghang.capital.api.dto.file.FileDownloadDto;
import com.jinghang.capital.api.dto.file.FileDownloadRequestDto;
import com.jinghang.capital.api.dto.file.FileDownloadResultDto;
import com.jinghang.capital.api.dto.file.FileSynRepayPlanDto;
import com.jinghang.capital.api.dto.file.FileSynRepayPlanResultDto;
import com.jinghang.capital.api.dto.file.FileSyncDueFileDto;
import com.jinghang.capital.api.dto.file.FileSyncDueFileResultDto;
import com.jinghang.capital.api.dto.file.FileUploadDto;
import com.jinghang.capital.api.dto.file.FileUploadResultDto;
import com.jinghang.capital.api.dto.file.PreviewDto;
import com.jinghang.capital.api.dto.file.PreviewResultDto;
import com.jinghang.capital.core.convert.apivo.ApiFileConvert;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.vo.file.FileDailyProcessVo;
import com.jinghang.capital.core.vo.file.FileDownloadRequestVo;
import com.jinghang.capital.core.vo.file.FileDownloadResultVo;
import com.jinghang.capital.core.vo.file.FileDownloadVo;
import com.jinghang.capital.core.vo.file.FileSynRepayPlanResultDVo;
import com.jinghang.capital.core.vo.file.FileSynRepayPlanVo;
import com.jinghang.capital.core.vo.file.FileSyncDueFileResultVo;
import com.jinghang.capital.core.vo.file.FileSyncDueFileVo;
import com.jinghang.capital.core.vo.file.FileUploadResultVo;
import com.jinghang.capital.core.vo.file.FileUploadVo;
import com.jinghang.capital.core.vo.file.PreviewResultVo;
import com.jinghang.capital.core.vo.file.PreviewVo;
import com.jinghang.common.util.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023/5/13
 */
@RestController
@RequestMapping("file")
public class LoanFileController implements LoanFileService {

    private static final Logger logger = LoggerFactory.getLogger(LoanFileController.class);

    private final ManageService manageService;

    @Autowired
    public LoanFileController(ManageService manageService) {
        this.manageService = manageService;
    }

    @Override
    public RestResult<FileDownloadResultDto> download(FileDownloadDto fileDownloadDto) {
        logger.info("文件下载: {}", JsonUtil.toJsonString(fileDownloadDto));
        FileDownloadVo fileDownloadVo = ApiFileConvert.INSTANCE.toVo(fileDownloadDto);
        FileDownloadResultVo resultVo = manageService.fileDownload(fileDownloadVo);
        FileDownloadResultDto downloadResultDto = ApiFileConvert.INSTANCE.toDto(resultVo);
        logger.info("文件下载 result: {}", JsonUtil.toJsonString(downloadResultDto));
        return RestResult.success(downloadResultDto);
    }


    @Override
    public RestResult<FileUploadResultDto> upload(FileUploadDto fileUploadDto) {
        logger.info("文件上传: {}", fileUploadDto.toString());
        FileUploadVo fileDownloadVo = ApiFileConvert.INSTANCE.toUploadVo(fileUploadDto);
        FileUploadResultVo resultVo = manageService.fileUpload(fileDownloadVo);
        FileUploadResultDto downloadResultDto = ApiFileConvert.INSTANCE.toUploadDto(resultVo);

        return RestResult.success(downloadResultDto);
    }

    @Override
    public RestResult<Void> dailyProcess(FileDailyProcessDto processDto) {
        logger.info("文件处理dailyProcess: {}", processDto.toString());
        FileDailyProcessVo fileDailyProcessVo = ApiFileConvert.INSTANCE.toProcessVo(processDto);
        manageService.processDailyFile(fileDailyProcessVo);

        return RestResult.success(null);
    }

    @Override
    public RestResult<Void> downloadRequest(FileDownloadRequestDto fileDownloadRequestDto) {
        logger.info("文件处理: {}", fileDownloadRequestDto.toString());
        FileDownloadRequestVo fileDownloadRequestVo = ApiFileConvert.INSTANCE.toDownloadRequestVo(fileDownloadRequestDto);
        manageService.fileDownloadRequest(fileDownloadRequestVo);

        return RestResult.success(null);
    }


    @Override
    public RestResult<PreviewResultDto> preview(PreviewDto previewDto) {
        logger.info("协议预览: {}", JsonUtil.toJsonString(previewDto));
        PreviewVo previewVo = ApiFileConvert.INSTANCE.toPreviewVo(previewDto);
        PreviewResultVo resultVo = manageService.filePreview(previewVo);
        PreviewResultDto downloadResultDto = ApiFileConvert.INSTANCE.toPreviewDto(resultVo);

        return RestResult.success(downloadResultDto);
    }

    @Override
    public RestResult<FileSynRepayPlanResultDto> synRepayPlanFile(FileSynRepayPlanDto fileSynRepayPlanDto) {
        logger.info("文件同步: {}", JsonUtil.toJsonString(fileSynRepayPlanDto));
        FileSynRepayPlanVo fileSynRepayPlanVo = ApiFileConvert.INSTANCE.toSynVo(fileSynRepayPlanDto);
        FileSynRepayPlanResultDVo resultVo = manageService.synRepayPlanFile(fileSynRepayPlanVo);
        FileSynRepayPlanResultDto fileSynRepayPlanResultDto = ApiFileConvert.INSTANCE.toSynDto(resultVo);
        logger.info("文件同步 result: {}", JsonUtil.toJsonString(fileSynRepayPlanResultDto));
        return RestResult.success(fileSynRepayPlanResultDto);
    }

    @Override
    public RestResult<FileSyncDueFileResultDto> syncDueFile(FileSyncDueFileDto fileSyncDueFileDto) {
        logger.info("融担费文件同步: {}", JsonUtil.toJsonString(fileSyncDueFileDto));
        FileSyncDueFileVo fileSynRepayPlanVo = ApiFileConvert.INSTANCE.toDueVo(fileSyncDueFileDto);
        FileSyncDueFileResultVo resultVo = manageService.syncDueFile(fileSynRepayPlanVo);
        FileSyncDueFileResultDto fileSynRepayPlanResultDto = ApiFileConvert.INSTANCE.toDueDto(resultVo);
        logger.info("融担费文件同步 result: {}", JsonUtil.toJsonString(fileSynRepayPlanResultDto));
        return RestResult.success(fileSynRepayPlanResultDto);
    }

}
