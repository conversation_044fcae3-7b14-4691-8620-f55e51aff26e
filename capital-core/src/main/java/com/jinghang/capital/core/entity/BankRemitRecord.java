package com.jinghang.capital.core.entity;


import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.ProcessStatus;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 对资打款表
 */
@Entity
@Table(name = "bank_remit_record")
public class BankRemitRecord extends BaseEntity {

    /**
     * 资方渠道
     */
    @Enumerated(EnumType.STRING)
    private BankChannel channel;
    /**
     * 打款状态
     */
    @Enumerated(EnumType.STRING)
    private ProcessStatus remitStatus;
    /**
     * 应打款时间
     */
    private LocalDateTime remitTime;
    /**
     * 实际打款时间
     */
    private LocalDateTime actRemitTime;
    /**
     * 打款对象
     */
    private String remitTarget;
    /**
     * 打款对象帐号
     */
    private String remitAccount;
    /**
     * 应打款金额
     */
    private BigDecimal remitAmt;
    /**
     * 应打款笔数
     */
    private Integer remitNum;
    /**
     * 实际打款金额
     */
    private BigDecimal actAmt;
    /**
     * 本金
     */
    private BigDecimal principalAmt;
    /**
     * 利息
     */
    private BigDecimal interestAmt;
    /**
     * 罚息
     */
    private BigDecimal penaltyAmt;
    /**
     * 违约金
     */
    private BigDecimal breachAmt;
    /**
     * 还款类型
     */
    private String repayType;
    /**
     * 还款模式
     */
    private String repayMode;

    /**
     * 打款凭证bucket
     */
    private String ossBucket;
    /**
     * 打款凭证路径
     */
    private String ossPath;


    public BankChannel getChannel() {
        return channel;
    }

    public void setChannel(BankChannel channel) {
        this.channel = channel;
    }

    public ProcessStatus getRemitStatus() {
        return remitStatus;
    }

    public void setRemitStatus(ProcessStatus remitStatus) {
        this.remitStatus = remitStatus;
    }

    public LocalDateTime getRemitTime() {
        return remitTime;
    }

    public void setRemitTime(LocalDateTime remitTime) {
        this.remitTime = remitTime;
    }

    public LocalDateTime getActRemitTime() {
        return actRemitTime;
    }

    public void setActRemitTime(LocalDateTime actRemitTime) {
        this.actRemitTime = actRemitTime;
    }

    public String getRemitTarget() {
        return remitTarget;
    }

    public void setRemitTarget(String remitTarget) {
        this.remitTarget = remitTarget;
    }

    public String getRemitAccount() {
        return remitAccount;
    }

    public void setRemitAccount(String remitAccount) {
        this.remitAccount = remitAccount;
    }

    public BigDecimal getRemitAmt() {
        return remitAmt;
    }

    public void setRemitAmt(BigDecimal remitAmt) {
        this.remitAmt = remitAmt;
    }

    public Integer getRemitNum() {
        return remitNum;
    }

    public void setRemitNum(Integer remitNum) {
        this.remitNum = remitNum;
    }

    public BigDecimal getActAmt() {
        return actAmt;
    }

    public void setActAmt(BigDecimal actAmt) {
        this.actAmt = actAmt;
    }

    public BigDecimal getPrincipalAmt() {
        return principalAmt;
    }

    public void setPrincipalAmt(BigDecimal principalAmt) {
        this.principalAmt = principalAmt;
    }

    public BigDecimal getInterestAmt() {
        return interestAmt;
    }

    public void setInterestAmt(BigDecimal interestAmt) {
        this.interestAmt = interestAmt;
    }

    public BigDecimal getPenaltyAmt() {
        return penaltyAmt;
    }

    public void setPenaltyAmt(BigDecimal penaltyAmt) {
        this.penaltyAmt = penaltyAmt;
    }

    public BigDecimal getBreachAmt() {
        return breachAmt;
    }

    public void setBreachAmt(BigDecimal breachAmt) {
        this.breachAmt = breachAmt;
    }

    public String getRepayType() {
        return repayType;
    }

    public void setRepayType(String repayType) {
        this.repayType = repayType;
    }

    public String getRepayMode() {
        return repayMode;
    }

    public void setRepayMode(String repayMode) {
        this.repayMode = repayMode;
    }

    public String getOssBucket() {
        return ossBucket;
    }

    public void setOssBucket(String ossBucket) {
        this.ossBucket = ossBucket;
    }

    public String getOssPath() {
        return ossPath;
    }

    public void setOssPath(String ossPath) {
        this.ossPath = ossPath;
    }

    @Override
    public String prefix() {
        return "BRE";
    }

}
