package com.jinghang.capital.core.repository;

import com.jinghang.capital.core.entity.HXBKAntFileLog;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * HXBK蚂蚁文件日志Repository
 *
 * @Author: System
 * @CreateTime: 2025/7/11
 */
public interface HXBKAntFileLogRepository extends JpaRepository<HXBKAntFileLog, String> {

    /**
     * 根据授信ID查找文件日志
     *
     * @param creditId 授信ID
     * @return 文件日志列表
     */
    List<HXBKAntFileLog> findByCreditId(String creditId);

    /**
     * 根据授信ID和文件名查找文件日志
     *
     * @param creditId 授信ID
     * @param fileName 文件名
     * @return 文件日志
     */
    Optional<HXBKAntFileLog> findByCreditIdAndFileName(String creditId, String fileName);

    /**
     * 根据文件路径查找文件日志
     *
     * @param filePath 文件路径
     * @return 文件日志
     */
    Optional<HXBKAntFileLog> findByFilePath(String filePath);
}
