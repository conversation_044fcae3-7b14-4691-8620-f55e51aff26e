package com.jinghang.capital.core.enums;

/**
 * <AUTHOR>
 * @description: OperateTypeEnum
 * @date 2024/8/16 14:55
 */
public enum OperateTypeEnum {
    //操作类型;ADD-增加;SUB-减少
    ADD("ADD", "增加"),
    SUB("SUB", "减少");
    private final String code;
    private final String msg;

    OperateTypeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static OperateTypeEnum getByCode(String code) {
        for (OperateTypeEnum operateTypeEnum : OperateTypeEnum.values()) {
            if (operateTypeEnum.getCode().equals(code)) {
                return operateTypeEnum;
            }
        }
        return null;
    }

    public static String getMsgByCode(String code) {
        for (OperateTypeEnum operateTypeEnum : OperateTypeEnum.values()) {
            if (operateTypeEnum.getCode().equals(code)) {
                return operateTypeEnum.getMsg();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
