package com.jinghang.capital.core.convert.apivo;

import com.jinghang.capital.api.dto.repay.ActiveClaimApplyDto;
import com.jinghang.capital.api.dto.repay.ActiveClaimApplyResultDto;
import com.jinghang.capital.api.dto.repay.ActiveClaimQueryDto;
import com.jinghang.capital.api.dto.repay.ActiveClaimQueryResultDto;
import com.jinghang.capital.core.vo.repay.ActiveClaimApplyResultVo;
import com.jinghang.capital.core.vo.repay.ActiveClaimApplyVo;
import com.jinghang.capital.core.vo.repay.ActiveClaimQueryResultVo;
import com.jinghang.capital.core.vo.repay.ActiveClaimQueryVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ActiveClaimConvert {

    ActiveClaimConvert INSTANCE = Mappers.getMapper(ActiveClaimConvert.class);

    ActiveClaimApplyVo toApplyVo(ActiveClaimApplyDto dto);

    ActiveClaimApplyResultDto toApplyResultDto(ActiveClaimApplyResultVo result);

    ActiveClaimQueryVo toQueryVo(ActiveClaimQueryDto dto);

    ActiveClaimQueryResultDto toQueryResultDto(ActiveClaimQueryResultVo result);
}
