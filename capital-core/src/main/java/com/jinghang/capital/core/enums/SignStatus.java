package com.jinghang.capital.core.enums;

/**
 * <AUTHOR>
 */
public enum SignStatus {

    Y("1", "已签约"),

    N("0", "未签约");


    private final String code;

    private final String desc;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    SignStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
