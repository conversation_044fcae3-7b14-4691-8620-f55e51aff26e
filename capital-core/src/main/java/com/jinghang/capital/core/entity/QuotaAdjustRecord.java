package com.jinghang.capital.core.entity;

import com.jinghang.capital.api.dto.QuotaStage;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.ProcessStatus;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.math.BigDecimal;

/**
 * 循环额度调额记录表;
 *
 * <AUTHOR> http://www.chiner.pro
 * @date : 2024-12-25
 */
@Entity
@Table(name = "quota_adjust_record")
public class QuotaAdjustRecord extends BaseEntity {

    /**
     * 循环额度用户ID
     */
    private String cycleUserId;
    /**
     * 资方渠道
     */
    @Enumerated(EnumType.STRING)
    private BankChannel channel;
    /**
     * 用户身份证
     */
    private String userIdCard;
    /**
     * 调额阶段
     */
    @Enumerated(EnumType.STRING)
    private QuotaStage adjustStage;
    /**
     * 阶段业务ID
     */
    private String businessId;
    /**
     * 调额状态
     */
    @Enumerated(EnumType.STRING)
    private ProcessStatus status;
    /**
     * 授信申请流水号
     */
    private String creditSeq;
    /**
     * 资方授信流水号
     */
    private String bankCreditSeq;
    /**
     * 资方授信编号
     */
    private String creditNo;
    /**
     * 资方客户编号
     */
    private String bankUserId;
    /**
     * 调额前查询额度
     */
    private BigDecimal queryQuotaAmt;
    /**
     * 调额前查询已用额度
     */
    private BigDecimal queryUsedQuotaAmt;
    /**
     * 调额前查询可用额度
     */
    private BigDecimal queryBalanceQuotaAmt;
    /**
     * 本次调额需要额度
     */
    private BigDecimal needQuotaAmt;
    /**
     * 调额申请流水号
     */
    private String quotaAdjustSeq;
    /**
     * 资方调额流水号
     */
    private String bankQuotaAdjustSeq;

    /**
     * 调额申请批次号
     */
    private String adjustNo;

    public String getAdjustNo() {
        return adjustNo;
    }

    public void setAdjustNo(String adjustNo) {
        this.adjustNo = adjustNo;
    }

    public String getCycleUserId() {
        return cycleUserId;
    }

    public void setCycleUserId(String cycleUserId) {
        this.cycleUserId = cycleUserId;
    }

    public BankChannel getChannel() {
        return channel;
    }

    public void setChannel(BankChannel channel) {
        this.channel = channel;
    }

    public String getUserIdCard() {
        return userIdCard;
    }

    public void setUserIdCard(String userIdCard) {
        this.userIdCard = userIdCard;
    }

    public QuotaStage getAdjustStage() {
        return adjustStage;
    }

    public void setAdjustStage(QuotaStage adjustStage) {
        this.adjustStage = adjustStage;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public ProcessStatus getStatus() {
        return status;
    }

    public void setStatus(ProcessStatus status) {
        this.status = status;
    }

    public String getCreditSeq() {
        return creditSeq;
    }

    public void setCreditSeq(String creditSeq) {
        this.creditSeq = creditSeq;
    }

    public String getBankCreditSeq() {
        return bankCreditSeq;
    }

    public void setBankCreditSeq(String bankCreditSeq) {
        this.bankCreditSeq = bankCreditSeq;
    }

    public String getCreditNo() {
        return creditNo;
    }

    public void setCreditNo(String creditNo) {
        this.creditNo = creditNo;
    }

    public String getBankUserId() {
        return bankUserId;
    }

    public void setBankUserId(String bankUserId) {
        this.bankUserId = bankUserId;
    }

    public BigDecimal getQueryQuotaAmt() {
        return queryQuotaAmt;
    }

    public void setQueryQuotaAmt(BigDecimal queryQuotaAmt) {
        this.queryQuotaAmt = queryQuotaAmt;
    }

    public BigDecimal getQueryUsedQuotaAmt() {
        return queryUsedQuotaAmt;
    }

    public void setQueryUsedQuotaAmt(BigDecimal queryUsedQuotaAmt) {
        this.queryUsedQuotaAmt = queryUsedQuotaAmt;
    }

    public BigDecimal getQueryBalanceQuotaAmt() {
        return queryBalanceQuotaAmt;
    }

    public void setQueryBalanceQuotaAmt(BigDecimal queryBalanceQuotaAmt) {
        this.queryBalanceQuotaAmt = queryBalanceQuotaAmt;
    }

    public BigDecimal getNeedQuotaAmt() {
        return needQuotaAmt;
    }

    public void setNeedQuotaAmt(BigDecimal needQuotaAmt) {
        this.needQuotaAmt = needQuotaAmt;
    }

    public String getQuotaAdjustSeq() {
        return quotaAdjustSeq;
    }

    public void setQuotaAdjustSeq(String quotaAdjustSeq) {
        this.quotaAdjustSeq = quotaAdjustSeq;
    }

    public String getBankQuotaAdjustSeq() {
        return bankQuotaAdjustSeq;
    }

    public void setBankQuotaAdjustSeq(String bankQuotaAdjustSeq) {
        this.bankQuotaAdjustSeq = bankQuotaAdjustSeq;
    }

    protected String prefix() {
        return "QAR";
    }
}
