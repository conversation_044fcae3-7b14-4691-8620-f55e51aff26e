package com.jinghang.capital.core.entity;

import com.jinghang.capital.core.enums.RepayStatus;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 还款计划表
 */
@Entity
@Table(name = "loan_replan")
public class LoanReplan extends BaseEntity {

    @Serial
    private static final long serialVersionUID = -3685277705963617041L;

    /**
     * 借款id
     */
    private String loanId;
    /**
     * 资方渠道
     */
    private String channel;
    /**
     * 期数
     */
    private Integer period;
    /**
     * 应还时间
     */
    private LocalDate repayDate;
    /**
     * 应还总金额
     */
    private BigDecimal totalAmt;
    /**
     * 应还本金
     */
    private BigDecimal principalAmt;
    /**
     * 应还利息
     */
    private BigDecimal interestAmt;
    /**
     * 应还罚息
     */
    private BigDecimal penaltyAmt;
    /**
     * 应还违约金
     */
    private BigDecimal breachAmt;
    /**
     * 融担费用
     */
    private BigDecimal guaranteeAmt;

    /**
     * 咨询费用
     */
    private BigDecimal consultAmt;


    /**
     * 对客还款状态;current, clear
     */
    @Enumerated(EnumType.STRING)
    private RepayStatus custRepayStatus;
    /**
     * 对资还款状态
     */
    @Enumerated(EnumType.STRING)
    private RepayStatus bankRepayStatus;


    /**
     * 借款id
     */
    public String getLoanId() {
        return this.loanId;
    }

    /**
     * 借款id
     */
    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    /**
     * 资方渠道
     */
    public String getChannel() {
        return this.channel;
    }

    /**
     * 资方渠道
     */
    public void setChannel(String channel) {
        this.channel = channel;
    }

    /**
     * 期数
     */
    public Integer getPeriod() {
        return this.period;
    }

    /**
     * 期数
     */
    public void setPeriod(Integer period) {
        this.period = period;
    }

    /**
     * 应还时间
     */
    public LocalDate getRepayDate() {
        return this.repayDate;
    }

    /**
     * 应还时间
     */
    public void setRepayDate(LocalDate repayDate) {
        this.repayDate = repayDate;
    }

    /**
     * 应还总金额
     */
    public BigDecimal getTotalAmt() {
        return this.totalAmt;
    }

    /**
     * 应还总金额
     */
    public void setTotalAmt(BigDecimal totalAmt) {
        this.totalAmt = totalAmt;
    }

    /**
     * 应还本金
     */
    public BigDecimal getPrincipalAmt() {
        return this.principalAmt;
    }

    /**
     * 应还本金
     */
    public void setPrincipalAmt(BigDecimal principalAmt) {
        this.principalAmt = principalAmt;
    }

    /**
     * 应还利息
     */
    public BigDecimal getInterestAmt() {
        return this.interestAmt;
    }

    /**
     * 应还利息
     */
    public void setInterestAmt(BigDecimal interestAmt) {
        this.interestAmt = interestAmt;
    }

    /**
     * 应还罚息
     */
    public BigDecimal getPenaltyAmt() {
        return this.penaltyAmt;
    }

    /**
     * 应还罚息
     */
    public void setPenaltyAmt(BigDecimal penaltyAmt) {
        this.penaltyAmt = penaltyAmt;
    }

    /**
     * 应还违约金
     */
    public BigDecimal getBreachAmt() {
        return this.breachAmt;
    }

    /**
     * 应还违约金
     */
    public void setBreachAmt(BigDecimal breachAmt) {
        this.breachAmt = breachAmt;
    }

    /**
     * 融担费用
     */
    public BigDecimal getGuaranteeAmt() {
        return this.guaranteeAmt;
    }

    /**
     * 融担费用
     */
    public void setGuaranteeAmt(BigDecimal guaranteeAmt) {
        this.guaranteeAmt = guaranteeAmt;
    }

    public BigDecimal getConsultAmt() {
        return consultAmt;
    }

    public void setConsultAmt(BigDecimal consultAmt) {
        this.consultAmt = consultAmt;
    }

    /**
     * 对客还款状态
     */
    public RepayStatus getCustRepayStatus() {
        return this.custRepayStatus;
    }

    /**
     * 对客还款状态
     */
    public void setCustRepayStatus(RepayStatus custRepayStatus) {
        this.custRepayStatus = custRepayStatus;
    }

    /**
     * 对资还款状态
     */
    public RepayStatus getBankRepayStatus() {
        return this.bankRepayStatus;
    }

    /**
     * 对资还款状态
     */
    public void setBankRepayStatus(RepayStatus bankRepayStatus) {
        this.bankRepayStatus = bankRepayStatus;
    }

    @Override
    public String prefix() {
        return "LR";
    }
}
