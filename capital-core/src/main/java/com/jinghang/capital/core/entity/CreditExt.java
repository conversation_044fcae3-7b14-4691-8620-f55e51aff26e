package com.jinghang.capital.core.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/8/23
 */
@Entity
@Table(name = "credit_ext")
public class CreditExt extends BaseEntity {

    /**
     * 可用授信
     */
    private BigDecimal availableCreditAmt;
    /**
     * 在贷余额
     */
    private BigDecimal onLoanAmt;


    public BigDecimal getAvailableCreditAmt() {
        return availableCreditAmt;
    }

    public void setAvailableCreditAmt(BigDecimal availableCreditAmt) {
        this.availableCreditAmt = availableCreditAmt;
    }

    public BigDecimal getOnLoanAmt() {
        return onLoanAmt;
    }

    public void setOnLoanAmt(BigDecimal onLoanAmt) {
        this.onLoanAmt = onLoanAmt;
    }
}
