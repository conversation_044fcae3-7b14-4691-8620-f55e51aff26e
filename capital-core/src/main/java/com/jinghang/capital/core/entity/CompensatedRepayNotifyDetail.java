package com.jinghang.capital.core.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/5/11
 */
@Entity
@Table(name = "compensated_repay_notify_detail")
public class CompensatedRepayNotifyDetail extends BaseEntity {
    /**
     * 通知记录ID
     */
    private String repayNotifyId;
    /**
     * 单据
     */
    private String loanId;
    /**
     * 外部单据
     */
    private String outerLoanId;
    /**
     * 期次
     */
    private Integer period;
    /**
     * 本金
     */
    private BigDecimal principalAmt;
    /**
     * 利息
     */
    private BigDecimal interestAmt;
    /**
     * 担保费
     */
    private BigDecimal guaranteeAmt;
    /**
     * 罚息
     */
    private BigDecimal penaltyAmt;
    /**
     * 总额
     */
    private BigDecimal amount;

    public String getRepayNotifyId() {
        return repayNotifyId;
    }

    public void setRepayNotifyId(String repayNotifyId) {
        this.repayNotifyId = repayNotifyId;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public String getOuterLoanId() {
        return outerLoanId;
    }

    public void setOuterLoanId(String outerLoanId) {
        this.outerLoanId = outerLoanId;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public BigDecimal getPrincipalAmt() {
        return principalAmt;
    }

    public void setPrincipalAmt(BigDecimal principalAmt) {
        this.principalAmt = principalAmt;
    }

    public BigDecimal getInterestAmt() {
        return interestAmt;
    }

    public void setInterestAmt(BigDecimal interestAmt) {
        this.interestAmt = interestAmt;
    }

    public BigDecimal getGuaranteeAmt() {
        return guaranteeAmt;
    }

    public void setGuaranteeAmt(BigDecimal guaranteeAmt) {
        this.guaranteeAmt = guaranteeAmt;
    }

    public BigDecimal getPenaltyAmt() {
        return penaltyAmt;
    }

    public void setPenaltyAmt(BigDecimal penaltyAmt) {
        this.penaltyAmt = penaltyAmt;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }
}
