package com.jinghang.capital.core.remote.manage;

import com.jinghang.cash.api.ProjectAgreementApiService;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 项目协议管理服务Feign客户端
 * 用于Capital模块调用Cash-Manage模块的项目协议管理接口
 *
 * @Author: Lior
 * @CreateTime: 2025/8/25 14:15
 */
@FeignClient(name = "cash-manage", contextId = "projectAgreement", path = "/api/projectAgreement")
public interface ProjectAgreementFeign extends ProjectAgreementApiService {
}
