package com.jinghang.capital.core.convert.apivo;


import com.jinghang.capital.api.dto.loan.LoanApplyDto;
import com.jinghang.capital.api.dto.loan.LoanQueryDto;
import com.jinghang.capital.api.dto.loan.LoanResultDto;
import com.jinghang.capital.core.convert.StatusConvert;
import com.jinghang.capital.core.vo.loan.LoanApplyVo;
import com.jinghang.capital.core.vo.loan.LoanQueryVo;
import com.jinghang.capital.core.vo.loan.LoanResultVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * api 和  vo 的转换器
 */
@Mapper(uses = {StatusConvert.class, FileInfoConvert.class})
public interface ApiLoanConvert {
    ApiLoanConvert INSTANCE = Mappers.getMapper(ApiLoanConvert.class);


    @Mapping(source = "fileInfoDtoList", target = "fileInfoVoList")
    LoanApplyVo toLoanApplyVo(LoanApplyDto apply);


    LoanQueryVo toLoanQueryVo(LoanQueryDto query);

    @Mapping(source = "period", target = "periods")
    LoanResultDto toLoanResultDto(LoanResultVo result);


}
