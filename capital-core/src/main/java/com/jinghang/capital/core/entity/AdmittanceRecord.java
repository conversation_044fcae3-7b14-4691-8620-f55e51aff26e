package com.jinghang.capital.core.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;

/**
 * <AUTHOR>
 * @date 2023/8/23
 */
@Entity
@Table(name = "admittance_record")
public class AdmittanceRecord extends BaseEntity {

    /**
     * 外部id
     */
    private String sysId;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 身份证
     */
    private String idNo;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 设备号
     */
    private String deviceId;
    /**
     * 终端类型
     */
    private String terminal;
    /**
     * GPS 解析省市
     */
    private String gps;
    /**
     * ip地址
     */
    private String ipAddress;
    /**
     * 准入状态
     */
    //    @Enumerated(EnumType.STRING)
    //    private LhrlCheckState checkStatus;

    /**
     * 可用渠道标识
     */
    private String channels;
    /**
     * creditId
     */
    private String creditId;
    /**
     * 失败原因
     */
    private String failReason;

    public String getSysId() {
        return sysId;
    }

    public void setSysId(String sysId) {
        this.sysId = sysId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getTerminal() {
        return terminal;
    }

    public void setTerminal(String terminal) {
        this.terminal = terminal;
    }

    public String getGps() {
        return gps;
    }

    public void setGps(String gps) {
        this.gps = gps;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    //    public LhrlCheckState getCheckStatus() {
    //        return checkStatus;
    //    }
    //
    //    public void setCheckStatus(LhrlCheckState checkStatus) {
    //        this.checkStatus = checkStatus;
    //    }

    public String getChannels() {
        return channels;
    }

    public void setChannels(String channels) {
        this.channels = channels;
    }

    public String getCreditId() {
        return creditId;
    }

    public void setCreditId(String creditId) {
        this.creditId = creditId;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    @Override
    protected String prefix() {
        return "AR";
    }
}
