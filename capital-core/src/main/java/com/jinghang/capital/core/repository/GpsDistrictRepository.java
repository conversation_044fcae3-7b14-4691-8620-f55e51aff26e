package com.jinghang.capital.core.repository;

import com.jinghang.capital.core.entity.GpsDistrict;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface GpsDistrictRepository extends JpaRepository<GpsDistrict, Integer> {
    List<GpsDistrict> findByCityCode(String cityCode);

    Optional<GpsDistrict> findByCityCodeAndDistrictSeqNum(String cityCode, Integer seqNo);

    @Query(value = "SELECT * FROM gps_district WHERE district_seq_num is NULL", nativeQuery = true)
    List<GpsDistrict> findByDistrictSeqNum();

    GpsDistrict findByDistrictCode(String districtCode);

    List<GpsDistrict> findByProvinceCode(String provinceCode);

    /**
     * 查询市范围的GPS
     *
     * @param cityCodes
     * @return
     */
    List<GpsDistrict> findByCityCodeIn(List<String> cityCodes);

    /**
     * 查询区范围的GPS
     *
     * @param districtCodes
     * @return
     */
    List<GpsDistrict> findByDistrictCodeIn(List<String> districtCodes);


    List<GpsDistrict> findByProvinceCodeAndCityCodeNotIn(String provinceCode, List<String> cityCodes);
}
