package com.jinghang.capital.core.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "gps_district")
public class GpsDistrict implements Serializable {
    @Serial
    private static final long serialVersionUID = -2512129731335414235L;
    /**
     * 自增主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    /**
     * 省
     */
    private String provinceCode;
    /**
     * 市
     */
    private String cityCode;
    /**
     * 区
     */
    private String districtCode;
    /**
     * 区名
     */
    private String districtName;
    /**
     * 自定义区序号
     */
    private Integer districtSeqNum;
    /**
     * 区域最小经度
     */
    private BigDecimal longitudeMin;
    /**
     * 区域最大经度
     */
    private BigDecimal longitudeMax;
    /**
     * 区域最小纬度
     */
    private BigDecimal latitudeMin;
    /**
     * 区域最大纬度
     */
    private BigDecimal latitudeMax;
    /**
     * 备注
     */
    private String remark;
    /**
     * 乐观锁
     */
    private Integer revision;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getDistrictCode() {
        return districtCode;
    }

    public void setDistrictCode(String districtCode) {
        this.districtCode = districtCode;
    }

    public String getDistrictName() {
        return districtName;
    }

    public void setDistrictName(String districtName) {
        this.districtName = districtName;
    }

    public Integer getDistrictSeqNum() {
        return districtSeqNum;
    }

    public void setDistrictSeqNum(Integer districtSeqNum) {
        this.districtSeqNum = districtSeqNum;
    }

    public BigDecimal getLongitudeMin() {
        return longitudeMin;
    }

    public void setLongitudeMin(BigDecimal longitudeMin) {
        this.longitudeMin = longitudeMin;
    }

    public BigDecimal getLongitudeMax() {
        return longitudeMax;
    }

    public void setLongitudeMax(BigDecimal longitudeMax) {
        this.longitudeMax = longitudeMax;
    }

    public BigDecimal getLatitudeMin() {
        return latitudeMin;
    }

    public void setLatitudeMin(BigDecimal latitudeMin) {
        this.latitudeMin = latitudeMin;
    }

    public BigDecimal getLatitudeMax() {
        return latitudeMax;
    }

    public void setLatitudeMax(BigDecimal latitudeMax) {
        this.latitudeMax = latitudeMax;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }
}
