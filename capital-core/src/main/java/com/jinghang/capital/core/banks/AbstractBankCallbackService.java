package com.jinghang.capital.core.banks;

public abstract class AbstractBankCallbackService implements BankCallbackService {

    @Override
    public String creditResultCallback(String json) {
        return "";
    }

    @Override
    public String loanResultCallback(String json) {
        return "";
    }

    @Override
    public String quotaAdjustResultCallback(String json) {
        return "";
    }

    @Override
    public String repayResultCallback(String json) {
        return "";
    }

    @Override
    public String rccResultCallback(String json) {
        return "";
    }

}
