package com.jinghang.capital.core.repository;

import com.jinghang.capital.core.entity.Credit;
import com.jinghang.capital.core.entity.HXBKCreditFlow;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * HXBK授信流水Repository
 *
 * @Author: Lior
 * @CreateTime: 2025/7/9 11:00
 */
public interface HXBKCreditFlowRepository extends JpaRepository<HXBKCreditFlow, String> {

    /**
     * 根据授信ID查找授信流水
     *
     * @param creditId 授信ID
     * @return 授信流水
     */
    Optional<HXBKCreditFlow> findByCreditId(String creditId);

    /**
     * 根据授信编号查找授信流水
     *
     * @param creditNo 授信编号
     * @return 授信流水
     */
    Optional<HXBKCreditFlow> findByCreditNo(String creditNo);

    /**
     * 根据客户编号查找授信流水
     *
     * @param customNo 客户编号
     * @return 授信流水
     */
    Optional<HXBKCreditFlow> findByCustomNo(String customNo);

    /**
     * 根据客户编号联查授信记录
     *
     * @param customNo 客户编号
     * @return 授信记录
     */
    @Query("SELECT c FROM Credit c JOIN HXBKCreditFlow hcf ON c.id = hcf.creditId WHERE hcf.customNo = :customNo")
    Optional<Credit> findCreditByCustomNo(@Param("customNo") String customNo);

}
