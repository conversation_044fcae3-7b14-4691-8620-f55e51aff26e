package com.jinghang.capital.core.service;


import com.jinghang.capital.core.entity.CommonIpAddress;
import com.jinghang.capital.core.repository.CommonIpAddressRepository;
import java.security.SecureRandom;
import java.util.List;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class CommonIpService {
    private static final Logger logger = LoggerFactory.getLogger(CommonIpService.class);


    private static final String SHAN_DONG_PROVINCE_CODE = "370000";

    private static final String LIAO_NING_PROVINCE_CODE = "210000";

    private static final SecureRandom SECURE_RANDOM = new SecureRandom();

    public static final int INT_MASK = 0xFF;
    public static final long THREE_BYTE_LENGTH = 24L;
    public static final long TWO_BYTE_LENGTH = 16L;
    public static final long ONE_BYTE_LENGTH = 8L;

    private CommonIpAddressRepository commonIpAddressRepository;

    public CommonIpService(CommonIpAddressRepository commonIpAddressRepository) {
        this.commonIpAddressRepository = commonIpAddressRepository;
    }

    /**
     * 获取山东的ip
     *
     * @return ip地址
     */
    public String genShanDongIp() {
        return generateRandomIp(SHAN_DONG_PROVINCE_CODE);
    }

    /**
     * 获取辽宁的ip
     *
     * @return ip地址
     */
    public String genLiaoNingIp() {
        return generateRandomIp(LIAO_NING_PROVINCE_CODE);
    }

    private String generateRandomIp(String provinceCode) {
        List<Long> ipIds = commonIpAddressRepository.findIdsByProvinceCode(provinceCode);
        String resultIp = "";
        if (ipIds.isEmpty()) {
            return resultIp;
        }

        int size = ipIds.size();
        int i = SECURE_RANDOM.nextInt(size);

        Long itemId = ipIds.get(i);


        Optional<CommonIpAddress> ipOption = commonIpAddressRepository.findById(itemId);
        if (ipOption.isPresent()) {
            CommonIpAddress ip = ipOption.get();
            Long minIpInt = ip.getMinIpInt();
            Long maxIpInt = ip.getMaxIpInt();
            //如果最大IP值和最小IP值一样的情况下，nextLong方法会报错，所以这种场景直接返回库里的IP值
            long targetIntIp = 0;
            if (minIpInt.equals(maxIpInt)) {
                targetIntIp = minIpInt;
            } else {
                targetIntIp = SECURE_RANDOM.nextLong(minIpInt, maxIpInt);
            }
            resultIp = toStringIP(targetIntIp);
        }

        return resultIp;
    }

    private String toStringIP(final long numberIp) {
        return (numberIp >> THREE_BYTE_LENGTH & INT_MASK) + "."
                + (numberIp >> TWO_BYTE_LENGTH & INT_MASK) + "."
                + (numberIp >> ONE_BYTE_LENGTH & INT_MASK) + "."
                + (numberIp & INT_MASK);
    }
}
