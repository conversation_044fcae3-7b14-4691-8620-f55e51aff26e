package com.jinghang.capital.core.entity;


import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * cz数据ip地址表
 *
 */
@Entity
@Table(name = "common_ip_address")
public class CommonIpAddress implements Serializable {


    @Serial
    private static final long serialVersionUID = -54088528671149855L;

    @Id
    private Long id;

    /**
     * ip段最小值（数字）
     */
    private Long minIpInt;

    private Long maxIpInt;

    private String minIp;

    private String maxIp;

    private String provinceCode;

    private String mainInfo;

    private String subInfo;

    /**
     * 备注
     */
    private String remark;
    /**
     * 乐观锁
     */
    @Version
    private Integer revision;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;


    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public Long getMinIpInt() {
        return minIpInt;
    }

    public void setMinIpInt(Long minIpInt) {
        this.minIpInt = minIpInt;
    }

    public Long getMaxIpInt() {
        return maxIpInt;
    }

    public void setMaxIpInt(Long maxIpInt) {
        this.maxIpInt = maxIpInt;
    }

    public String getMinIp() {
        return minIp;
    }

    public void setMinIp(String minIp) {
        this.minIp = minIp;
    }

    public String getMaxIp() {
        return maxIp;
    }

    public void setMaxIp(String maxIp) {
        this.maxIp = maxIp;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getMainInfo() {
        return mainInfo;
    }

    public void setMainInfo(String mainInfo) {
        this.mainInfo = mainInfo;
    }

    public String getSubInfo() {
        return subInfo;
    }

    public void setSubInfo(String subInfo) {
        this.subInfo = subInfo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }
}
