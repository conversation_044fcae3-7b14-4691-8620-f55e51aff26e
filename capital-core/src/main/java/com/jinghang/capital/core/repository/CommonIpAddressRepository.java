package com.jinghang.capital.core.repository;


import com.jinghang.capital.core.entity.CommonIpAddress;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface CommonIpAddressRepository extends JpaRepository<CommonIpAddress, Long> {

    @Query(value = "select l.id from CommonIpAddress l where l.provinceCode = ?1")
    List<Long> findIdsByProvinceCode(String provinceCode);

}
