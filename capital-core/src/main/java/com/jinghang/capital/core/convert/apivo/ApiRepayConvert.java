package com.jinghang.capital.core.convert.apivo;


import com.jinghang.capital.api.dto.repay.BatchTrailDto;
import com.jinghang.capital.api.dto.repay.BatchTrialResultDto;
import com.jinghang.capital.api.dto.repay.CompensatedRepaySyncDto;
import com.jinghang.capital.api.dto.repay.CompensatedRepaySyncRltDto;
import com.jinghang.capital.api.dto.repay.DefrayDto;
import com.jinghang.capital.api.dto.repay.DefrayResultDto;
import com.jinghang.capital.api.dto.repay.Payee;
import com.jinghang.capital.api.dto.repay.RepayApplyDto;
import com.jinghang.capital.api.dto.repay.RepayBatchResultDto;
import com.jinghang.capital.api.dto.repay.RepayDeductionApplyDto;
import com.jinghang.capital.api.dto.repay.RepayQueryDto;
import com.jinghang.capital.api.dto.repay.RepayResultDto;
import com.jinghang.capital.api.dto.repay.RepayReturnUploadDto;
import com.jinghang.capital.api.dto.repay.RepayTrailDto;
import com.jinghang.capital.api.dto.repay.SubstituteApplyDto;
import com.jinghang.capital.api.dto.repay.SubstituteApplyResultDto;
import com.jinghang.capital.api.dto.repay.SubstituteMarkApplyDto;
import com.jinghang.capital.api.dto.repay.SubstituteMarkResultDto;
import com.jinghang.capital.api.dto.repay.TrailResultDto;
import com.jinghang.capital.core.convert.StatusConvert;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.vo.repay.BatchTrailVo;
import com.jinghang.capital.core.vo.repay.BatchTrialResultVo;
import com.jinghang.capital.core.vo.repay.CompensatedRepaySyncRlt;
import com.jinghang.capital.core.vo.repay.CompensatedRepaySyncVo;
import com.jinghang.capital.core.vo.repay.DefrayResultVo;
import com.jinghang.capital.core.vo.repay.DefrayVo;
import com.jinghang.capital.core.vo.repay.RepayApplyVo;
import com.jinghang.capital.core.vo.repay.RepayBatchResultVo;
import com.jinghang.capital.core.vo.repay.RepayDeductionApplyVo;
import com.jinghang.capital.core.vo.repay.RepayQueryVo;
import com.jinghang.capital.core.vo.repay.RepayResultVo;
import com.jinghang.capital.core.vo.repay.RepayReturnUploadVo;
import com.jinghang.capital.core.vo.repay.RepayTrailVo;
import com.jinghang.capital.core.vo.repay.SubstituteApplyResultVo;
import com.jinghang.capital.core.vo.repay.SubstituteApplyVo;
import com.jinghang.capital.core.vo.repay.SubstituteMarkApplyVo;
import com.jinghang.capital.core.vo.repay.SubstituteMarkResultVo;
import com.jinghang.capital.core.vo.repay.TrailResultVo;
import org.mapstruct.Mapper;
import org.mapstruct.ValueMapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/5/20
 */
@Mapper(uses = {StatusConvert.class})
public interface ApiRepayConvert {
    ApiRepayConvert INSTANCE = Mappers.getMapper(ApiRepayConvert.class);

    RepayTrailVo toVo(RepayTrailDto trailDto);

    RepayApplyVo toVo(RepayApplyDto applyDto);

    RepayQueryVo toVo(RepayQueryDto queryDto);

    BatchTrailVo toVo(BatchTrailDto trailDto);

    RepayDeductionApplyVo toVo(RepayDeductionApplyDto applyDto);

    CompensatedRepaySyncVo toVo(CompensatedRepaySyncDto compensatedRepaySyncDto);

    DefrayVo toVo(DefrayDto defrayDto);

    SubstituteMarkApplyVo toVo(SubstituteMarkApplyDto applyDto);

    SubstituteApplyVo toVo(SubstituteApplyDto applyDto);


    RepayReturnUploadVo toVo(RepayReturnUploadDto dto);

    TrailResultDto toDto(TrailResultVo trailResultVo);

    RepayResultDto toDto(RepayResultVo repayResultVo);

    RepayBatchResultDto toDto(RepayBatchResultVo repayBatchResultVo);

    CompensatedRepaySyncRltDto toDto(CompensatedRepaySyncRlt compensatedRepaySyncRlt);

    BatchTrialResultDto toDto(BatchTrialResultVo batchTrialResultVo);

    DefrayResultDto toDto(DefrayResultVo defrayResultVo);

    SubstituteMarkResultDto toDto(SubstituteMarkResultVo substituteMarkResultVo);

    SubstituteApplyResultDto toDto(SubstituteApplyResultVo substituteApplyResultVo);


    Payee toPayeeDto(Payee payee);


    @ValueMapping(source = "P", target = "PROCESSING")
    @ValueMapping(source = "S", target = "PROCESSING")
    @ValueMapping(source = "F", target = "PROCESSING")
    @ValueMapping(source = "R", target = "PROCESSING")
    @ValueMapping(source = "U", target = "PROCESSING")
    ProcessStatus map(com.jinghang.capital.api.dto.ProcessStatus status);

}
