package com.jinghang.capital.core.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <p>
 * 长银直连代偿对账文件
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-21
 */
@Entity
@Table(name = "cybk_recc_claim")
public class CYBKReccClaim extends BaseEntity implements Serializable {


    @Serial
    private static final long serialVersionUID = 683127068664989993L;

    /**
     * recc_id
     */
    private String reccId;

    /**
     * 长银还款流水号
     */
    private String setlSeq;
    /**
     * 长银授信流水号
     */
    private String creditNo;

    /**
     * 资金方借据
     */
    private String loanNo;

    /**
     * 借据号
     */
    private String sysId;

    private LocalDate claimDate;

    /**
     * 期数
     */
    private Integer period;


    /**
     * 归还金额
     */
    private BigDecimal returnAmount;

    /**
     * 代偿金额
     */
    private BigDecimal amount;

    /**
     * 代偿本金
     */
    private BigDecimal principalAmt;

    /**
     * 利息
     */
    private BigDecimal interestAmt;

    /**
     * 代偿罚息
     */
    private BigDecimal penaltyAmt;


    /**
     * 代偿复利
     */
    private BigDecimal overdueAmt;
    /**
     * 代偿费用
     */
    private BigDecimal feeAmt;

    /**
     * 银行
     */
    private String channel;

    /**
     * 处理状态
     */
    private String status;

    /**
     * 对账状态
     */
    private String reccStatus;

    /**
     * 还款模式
     * 01：到期日及之后的还款，一次还一期
     * 02：提前结清
     * 04：整笔借据逾期后，逾期结清
     * 06：提前还一期
     */
    private String repayMode;

    public String getReccId() {
        return reccId;
    }

    public void setReccId(String reccId) {
        this.reccId = reccId;
    }

    public String getSetlSeq() {
        return setlSeq;
    }

    public void setSetlSeq(String setlSeq) {
        this.setlSeq = setlSeq;
    }

    public String getCreditNo() {
        return creditNo;
    }

    public void setCreditNo(String creditNo) {
        this.creditNo = creditNo;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public String getSysId() {
        return sysId;
    }

    public void setSysId(String sysId) {
        this.sysId = sysId;
    }

    public LocalDate getClaimDate() {
        return claimDate;
    }

    public void setClaimDate(LocalDate claimDate) {
        this.claimDate = claimDate;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public BigDecimal getReturnAmount() {
        return returnAmount;
    }

    public void setReturnAmount(BigDecimal returnAmount) {
        this.returnAmount = returnAmount;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getPrincipalAmt() {
        return principalAmt;
    }

    public void setPrincipalAmt(BigDecimal principalAmt) {
        this.principalAmt = principalAmt;
    }

    public BigDecimal getInterestAmt() {
        return interestAmt;
    }

    public void setInterestAmt(BigDecimal interestAmt) {
        this.interestAmt = interestAmt;
    }

    public BigDecimal getPenaltyAmt() {
        return penaltyAmt;
    }

    public void setPenaltyAmt(BigDecimal penaltyAmt) {
        this.penaltyAmt = penaltyAmt;
    }

    public BigDecimal getOverdueAmt() {
        return overdueAmt;
    }

    public void setOverdueAmt(BigDecimal overdueAmt) {
        this.overdueAmt = overdueAmt;
    }

    public BigDecimal getFeeAmt() {
        return feeAmt;
    }

    public void setFeeAmt(BigDecimal feeAmt) {
        this.feeAmt = feeAmt;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getReccStatus() {
        return reccStatus;
    }

    public void setReccStatus(String reccStatus) {
        this.reccStatus = reccStatus;
    }

    public String getRepayMode() {
        return repayMode;
    }

    public void setRepayMode(String repayMode) {
        this.repayMode = repayMode;
    }
}
