package com.jinghang.capital.core.service.listener;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.dto.ContractBizDto;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.jinghang.common.util.JsonUtil;
import com.rabbitmq.client.Channel;
import java.nio.charset.StandardCharsets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 资方电子签章查询监听
 */
@Component
public class BankSignQueryListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(BankSignQueryListener.class);

    public BankSignQueryListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @Autowired
    private ManageService manageService;

    @RabbitListener(queues = RabbitConfig.Queues.BANK_SIGN_QUERY)
    public void listenContractUpload(Message message, Channel channel) {
        String contractMsg = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            logger.info("资方电子签章查询, 业务id:{}", contractMsg);
            var bizDto = JsonUtil.convertToObject(contractMsg, ContractBizDto.class);
            // manageService
            manageService.bankSignQuery(bizDto.getBusinessId(), bizDto.getStage());
        } catch (Exception e) {
            processException(contractMsg, message, e, "资方电子签章查询", getMqService()::submitBankSignQueryDelay);
        } finally {
            ackMsg(contractMsg, message, channel);
        }
    }
}
