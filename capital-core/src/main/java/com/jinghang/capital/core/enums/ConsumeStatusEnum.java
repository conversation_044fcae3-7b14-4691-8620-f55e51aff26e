package com.jinghang.capital.core.enums;

/**
 * <AUTHOR>
 * @description: OperateTypeEnum
 * @date 2024/8/16 14:55
 */
public enum ConsumeStatusEnum {
    //消费状态;APPLY-申请;-CONSUME-消费;FAILURE-授信失败解冻
    APPLY("APPLY", "申请"),
    CONSUME("CONSUME", "消费"),
    FAILURE("FAILURE", "授信失败解冻");
    private final String code;
    private final String msg;

    ConsumeStatusEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static ConsumeStatusEnum getByCode(String code) {
        for (ConsumeStatusEnum operateTypeEnum : ConsumeStatusEnum.values()) {
            if (operateTypeEnum.getCode().equals(code)) {
                return operateTypeEnum;
            }
        }
        return null;
    }

    public static String getMsgByCode(String code) {
        for (ConsumeStatusEnum operateTypeEnum : ConsumeStatusEnum.values()) {
            if (operateTypeEnum.getCode().equals(code)) {
                return operateTypeEnum.getMsg();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
