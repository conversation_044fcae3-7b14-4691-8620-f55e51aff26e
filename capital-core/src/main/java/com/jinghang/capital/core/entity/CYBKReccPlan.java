package com.jinghang.capital.core.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @Description 还款计划文件，所有放款成功的
 * @Date 2025-06-02
 * @Version 1.0
 */
@Entity
@Table(name = "cybk_recc_plan")
public class CYBKReccPlan extends BaseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = -6932682760210511669L;

    /**
     * recc_id
     */
    private String reccId;

    private String reccRepayId;
    private String sysId;
    private String creditId;

    /**
     * 长银借据号
     */
    private String cybkLoanNo;

    /**
     * 期数
     */
    private Integer period;


    /**
     * 还款编号
     */
    private String repayReqNo;


    /**
     * 还款日期
     */
    private LocalDate repayDay;
    /**
     * 到期日
     */
    private LocalDate dueDate;

    private BigDecimal repayAmount;
    private BigDecimal repayPrincipalAmt;
    private BigDecimal repayInterestAmt;
    private BigDecimal repayPenaltyAmt;
    private BigDecimal repayReinterestAmt;
    private BigDecimal repayGuaranteeAmt;
    private BigDecimal repayGuaranteeOverdueAmt;

    /**
     * 处理状态
     */
    private String status;

    /**
     * 资产池编号
     */
    private String channel;

    /**
     * 还款结果说明
     */
    private String result;

    /**
     * 还款模式
     * 01：到期日及之后的还款，一次还一期
     * 02：提前结清
     * 04：整笔借据逾期后，逾期结清
     * 06 ：提前还一期"
     */
    //private String repayMode;

    /**
     * 对账状态
     */
    private String reccStatus;

    public String getSysId() {
        return sysId;
    }

    public void setSysId(String sysId) {
        this.sysId = sysId;
    }

    public String getReccId() {
        return reccId;
    }

    public void setReccId(String reccId) {
        this.reccId = reccId;
    }

    public String getReccRepayId() {
        return reccRepayId;
    }

    public void setReccRepayId(String reccRepayId) {
        this.reccRepayId = reccRepayId;
    }

    public String getCreditId() {
        return creditId;
    }

    public void setCreditId(String creditId) {
        this.creditId = creditId;
    }

    public String getCybkLoanNo() {
        return cybkLoanNo;
    }

    public void setCybkLoanNo(String cybkLoanNo) {
        this.cybkLoanNo = cybkLoanNo;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public String getRepayReqNo() {
        return repayReqNo;
    }

    public void setRepayReqNo(String repayReqNo) {
        this.repayReqNo = repayReqNo;
    }

    public LocalDate getRepayDay() {
        return repayDay;
    }

    public void setRepayDay(LocalDate repayDay) {
        this.repayDay = repayDay;
    }

    public LocalDate getDueDate() {
        return dueDate;
    }

    public void setDueDate(LocalDate dueDate) {
        this.dueDate = dueDate;
    }

    public BigDecimal getRepayAmount() {
        return repayAmount;
    }

    public void setRepayAmount(BigDecimal repayAmount) {
        this.repayAmount = repayAmount;
    }

    public BigDecimal getRepayPrincipalAmt() {
        return repayPrincipalAmt;
    }

    public void setRepayPrincipalAmt(BigDecimal repayPrincipalAmt) {
        this.repayPrincipalAmt = repayPrincipalAmt;
    }

    public BigDecimal getRepayInterestAmt() {
        return repayInterestAmt;
    }

    public void setRepayInterestAmt(BigDecimal repayInterestAmt) {
        this.repayInterestAmt = repayInterestAmt;
    }

    public BigDecimal getRepayPenaltyAmt() {
        return repayPenaltyAmt;
    }

    public void setRepayPenaltyAmt(BigDecimal repayPenaltyAmt) {
        this.repayPenaltyAmt = repayPenaltyAmt;
    }

    public BigDecimal getRepayReinterestAmt() {
        return repayReinterestAmt;
    }

    public void setRepayReinterestAmt(BigDecimal repayReinterestAmt) {
        this.repayReinterestAmt = repayReinterestAmt;
    }

    public BigDecimal getRepayGuaranteeAmt() {
        return repayGuaranteeAmt;
    }

    public void setRepayGuaranteeAmt(BigDecimal repayGuaranteeAmt) {
        this.repayGuaranteeAmt = repayGuaranteeAmt;
    }

    public BigDecimal getRepayGuaranteeOverdueAmt() {
        return repayGuaranteeOverdueAmt;
    }

    public void setRepayGuaranteeOverdueAmt(BigDecimal repayGuaranteeOverdueAmt) {
        this.repayGuaranteeOverdueAmt = repayGuaranteeOverdueAmt;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    //public String getRepayMode() {
    //    return repayMode;
    //}
    //
    //public void setRepayMode(String repayMode) {
    //    this.repayMode = repayMode;
    //}

    public String getReccStatus() {
        return reccStatus;
    }

    public void setReccStatus(String reccStatus) {
        this.reccStatus = reccStatus;
    }
}
