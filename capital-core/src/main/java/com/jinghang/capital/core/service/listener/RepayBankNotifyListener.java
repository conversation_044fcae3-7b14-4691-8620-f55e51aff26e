package com.jinghang.capital.core.service.listener;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.rabbitmq.client.Channel;
import java.nio.charset.StandardCharsets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/6/22
 */
@Component
public class RepayBankNotifyListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(RepayBankNotifyListener.class);

    private ManageService manageService;

    public RepayBankNotifyListener(MqService mqService, WarningService mqWarningService, ManageService manageService) {
        super(mqService, mqWarningService);
        this.manageService = manageService;
    }

    @RabbitListener(queues = RabbitConfig.Queues.REPAY_BANK_NOTIFY_QUEUE)
    public void repayBankNotify(Message message, Channel channel) {
        String customRepayId = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            logger.info("对客已成功,通知银行还款:{}", customRepayId);
            manageService.retryBankRepayNotify(customRepayId);
        } catch (Exception e) {
            logger.error("通知银行还款异常, custom_record_id: {} ", customRepayId, e);
            processException(customRepayId, message, e, "对客已成功,通知银行还款异常", getMqService()::submitRepayBankNotifyDelay);
        } finally {
            ackMsg(customRepayId, message, channel);
        }
    }


}
