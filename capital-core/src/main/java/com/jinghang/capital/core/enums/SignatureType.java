package com.jinghang.capital.core.enums;

/**
 * 签章类型
 */
public enum SignatureType {
    TEMPLATE("TEMPLATE", "模版签署"),
    DYNAMIC("DYNAMIC", "动态签署");

    private final String code;

    private final String desc;

    SignatureType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
