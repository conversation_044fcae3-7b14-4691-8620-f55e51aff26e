package com.jinghang.capital.core.repository;


import com.jinghang.capital.core.entity.CovenantDownloadApplyRecord;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.FileType;
import com.jinghang.capital.core.enums.ProcessStatus;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

public interface CovenantDownloadApplyRecordRepository extends JpaRepository<CovenantDownloadApplyRecord, String> {

    List<CovenantDownloadApplyRecord> findByApplyStatusAndChannelAndFileTypeAndDownloadTimeGreaterThanEqualAndDownloadTimeLessThan(ProcessStatus applyStatus,
                                                                                                                                   BankChannel channel,
                                                                                                                                   FileType fileType,
                                                                                                                                   LocalDateTime startTime,
                                                                                                                                   LocalDateTime endTime);

    /**
     * 是否存在某些状态的申请数据
     *
     * @param loanId   借据ID
     * @param fileType 文件类型
     * @param period   期数
     * @param statuses 状态list
     * @return
     */
    boolean existsByLoanIdAndFileTypeAndPeriodAndDownloadStatusIn(String loanId, FileType fileType, Integer period, List<ProcessStatus> statuses);

}
