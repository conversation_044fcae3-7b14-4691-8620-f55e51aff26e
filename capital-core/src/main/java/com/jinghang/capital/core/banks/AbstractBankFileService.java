package com.jinghang.capital.core.banks;

import com.jinghang.capital.core.dto.CovenantDownApplyDto;
import com.jinghang.capital.core.dto.RateLimiterDto;
import com.jinghang.capital.core.entity.BankRepayRecord;
import com.jinghang.capital.core.entity.CovenantDownloadApplyRecord;
import com.jinghang.capital.core.entity.CovenantDownloadFailRecord;
import com.jinghang.capital.core.entity.DownloadFileLog;
import com.jinghang.capital.core.entity.Loan;
import com.jinghang.capital.core.entity.LoanFile;
import com.jinghang.capital.core.enums.AgreementType;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.BusinessSource;
import com.jinghang.capital.core.enums.CovenantDownloadMode;
import com.jinghang.capital.core.enums.CovenantDownloadType;
import com.jinghang.capital.core.enums.DownloadFileStatusEnum;
import com.jinghang.capital.core.enums.FileType;
import com.jinghang.capital.core.enums.LoanStage;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.exception.BizException;
import com.jinghang.capital.core.repository.BankRepayRecordRepository;
import com.jinghang.capital.core.repository.CovenantDownloadApplyRecordRepository;
import com.jinghang.capital.core.repository.CovenantDownloadFailRecordRepository;
import com.jinghang.capital.core.repository.DownloadFileLogRepository;
import com.jinghang.capital.core.service.CommonService;
import com.jinghang.capital.core.service.FileService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.util.FileUtil;
import com.jinghang.capital.core.vo.file.FileDailyProcessVo;
import com.jinghang.capital.core.vo.file.FileDownloadRequestVo;
import com.jinghang.capital.core.vo.file.FileDownloadResultVo;
import com.jinghang.capital.core.vo.file.FileDownloadVo;
import com.jinghang.capital.core.vo.file.FilePushVo;
import com.jinghang.capital.core.vo.file.FileSynRepayPlanResultDVo;
import com.jinghang.capital.core.vo.file.FileSynRepayPlanVo;
import com.jinghang.capital.core.vo.file.FileSyncDueFileResultVo;
import com.jinghang.capital.core.vo.file.FileSyncDueFileVo;
import com.jinghang.capital.core.vo.file.FileUploadVo;
import com.jinghang.capital.core.vo.file.PreviewResultVo;
import com.jinghang.capital.core.vo.file.PreviewVo;
import com.jinghang.common.util.DateUtil;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import org.apache.commons.lang3.RandomStringUtils;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RateType;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

public abstract class AbstractBankFileService implements BankFileService {
    private static final Logger logger = LoggerFactory.getLogger(AbstractBankFileService.class);
    public static final int RND_STR_LENGTH = 8;

    @Autowired
    private CommonService commonService;

    @Autowired
    private FileService fileService;

    @Autowired
    private DownloadFileLogRepository downloadFileLogRepository;

    @Autowired
    private MqService mqService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private CovenantDownloadApplyRecordRepository covenantDownloadApplyRecordRepository;

    @Autowired
    private CovenantDownloadFailRecordRepository covenantDownloadFailRecordRepository;

    @Autowired
    private BankRepayRecordRepository bankRepayRecordRepository;

    /**
     * remark字段最大长度
     */
    private static final int REMARK_MAX_LENGTH = 250;

    /**
     * 限流器Map
     */
    private static final Map<String, RRateLimiter> RATE_LIMITER_MAP = new ConcurrentHashMap<>();

    /**
     * 三十次每秒
     */
    protected static final long THIRTY_TIMES_HTTP = 30L;

    /**
     * 五次每秒
     */
    protected static final long FIVE_TIMES_SFTP = 5L;


    /**
     * 借款合同、放款凭证下载，或者其他文件下载 发送mq
     */
    protected void loanContractAndVoucherDownload(FileDailyProcessVo processVo) {
        String loanId = null == processVo.getLoanId() ? null : processVo.getLoanId().strip();
        List<Loan> loans;
        if (StringUtil.isNotBlank(loanId)) {
            loans = commonService.getLoanRepository().findByIdIn(Arrays.stream(loanId.split(",")).toList());
        } else {
            loans = commonService.findSuccessLoan(processVo.getBankChannel(), processVo.getProcessDate());
        }

        for (Loan loan : loans) {
            if (FileType.LOAN_CONTRACT.equals(processVo.getType())) {
                // 借款合同下载mq
                getMqService().submitCovenantDownloadLoanContract(loan.getId());
            } else if (FileType.LOAN_VOUCHER_FILE.equals(processVo.getType())) {
                // 放款凭证下载mq
                getMqService().submitCovenantDownloadLoanVoucher(loan.getId());
            } else {
                // 除借款合同、放款凭证外其他文件下载mq
                getMqService().submitCovenantDownloadOther(loan.getId());
            }
        }
    }

    /**
     * 代偿证明、回购证明下载 发送mq
     */
    protected void claimProveDownload(FileDailyProcessVo processVo) {
        LocalDateTime startOfDay = processVo.getProcessDate().atStartOfDay();
        LocalDateTime nextDayStart = processVo.getProcessDate().plusDays(1).atStartOfDay();
        List<BankRepayRecord> claimRepays = bankRepayRecordRepository.findListClaimSuccess(processVo.getBankChannel(), startOfDay, nextDayStart);

        for (BankRepayRecord claimRepay : claimRepays) {
            CovenantDownApplyDto dto = new CovenantDownApplyDto();
            dto.setLoanId(claimRepay.getLoanId());
            dto.setFileType(FileType.COMPENSATORY_VOUCHER_FILE);
            dto.setPeriod(claimRepay.getPeriod());
            getMqService().submitCovenantDownloadAsyncApply(JsonUtil.toJsonString(dto));
        }
    }

    /**
     * 结清证明下载 发送mq
     */
    protected void settleProveDownload(FileDailyProcessVo processVo) {
        String loanIdOrList = null == processVo.getLoanId() ? null : processVo.getLoanId().strip();
        List<BankRepayRecord> settleRepays;
        if (StringUtil.isNotBlank(loanIdOrList)) {
            List<String> loanIds = Arrays.stream(loanIdOrList.split(",")).toList();
            settleRepays = new ArrayList<>();
            for (String loanId : loanIds) {
                settleRepays.add(bankRepayRecordRepository.findClearSuccessByLoanIdNotClaim(loanId));
            }
        } else {
            LocalDateTime startOfDay = processVo.getProcessDate().atStartOfDay();
            LocalDateTime nextDayStart = processVo.getProcessDate().plusDays(1).atStartOfDay();
            settleRepays = bankRepayRecordRepository.findListSettleSuccessNotClaim(processVo.getBankChannel(), startOfDay, nextDayStart);
        }

        for (BankRepayRecord settleRepay : settleRepays) {
            CovenantDownApplyDto dto = new CovenantDownApplyDto();
            dto.setLoanId(settleRepay.getLoanId());
            dto.setFileType(FileType.CREDIT_SETTLE_VOUCHER_FILE);
            getMqService().submitCovenantDownloadAsyncApply(JsonUtil.toJsonString(dto));
        }
    }

    /**
     * 协议下载
     *
     * @param downloadVo
     * @return
     */
    @Override
    public FileDownloadResultVo download(FileDownloadVo downloadVo) {
        Loan loan;
        if (StringUtil.isNotBlank(downloadVo.getLoanId())) {
            loan = commonService.getLoanRepository().findById(downloadVo.getLoanId()).orElseThrow();
        } else {
            loan = commonService.getLoanRepository().findByOuterLoanId(downloadVo.getLoanOrderId()).orElseThrow();
        }

        // 借据关联的文件
        List<LoanFile> loanFiles = commonService.getLoanFileRepository().findByRelatedIdAndFileType(loan.getId(), downloadVo.getType());

        if (CollectionUtils.isEmpty(loanFiles)) {
            return new FileDownloadResultVo();
        }

        FileDownloadResultVo resultVo = new FileDownloadResultVo();
        LoanFile loanFile = loanFiles.get(0);
        resultVo.setOssBucket(loanFile.getOssBucket());
        resultVo.setOssPath(loanFile.getOssKey());
        resultVo.setFileName(loanFile.getFileName());
        resultVo.setSignStatus(loanFile.getSignStatus());
        resultVo.setFileStatus(loanFile.getSignStatus());
        return resultVo;
    }

    @Override
    public void covenantDownloadLoanContract(Loan loan) {
        // updateCovenantDownloadFailRetrySuccess(loan, FileType.LOAN_CONTRACT);
        covenantDownload(loan, FileType.LOAN_CONTRACT);
    }

    @Override
    public void covenantDownloadLoanVoucher(Loan loan) {
        // updateCovenantDownloadFailRetrySuccess(loan, FileType.LOAN_VOUCHER_FILE);
        covenantDownload(loan, FileType.LOAN_VOUCHER_FILE);
    }

    @Override
    public void covenantDownloadOther(Loan loan) {
        List<FileType> otherFileTypes = getOtherFileTypes(loan);
        if (null == otherFileTypes) {
            return;
        }
        for (FileType otherFileType : otherFileTypes) {
            // updateCovenantDownloadFailRetrySuccess(loan, otherFileType);
            covenantDownload(loan, otherFileType);
        }
    }

    @Override
    public void covenantDownloadAgenda(LocalDate localDate, FileType fileType) {

    }

    @Override
    public void covenantDownloadAsyncApply(CovenantDownApplyDto dto) {
        logger.info("资方贷后凭证文件下载：loanId:{}, fileType:{}", dto.getLoanId(), dto.getFileType());
        if (!FileType.COMPENSATORY_VOUCHER_FILE.equals(dto.getFileType())) {
            // 非代偿凭证文件，无需期数
            dto.setPeriod(null);
        }
        Loan loan = commonService.findLoanById(dto.getLoanId());
        if (!needDownloadCheck(loan, dto.getFileType(), dto.getPeriod())) {
            // 文件已下载，无需重复下载
            logger.info("资方贷后凭证文件已下载，无需重复下载：loanId:{}, fileType:{}", dto.getLoanId(), dto.getFileType());
            return;
        }
        if (checkApplyProcessing(loan, dto.getFileType(), dto.getPeriod())) {
            // 存在下载处理中的申请记录，无需重复下载
            return;
        }
        updateCovenantDownloadFailRetrySuccess(loan, dto.getFileType(), dto.getPeriod());
        CovenantDownloadApplyRecord applyRecord = insertCovenantDownloadApplyRecord(loan, dto.getFileType(), dto.getPeriod());
        // 限流
        getRateLimiterApply(loan, dto.getFileType()).acquire();
        try {
            capitalCovenantDownApply(applyRecord);
        } catch (Exception e) {
            logger.error("资方贷后凭证文件下载申请失败 loanId: {}, fileType: {}", dto.getLoanId(), dto.getFileType(), e);
            capitalCovenantDownApplyFail(applyRecord, e.getMessage());
            covenantDownloadFail(applyRecord, e.getMessage());
        }
    }

    @Override
    public void covenantDownloadAsyncQuery(String applyId) {
        logger.info("资方贷后凭证文件申请查询：applyId:{}", applyId);
        CovenantDownloadApplyRecord applyRecord = covenantDownloadApplyRecordRepository.findById(applyId).orElseThrow();
        logger.info("资方贷后凭证文件申请查询：loanId:{}, fileType:{}", applyRecord.getLoanId(), applyRecord.getFileType());
        Loan loan = commonService.findLoanById(applyRecord.getLoanId());
        // 限流
        getRateLimiterQuery(loan, applyRecord.getFileType()).acquire();
        capitalCovenantDownQuery(applyRecord);
    }

    /**
     * 资方协议文件申请下载失败
     *
     * @param applyRecord 协议文件下载申请记录
     * @param failMsg     失败描述
     */
    protected void capitalCovenantDownApplyFail(CovenantDownloadApplyRecord applyRecord, String failMsg) {
        if (null != failMsg && failMsg.length() > REMARK_MAX_LENGTH) {
            failMsg = failMsg.substring(0, REMARK_MAX_LENGTH);
        }
        if (!applyRecord.getApplyStatus().isFinal()) {
            applyRecord.setApplyStatus(ProcessStatus.FAIL);
        }
        applyRecord.setRemark(failMsg);
        applyRecord.setDownloadStatus(ProcessStatus.FAIL);
        covenantDownloadApplyRecordRepository.save(applyRecord);
    }

    /**
     * 资方协议文件下载申请成功
     *
     * @param applyRecord 协议文件下载申请记录
     */
    protected void capitalCovenantDownApplySuccess(CovenantDownloadApplyRecord applyRecord) {
        applyRecord.setApplyStatus(ProcessStatus.SUCCESS);
        covenantDownloadApplyRecordRepository.save(applyRecord);
    }

    /**
     * 资方协议文件申请并下载成功
     *
     * @param applyRecord
     * @param downloadUrl
     * @param ossBucket
     * @param ossFilePath
     */
    protected void capitalCovenantApplyDownSuccess(CovenantDownloadApplyRecord applyRecord, String downloadUrl, String ossBucket, String ossFilePath) {
        if (!ProcessStatus.SUCCESS.equals(applyRecord.getApplyStatus())) {
            applyRecord.setApplyStatus(ProcessStatus.SUCCESS);
        }
        applyRecord.setDownloadStatus(ProcessStatus.SUCCESS);
        applyRecord.setDownloadTime(LocalDateTime.now());
        applyRecord.setDownloadUrl(downloadUrl);
        applyRecord.setOssBucket(ossBucket);
        applyRecord.setOssFilePath(ossFilePath);
        covenantDownloadApplyRecordRepository.save(applyRecord);
    }

    protected CovenantDownloadApplyRecord insertCovenantDownloadApplyRecord(Loan loan, FileType fileType, Integer period) {
        CovenantDownloadApplyRecord applyRecord = new CovenantDownloadApplyRecord();
        applyRecord.setChannel(loan.getChannel());
        applyRecord.setLoanId(loan.getId());
        applyRecord.setPeriod(period);
        applyRecord.setFileType(fileType);
        applyRecord.setApplyTime(LocalDateTime.now());
        applyRecord.setApplyStatus(ProcessStatus.INIT);
        applyRecord.setDownloadType(getCovenantDownloadType(loan, fileType));
        applyRecord.setDownloadStatus(ProcessStatus.INIT);
        return covenantDownloadApplyRecordRepository.save(applyRecord);
    }

    /**
     * 检查是否存在下载处理中的申请记录
     *
     * @param loan     借据
     * @param fileType 文件类型
     * @param period   期数
     * @return
     */
    protected boolean checkApplyProcessing(Loan loan, FileType fileType, Integer period) {
        List<ProcessStatus> statuses = List.of(ProcessStatus.INIT, ProcessStatus.PROCESSING);
        return covenantDownloadApplyRecordRepository.existsByLoanIdAndFileTypeAndPeriodAndDownloadStatusIn(
                loan.getId(), fileType, period, statuses);
    }

    /**
     * 放款后，协议下载（正常模式标准流程）
     *
     * @param loan     借据信息
     * @param fileType 文件类型
     */
    protected void covenantDownload(Loan loan, FileType fileType) {
        if (!needDownloadCheck(loan, fileType)) {
            // 文件已下载，无需重复下载
            return;
        }

        Path path = null;
        try {
            // 限流
            getRateLimiter(loan, fileType).acquire();
            // 下载资方文件
            path = aloneDownloadPath(loan, fileType);
        } catch (Exception e) {
            logger.error("file download error，loanId: {}, fileType: {}", loan.getId(), fileType, e);
            covenantDownloadFail(loan, fileType, e.getMessage());
            return;
        }

        // 文件下载失败
        if (path == null || path.toFile().length() == 0) {
            // 删除临时文件
            FileUtil.deletePathFile(path);
            covenantDownloadFail(loan, fileType, path == null ? "文件不存在" : "文件大小为空");
            return;
        }

        // 文件上传至OSS
        uploadOss(path, loan, fileType, getOssBucket(loan));
    }

    /**
     * 合同下载失败
     *
     * @param loan
     * @param fileType
     * @param failMsg
     */
    protected void covenantDownloadFail(Loan loan, FileType fileType, String failMsg) {
        if (null != failMsg && failMsg.length() > REMARK_MAX_LENGTH) {
            failMsg = failMsg.substring(0, REMARK_MAX_LENGTH);
        }
        CovenantDownloadFailRecord failRecord = new CovenantDownloadFailRecord();
        failRecord.setChannel(loan.getChannel());
        failRecord.setLoanId(loan.getId());
        failRecord.setFileType(fileType);
        failRecord.setBusinessSource(BusinessSource.LOAN);
        failRecord.setBusinessId(loan.getId());
        failRecord.setFailedTime(LocalDateTime.now());
        failRecord.setRetryStatus(ProcessStatus.INIT);
        failRecord.setRemark(failMsg);
        covenantDownloadFailRecordRepository.save(failRecord);
    }

    /**
     * 合同申请下载失败
     *
     * @param applyRecord
     * @param failMsg
     */
    protected void covenantDownloadFail(CovenantDownloadApplyRecord applyRecord, String failMsg) {
        CovenantDownloadFailRecord failRecord = new CovenantDownloadFailRecord();
        failRecord.setChannel(applyRecord.getChannel());
        failRecord.setLoanId(applyRecord.getId());
        failRecord.setPeriod(applyRecord.getPeriod());
        failRecord.setFileType(applyRecord.getFileType());
        failRecord.setBusinessSource(BusinessSource.COVENANT_DOWNLOAD_APPLY_RECORD);
        failRecord.setBusinessId(applyRecord.getId());
        failRecord.setFailedTime(LocalDateTime.now());
        failRecord.setRetryStatus(ProcessStatus.INIT);
        failRecord.setRemark(failMsg);
        covenantDownloadFailRecordRepository.save(failRecord);
    }

    /**
     * 下载失败表记录标记为已重试
     *
     * @param loan     借据ID
     * @param fileType 文件类型
     */
    protected void updateCovenantDownloadFailRetrySuccess(Loan loan, FileType fileType) {
        List<CovenantDownloadFailRecord> failRecords = covenantDownloadFailRecordRepository
                .findByLoanIdAndFileTypeAndRetryStatus(loan.getId(), fileType, ProcessStatus.INIT);

        if (!CollectionUtils.isEmpty(failRecords)) {
            for (CovenantDownloadFailRecord failRecord : failRecords) {
                failRecord.setRetryStatus(ProcessStatus.SUCCESS);
                failRecord.setRetryTime(LocalDateTime.now());
                covenantDownloadFailRecordRepository.save(failRecord);
            }
        }
    }

    /**
     * 下载失败表记录标记为已重试
     *
     * @param loan     借据ID
     * @param fileType 文件类型
     */
    protected void updateCovenantDownloadFailRetrySuccess(Loan loan, FileType fileType, Integer period) {
        if (FileType.COMPENSATORY_VOUCHER_FILE != fileType) {
            updateCovenantDownloadFailRetrySuccess(loan, fileType);
            return;
        }

        String periodStr = null == period ? null : String.valueOf(period);
        List<CovenantDownloadFailRecord> failRecords = covenantDownloadFailRecordRepository
                .findByLoanIdAndFileTypeAndRemarkAndRetryStatus(loan.getId(), fileType, periodStr, ProcessStatus.INIT);

        if (!CollectionUtils.isEmpty(failRecords)) {
            for (CovenantDownloadFailRecord failRecord : failRecords) {
                failRecord.setRetryStatus(ProcessStatus.SUCCESS);
                failRecord.setRetryTime(LocalDateTime.now());
                covenantDownloadFailRecordRepository.save(failRecord);
            }
        }
    }

    /**
     * 文件下载类型
     * sftp、http等
     *
     * @param loan
     * @param fileType
     * @return
     */
    protected CovenantDownloadType getCovenantDownloadType(Loan loan, FileType fileType) {
        throw new BizException("", loan.getChannel().getDesc() + "下载类型，功能未实现！");
    }

    /**
     * 获取除借款合同、借款凭证外所有其他文件类型
     *
     * @return fileTypes
     */
    protected List<FileType> getOtherFileTypes(Loan loan) {
        throw new BizException("", loan.getChannel().getDesc() + "获取除借款合同、借款凭证外所有其他文件类型，功能未实现！");
    }

    /**
     * 资方协议文件下载申请
     * （如果申请资方协议失败，请抛出异常，或者自行将申请记录置为失败）
     *
     * @param applyRecord 协议文件下载申请记录
     */
    protected void capitalCovenantDownApply(CovenantDownloadApplyRecord applyRecord) {
        throw new BizException("", applyRecord.getChannel().getDesc() + "资方协议文件下载申请，功能未实现！");
    }

    /**
     * 资方协议文件下载查询
     *
     * @param applyRecord 协议文件下载申请记录
     */
    protected void capitalCovenantDownQuery(CovenantDownloadApplyRecord applyRecord) {
        throw new BizException("", applyRecord.getChannel().getDesc() + "资方协议文件下载查询，功能未实现！");
    }

    /**
     * 下载资方协议文件（非需要申请下载的所有文件都是这个方法）（需要各资方自行实现）
     *
     * @param loan     借据信息
     * @param fileType 文件类型
     * @return 本地临时path
     */
    protected Path aloneDownloadPath(Loan loan, FileType fileType) {
        throw new BizException("", loan.getChannel().getDesc() + "下载资方协议文件" + fileType.getDesc() + "功能未实现！");
    }

    /**
     * 资方贷后凭证文件下载（上传OSS并记录loan_file表）
     * 贷后凭证文件：代偿证明、结清证明等
     *
     * @param applyRecord 申请下载记录
     * @param path        path
     */
    protected void capitalLoanAfterVoucherCovenantDown(CovenantDownloadApplyRecord applyRecord, Path path, String downloadUrl) {
        Loan loan = commonService.findLoanById(applyRecord.getLoanId());

        // 文件下载失败
        if (path == null || path.toFile().length() == 0) {
            // 删除临时文件
            FileUtil.deletePathFile(path);
            covenantDownloadFail(loan, applyRecord.getFileType(), path == null ? "文件不存在" : "文件大小为空");
            return;
        }

        // 文件上传至OSS
        LoanFile loanFile = uploadBaseOss(path, loan, applyRecord.getFileType(), applyRecord.getPeriod(), getOssBucket(loan));
        capitalCovenantApplyDownSuccess(applyRecord, downloadUrl, loanFile.getOssBucket(), loanFile.getOssKey());
    }

    /**
     * 获取限流器名称后缀
     * 以文件获取方式区分，同一获取方式返回相同后缀
     *
     * @param loan     借据
     * @param fileType 文件类型
     * @return 限流器名称后缀
     */
    protected String getRateLimiterName(Loan loan, FileType fileType, CovenantDownloadMode downloadMode) {
        throw new BizException("", loan.getChannel().getDesc() + "获取限流器名称后缀" + fileType.getDesc() + "功能未实现！");
    }

    /**
     * 获取限流器的设置值
     *
     * @param loan     借据
     * @param fileType 文件类型
     * @return 限流配置
     */
    protected RateLimiterDto getRateLimiterConfig(Loan loan, FileType fileType, CovenantDownloadMode downloadMode) {
        throw new BizException("", loan.getChannel().getDesc() + "获取限流器名称后缀" + fileType.getDesc() + "功能未实现！");
    }

    private RRateLimiter getRateLimiterApply(Loan loan, FileType fileType) {
        return getBaseRateLimiter(loan, fileType, CovenantDownloadMode.APPLY);
    }

    private RRateLimiter getRateLimiterQuery(Loan loan, FileType fileType) {
        return getBaseRateLimiter(loan, fileType, CovenantDownloadMode.QUERY);
    }

    /**
     * 获取文件下载类限流器
     *
     * @param loan     借据
     * @param fileType 文件类型
     * @return 限流器
     */
    private RRateLimiter getRateLimiter(Loan loan, FileType fileType) {
        return getBaseRateLimiter(loan, fileType, CovenantDownloadMode.DIRECT);
    }

    private RRateLimiter getBaseRateLimiter(Loan loan, FileType fileType, CovenantDownloadMode downloadMode) {
        String limiterId = getRateLimiterName(loan, fileType, downloadMode);
        return RATE_LIMITER_MAP.computeIfAbsent(limiterId, id -> {
            RRateLimiter rateLimiter = redissonClient.getRateLimiter("covenant:rate:" + id);
            long rate = getRateLimiterConfig(loan, fileType, downloadMode).getRate();
            rateLimiter.setRate(RateType.OVERALL, rate, 1, RateIntervalUnit.SECONDS);
            return rateLimiter;
        });
    }


    /**
     * 下载结清证明文件
     *
     * @param downloadDate
     */
    protected void downloadSettleProveFile(LocalDate downloadDate, String loanId, BankChannel bankChannel) {
        List<Loan> settleLoans;
        if (StringUtil.isNotBlank(loanId)) {
            settleLoans = List.of(commonService.getLoanRepository().findById(loanId).orElseThrow());
        } else {
            LocalDateTime startDate = downloadDate.minusDays(2L).atStartOfDay();
            LocalDateTime endDate = downloadDate.plusDays(1L).atStartOfDay();
            settleLoans = commonService.getLoanRepository().findCustomerSettleLoans(bankChannel, startDate, endDate);
        }

        for (Loan settleLoan : settleLoans) {
            if (!needDownloadCheck(settleLoan, FileType.CREDIT_SETTLE_VOUCHER_FILE)) {
                // 当前借据的结清证明已下载成功
                continue;
            }

            // 查询是否已申请资方开具结清证明文件
            Optional<DownloadFileLog> downloadFileLogOptional = downloadFileLogRepository
                    .findByBizIdAndFileType(settleLoan.getId(), FileType.CREDIT_SETTLE_VOUCHER_FILE);
            if (downloadFileLogOptional.isPresent() && DownloadFileStatusEnum.P.equals(downloadFileLogOptional.get().getStatus())) {
                DownloadFileLog downloadFileLog = downloadFileLogOptional.get();
                // 查询资方结清证明文件开具结果 并下载转存文件至OSS
                capitalSettleProveFileOpenQuery(settleLoan, downloadFileLog);
                continue;
            }

            // 初始化 DownloadFileLog
            DownloadFileLog downloadFileLog = initDownloadFileLog(settleLoan, FileType.CREDIT_SETTLE_VOUCHER_FILE, bankChannel);
            // 申请资方 开具结清证明文件
            capitalSettleProveFileOpenApply(settleLoan, downloadFileLog);
        }
    }

    /**
     * 初始化 DownloadFileLog
     *
     * @param loan
     * @param fileType
     * @return
     */
    private DownloadFileLog initDownloadFileLog(Loan loan, FileType fileType, BankChannel bankChannel) {
        Optional<DownloadFileLog> downloadFileLogOptional = downloadFileLogRepository
                .findByBizIdAndFileType(loan.getId(), fileType);
        if (downloadFileLogOptional.isPresent()) {
            return downloadFileLogOptional.get();
        }
        DownloadFileLog downloadFileLog = new DownloadFileLog();
        downloadFileLog.setBankChannel(bankChannel);
        downloadFileLog.setBizId(loan.getId());
        downloadFileLog.setFileType(fileType);
        downloadFileLog.setStatus(DownloadFileStatusEnum.I);
        return downloadFileLog;
    }

    /**
     * 申请资方 开具结清证明文件
     *
     * @param settleLoan
     * @param downloadFileLog
     */
    protected void capitalSettleProveFileOpenApply(Loan settleLoan, DownloadFileLog downloadFileLog) {
        throw new BizException("", "结清证明下载申请 功能未实现！");
    }

    /**
     * 查询资方结清证明文件开具结果 并下载转存文件至OSS
     *
     * @param settleLoan
     * @param downloadFileLog
     */
    protected void capitalSettleProveFileOpenQuery(Loan settleLoan, DownloadFileLog downloadFileLog) {
        throw new BizException("", "查询资方结清证明文件开具结果 功能未实现！");
    }

    @Override
    public void downloadRequest(FileDownloadRequestVo requestVo) {

    }


    @Override
    public void batchVoucherDownload(FileDailyProcessVo fileDailyProcessVo) {

    }

    @Override
    public PreviewResultVo preview(PreviewVo previewVo) {
        return null;
    }

    @Override
    public void filePush(FilePushVo pushVo) {

    }

    @Override
    public void loanUploadImage(FileUploadVo uploadVo) {

    }

    private void needSignAgain(LoanFile loanFile) {
        //todo 签章枚举要替换为配置表获取
        AgreementType agreement = AgreementType.getAgreement(loanFile.getFileType(), loanFile.getChannel());
        // 只有（需要重新签章的协议 && loanFile签章状态不是已签署成功 && loanFile的未签章ossKey为空时）才需要转存未签章ossKey与未签章ossBucket
        if (null != agreement
                && agreement.getNeedSignAgain()
                && ProcessStatus.SUCCESS != loanFile.getSignStatus()
                && StringUtil.isEmpty(loanFile.getOssUnsignKey())) {
            // 需要再次签章的文件
            loanFile.setOssUnsignBucket(loanFile.getOssBucket());
            loanFile.setOssUnsignKey(loanFile.getOssKey());
        }
    }

    /**
     * 上传文件到OSS
     *
     * @param loan        借据信息
     * @param ossFilePath oss路径
     * @param tempPath    需要上传的文件
     * @param fileType    文件类型
     */
    protected void uploadOss(Loan loan, String ossFilePath, Path tempPath, FileType fileType) {
        // 查询文件是否存在
        List<LoanFile> loanFiles = commonService.getLoanFileRepository().findByRelatedIdAndFileType(loan.getId(), fileType);
        LoanFile loanFile;
        if (CollectionUtils.isEmpty(loanFiles)) {
            loanFile = new LoanFile();
            loanFile.setRelatedId(loan.getId());
            loanFile.setCreditId(loan.getCreditId());
            loanFile.setChannel(loan.getChannel());
        } else {
            loanFile = loanFiles.get(0);
            needSignAgain(loanFile);
        }

        try (InputStream inputStream = new FileInputStream(tempPath.toFile())) {
            String ossBucket = getOssBucket(loan);
            fileService.uploadOss(ossBucket, ossFilePath, inputStream);
            loanFile.setOssBucket(ossBucket);
            loanFile.setOssKey(ossFilePath);
            loanFile.setSignStatus(ProcessStatus.SUCCESS);
            loanFile.setStage(getStage(fileType));
            loanFile.setFileType(fileType);
            loanFile.setFileName(fileType.getDesc());
            commonService.getLoanFileRepository().save(loanFile);
        } catch (IOException e) {
            logger.error("上传文件到OSS异常", e);
            throw new RuntimeException("上传文件到OSS异常", e);
        }

        // 删除临时文件
        FileUtil.deletePathFile(tempPath);
    }

    /**
     * 上传文件到OSS
     *
     * @param tempPath  需要上传的文件
     * @param loan      借据信息
     * @param fileType  文件类型
     * @param ossBucket ossBucket
     */
    protected LoanFile uploadOss(Path tempPath, Loan loan, FileType fileType, String ossBucket) {
        return uploadBaseOss(tempPath, loan, fileType, null, ossBucket);
    }

    /**
     * 上传文件到OSS
     *
     * @param tempPath  需要上传的文件
     * @param loan      借据信息
     * @param fileType  文件类型
     * @param ossBucket ossBucket
     */
    protected LoanFile uploadBaseOss(Path tempPath, Loan loan, FileType fileType, Integer period, String ossBucket) {
        String ossFilePath;
        if (period == null) {
            ossFilePath = getOssPath(loan, fileType);
        } else {
            ossFilePath = getOssPath(loan, fileType, period);
        }
        LoanFile loanFile;
        try (InputStream inputStream = Files.newInputStream(tempPath)) {
            fileService.uploadOss(ossBucket, ossFilePath, inputStream);
            loanFile = saveLoanFile(loan, fileType, period, ossBucket, ossFilePath);
        } catch (IOException e) {
            logger.error("上传文件到OSS异常", e);
            throw new RuntimeException("上传文件到OSS异常", e);
        } finally {
            // 删除临时文件
            FileUtil.deletePathFile(tempPath);
        }

        return loanFile;
    }

    private String getOssPath(Loan loan, FileType fileType) {
        if (FileType.CREDIT_SETTLE_VOUCHER_FILE == fileType) {
            return getCreditSettleVoucherFileOssPath(loan, fileType);
        }
        return "contract/" + loan.getChannel().name() + "/" + loan.getLoanTime().format(DateUtil.SHORT_FORMATTER)
                + "/" + loan.getId() + "-" + fileType.name() + generateRandomFilePath() + ".pdf";
    }

    private String getOssPath(Loan loan, FileType fileType, Integer period) {
        return switch (fileType) {
            case COMPENSATORY_VOUCHER_FILE -> getCompensatoryVoucherFileOssPath(loan, fileType, period);
            case CREDIT_SETTLE_VOUCHER_FILE -> getCreditSettleVoucherFileOssPath(loan, fileType);
            default -> "contract/" + loan.getChannel().name() + "/" + loan.getLoanTime().format(DateUtil.SHORT_FORMATTER)
                    + "/" + loan.getId() + "-" + fileType.name() + generateRandomFilePath() + ".pdf";
        };
    }

    private String generateRandomFilePath() {
        return "-" + RandomStringUtils.randomAlphabetic(RND_STR_LENGTH);
    }

    /**
     * 结清证明ossKey
     *
     * @param loan
     * @param fileType
     * @return
     */
    private String getCreditSettleVoucherFileOssPath(Loan loan, FileType fileType) {
        BankRepayRecord record = bankRepayRecordRepository.findClearSuccessByLoanIdNotClaim(loan.getId());
        return "contract/" + loan.getChannel().name() + "/" + record.getRepayTime().format(DateUtil.SHORT_FORMATTER)
                + "/" + loan.getId() + "-" + fileType.name() + generateRandomFilePath() + ".pdf";
    }

    /**
     * 代偿证明ossKey
     *
     * @param loan
     * @param fileType
     * @param period
     * @return
     */
    private String getCompensatoryVoucherFileOssPath(Loan loan, FileType fileType, Integer period) {
        BankRepayRecord record = bankRepayRecordRepository.findClaimSuccessByLoanIdAndPeriod(loan.getId(), period);
        return "contract/" + loan.getChannel().name() + "/" + record.getRepayTime().format(DateUtil.SHORT_FORMATTER)
                + "/" + loan.getId() + "-" + period + "-" + fileType.name() + generateRandomFilePath() + ".pdf";
    }

    /**
     * 新增或者更新loan_file表
     *
     * @param loan      借据信息
     * @param fileType  文件类型
     * @param ossBucket bucket
     * @param ossKey    key
     * @return
     */
    protected LoanFile saveLoanFile(Loan loan, FileType fileType, String ossBucket, String ossKey) {
        return saveLoanFile(loan, fileType, null, ossBucket, ossKey);
    }

    /**
     * 新增或者更新loan_file表
     *
     * @param loan      借据信息
     * @param fileType  文件类型
     * @param period    期数（代偿证明文件需要期数）
     * @param ossBucket bucket
     * @param ossKey    key
     * @return
     */
    protected LoanFile saveLoanFile(Loan loan, FileType fileType, Integer period, String ossBucket, String ossKey) {
        // 查询文件是否存在
        List<LoanFile> loanFiles;
        if (FileType.COMPENSATORY_VOUCHER_FILE.equals(fileType) && null != period) {
            loanFiles = commonService.getLoanFileRepository().findByRelatedIdAndFileTypeAndRemark(loan.getId(), fileType, period.toString());
        } else {
            loanFiles = commonService.getLoanFileRepository().findByRelatedIdAndFileType(loan.getId(), fileType);
        }

        LoanFile loanFile;
        if (CollectionUtils.isEmpty(loanFiles)) {
            loanFile = new LoanFile();
            loanFile.setRelatedId(loan.getId());
            loanFile.setCreditId(loan.getCreditId());
            loanFile.setChannel(loan.getChannel());
            loanFile.setStage(getStage(fileType));
            loanFile.setFileType(fileType);
            loanFile.setFileName(fileType.getDesc());
            loanFile.setRemark(null != period ? period.toString() : null);
        } else {
            loanFile = loanFiles.get(0);
            needSignAgain(loanFile);
        }

        loanFile.setSignStatus(ProcessStatus.SUCCESS);
        loanFile.setOssKey(ossKey);
        loanFile.setOssBucket(ossBucket);
        return commonService.getLoanFileRepository().save(loanFile);
    }

    /**
     * 文件类型映射阶段
     *
     * @param fileType
     * @return
     */
    protected String getStage(FileType fileType) {
        return switch (fileType) {
            case PERSONAL_INFORMATION_QUERY_LETTER, PERSONAL_CREDIT_AUTHORIZATION_LETTER, PERSONAL_INFORMATION_LETTER,
                 AMOUNT_CONTRACT, SYNTHESIS_AUTHORIZATION, PERSONAL_INFORMATION_QUERY_LETTER_LHD, PERSONAL_CREDIT_AUTHORIZATION_LETTER_LHD,
                 PERSONAL_INFORMATION_QUERY_LETTER_YTX, SYNTHESIS_CREDIT_AUTHORIZATION_LETTER_UNION -> LoanStage.CREDIT.name();
            case LOAN_CONTRACT, LOAN_VOUCHER_FILE, DEBT_CONFIRMATION_AGREEMENT,
                 PERSONAL_LOAN_CUSTOMER_COMMIT_CONFIRMATION, ENTRUSTED_DEDUCTION_LETTER,
                 ENTRUSTED_GUARANTEE_CONTRACT, IRREVOCABLE_GUARANTEE_LETTER -> LoanStage.LOAN.name();
            case COMPENSATORY_VOUCHER_FILE, CREDIT_SETTLE_VOUCHER_FILE -> LoanStage.AFTER_LOAN.name();
            default -> null;
        };
    }

    /**
     * 获取ossBucket
     *
     * @return
     */
    protected String getOssBucket(Loan loan) {
        return getOssBucket();
    }

    /**
     * 获取ossBucket
     *
     * @return
     */
    protected String getOssBucket() {
        throw new BizException("", "未知的ossBucket");
    }

    /**
     * 检测是否需要重新下载文件
     * 需要下载 true
     * 无需下载 false
     *
     * @param loan     借据
     * @param fileType 文件类型
     * @return 是否需要
     */
    protected boolean needDownloadCheck(Loan loan, FileType fileType) {
        List<LoanFile> loanFiles = commonService.getLoanFileRepository().findByRelatedIdAndFileType(loan.getId(), fileType);
        return needDownloadCheck(loanFiles);
    }

    /**
     * 检测是否需要重新下载文件
     * 需要下载 true
     * 无需下载 false
     *
     * @param loan     借据
     * @param fileType 文件类型
     * @return 是否需要
     */
    protected boolean needDownloadCheck(Loan loan, FileType fileType, Integer period) {
        List<LoanFile> loanFiles;
        if (period == null) {
            loanFiles = commonService.getLoanFileRepository().findByRelatedIdAndFileType(loan.getId(), fileType);
            return needDownloadCheck(loanFiles);
        }
        loanFiles = commonService.getLoanFileRepository().findByRelatedIdAndFileTypeAndRemark(loan.getId(), fileType, period.toString());
        return needDownloadCheck(loanFiles);
    }

    /**
     * 检测是否需要重新下载文件
     * 需要下载 true
     * 无需下载 false
     *
     * @param loanFiles 文件记录
     * @return 是否需要
     */
    private boolean needDownloadCheck(List<LoanFile> loanFiles) {
        if (CollectionUtils.isEmpty(loanFiles)) {
            return true;
        }
        LoanFile loanFile = loanFiles.get(0);
        if (ProcessStatus.SUCCESS != loanFile.getSignStatus()) {
            return true;
        }
        return fileService.getOssFileSize(loanFile.getOssBucket(), loanFile.getOssKey()) == 0;
    }


    protected boolean checkFileIsEmpty(Path file) {
        if (file == null) {
            return true;
        }
        try {
            return Files.size(file) == 0;
        } catch (IOException e) {
            throw new RuntimeException("检测下载空文件异常", e);
        }
    }

    @Override
    public FileSynRepayPlanResultDVo synRepayPlanFile(FileSynRepayPlanVo fileSynRepayPlanVo) {
        return null;
    }

    @Override
    public FileSyncDueFileResultVo syncDueFile(FileSyncDueFileVo fileSyncDueFileVo) {
        return null;
    }

    protected CovenantDownloadApplyRecordRepository getCovenantDownloadApplyRecordRepository() {
        return covenantDownloadApplyRecordRepository;
    }

    protected CovenantDownloadFailRecordRepository getCovenantDownloadFailRecordRepository() {
        return covenantDownloadFailRecordRepository;
    }

    protected MqService getMqService() {
        return mqService;
    }

    public abstract ByteArrayOutputStream getCheckFileStream(String fileName, Integer size);
}
