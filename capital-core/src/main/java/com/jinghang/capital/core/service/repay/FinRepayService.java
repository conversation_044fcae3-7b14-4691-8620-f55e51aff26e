package com.jinghang.capital.core.service.repay;


import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.jinghang.capital.core.entity.AccountBankCard;
import com.jinghang.capital.core.entity.BankLoanReplan;
import com.jinghang.capital.core.entity.BankRepayRecord;
import com.jinghang.capital.core.entity.BatchCustomerRepayDetail;
import com.jinghang.capital.core.entity.BatchCustomerRepayRecord;
import com.jinghang.capital.core.entity.ClaimAfterRepayRecord;
import com.jinghang.capital.core.entity.CompensatedRepayNotify;
import com.jinghang.capital.core.entity.CompensatedRepayNotifyDetail;
import com.jinghang.capital.core.entity.CustomerLoanReplan;
import com.jinghang.capital.core.entity.CustomerRepayRecord;
import com.jinghang.capital.core.entity.FlowClaimRecord;
import com.jinghang.capital.core.entity.Loan;
import com.jinghang.capital.core.entity.LoanReplan;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.enums.RepayMode;
import com.jinghang.capital.core.enums.RepayStatus;
import com.jinghang.capital.core.enums.RepayType;
import com.jinghang.capital.core.enums.WhetherState;
import com.jinghang.capital.core.exception.BizErrorCode;
import com.jinghang.capital.core.exception.BizException;
import com.jinghang.capital.core.repository.AccountBankCardRepository;
import com.jinghang.capital.core.repository.BankLoanReplanRepository;
import com.jinghang.capital.core.repository.BankRepayRecordRepository;
import com.jinghang.capital.core.repository.BatchCustomerRepayDetailRepository;
import com.jinghang.capital.core.repository.BatchCustomerRepayRecordRepository;
import com.jinghang.capital.core.repository.ClaimAfterRepayRecordRepository;
import com.jinghang.capital.core.repository.ClaimRecordRepository;
import com.jinghang.capital.core.repository.CompensatedRepayDetailRepository;
import com.jinghang.capital.core.repository.CompensatedRepayNotifyRepository;
import com.jinghang.capital.core.repository.CustomerLoanReplanRepository;
import com.jinghang.capital.core.repository.CustomerRepayRecordRepository;
import com.jinghang.capital.core.repository.LoanReplanRepository;
import com.jinghang.capital.core.repository.LoanRepository;
import com.jinghang.capital.core.vo.repay.PlanQueryVo;
import com.jinghang.common.util.StringUtil;
import jakarta.annotation.Nullable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

@Service
public class FinRepayService {

    private static final Logger logger = LoggerFactory.getLogger(FinRepayService.class);

    private LoanRepository loanRepository;

    private LoanReplanRepository loanReplanRepository;

    private BankRepayRecordRepository bankRepayRecordRepository;

    private CustomerRepayRecordRepository customerRepayRecordRepository;

    private AccountBankCardRepository accountBankCardRepository;

    private ClaimAfterRepayRecordRepository claimAfterRepayRecordRepository;

    private BankLoanReplanRepository bankLoanReplanRepository;
    private CustomerLoanReplanRepository customerLoanReplanRepository;

    private CompensatedRepayNotifyRepository compensatedRepayNotifyRepository;
    private CompensatedRepayDetailRepository compensatedRepayDetailRepository;

    private BatchCustomerRepayRecordRepository batchCustomerRepayRecordRepository;

    private BatchCustomerRepayDetailRepository batchCustomerRepayDetailRepository;


    private ClaimRecordRepository claimRecordRepository;


    public FinRepayService(LoanRepository loanRepository,
                           LoanReplanRepository loanReplanRepository,
                           BankRepayRecordRepository bankRepayRecordRepository,
                           CustomerRepayRecordRepository customerRepayRecordRepository,
                           AccountBankCardRepository accountBankCardRepository,
                           ClaimAfterRepayRecordRepository claimAfterRepayRecordRepository) {
        this.loanRepository = loanRepository;
        this.loanReplanRepository = loanReplanRepository;
        this.bankRepayRecordRepository = bankRepayRecordRepository;
        this.customerRepayRecordRepository = customerRepayRecordRepository;
        this.accountBankCardRepository = accountBankCardRepository;
        this.claimAfterRepayRecordRepository = claimAfterRepayRecordRepository;
    }

    @Autowired
    public void setClaimRecordRepository(ClaimRecordRepository claimRecordRepository) {
        this.claimRecordRepository = claimRecordRepository;
    }


    @Autowired
    public void setBatchCustomerRepayRecordRepository(BatchCustomerRepayRecordRepository batchCustomerRepayRecordRepository) {
        this.batchCustomerRepayRecordRepository = batchCustomerRepayRecordRepository;
    }

    @Autowired
    public void setCustomerRepayDetailRepository(BatchCustomerRepayDetailRepository batchCustomerRepayDetailRepository) {
        this.batchCustomerRepayDetailRepository = batchCustomerRepayDetailRepository;
    }

    @Autowired
    public void setBankLoanReplanRepository(BankLoanReplanRepository bankLoanReplanRepository) {
        this.bankLoanReplanRepository = bankLoanReplanRepository;
    }

    @Autowired
    public void setCustomerLoanReplanRepository(CustomerLoanReplanRepository customerLoanReplanRepository) {
        this.customerLoanReplanRepository = customerLoanReplanRepository;
    }

    @Autowired
    public void setCompensatedRepayNotifyRepository(CompensatedRepayNotifyRepository compensatedRepayNotifyRepository) {
        this.compensatedRepayNotifyRepository = compensatedRepayNotifyRepository;
    }

    @Autowired
    public void setCompensatedRepayDetailRepository(CompensatedRepayDetailRepository compensatedRepayDetailRepository) {
        this.compensatedRepayDetailRepository = compensatedRepayDetailRepository;
    }

    public Loan getLoan(PlanQueryVo planQueryVo) {
        if (StringUtil.isNotBlank(planQueryVo.getLoanId())) {
            return loanRepository.findById(planQueryVo.getLoanId()).orElseThrow(() -> new BizException(BizErrorCode.LOAN_NOT_FOUND));
        }
        return loanRepository.findByOuterLoanId(planQueryVo.getSysLoanId()).orElseThrow(() -> new BizException(BizErrorCode.LOAN_NOT_FOUND));
    }


    public Loan getLoan(String loanId, String outerLoanId) {
        if (StringUtil.isNotBlank(loanId)) {
            return loanRepository.findById(loanId).orElseThrow(() -> new BizException(BizErrorCode.LOAN_NOT_FOUND));
        }
        return loanRepository.findByOuterLoanId(outerLoanId).orElseThrow(() -> new BizException(BizErrorCode.LOAN_NOT_FOUND));
    }

    public AccountBankCard getBankCard(String cardId) {
        return accountBankCardRepository.findById(cardId).orElseThrow(() -> new BizException(BizErrorCode.BANK_PROTOCOL_NOT_FOUND));
    }

    public BankRepayRecord getBankRepayRecord(String recordId) {
        return bankRepayRecordRepository.findById(recordId).orElseThrow(() -> new BizException(BizErrorCode.BANK_REPAY_NOT_FOUND));
    }

    public Optional<BankRepayRecord> findBySysIdAndRepayStatusIn(String sysId) {
        return bankRepayRecordRepository.findBySysIdAndRepayStatusIn(sysId, List.of(ProcessStatus.INIT, ProcessStatus.PROCESSING, ProcessStatus.SUCCESS));
    }

    public Optional<BankRepayRecord> findBankRecordBySysId(String sysId) {
        return bankRepayRecordRepository.findBySysId(sysId);
    }

    public BankRepayRecord getBankRepayRecordSuccess(String loanId, Integer period) {
        return bankRepayRecordRepository.findByLoanIdAndPeriodAndRepayStatus(loanId, period, ProcessStatus.SUCCESS)
                .orElseThrow(() -> new BizException(BizErrorCode.BANK_REPAY_NOT_FOUND));
    }

    public CustomerRepayRecord getCustomerRepayRecordSuccess(String loanId, Integer period) {
        return customerRepayRecordRepository.findByLoanIdAndPeriodAndRepayStatus(loanId, period, ProcessStatus.SUCCESS)
                .orElseThrow(() -> new BizException(BizErrorCode.BANK_REPAY_NOT_FOUND));
    }

    public BankRepayRecord findBankRepayRecordSuccess(String loanId, Integer period) {
        return bankRepayRecordRepository.findByLoanIdAndPeriodAndRepayStatus(loanId, period, ProcessStatus.SUCCESS)
                .orElse(null);
    }

    public List<BankRepayRecord> findBankRepayRecordSuccess(BankChannel channel, LocalDate actRepayDay) {
        LocalDateTime dayStart = actRepayDay.atStartOfDay();
        LocalDateTime nextDayStart = actRepayDay.plusDays(1L).atStartOfDay();
        return bankRepayRecordRepository.findByRepayStatusAndChannelAndRepayTimeIsGreaterThanEqualAndRepayTimeIsLessThan(ProcessStatus.SUCCESS, channel,
                dayStart, nextDayStart);
    }

    public BankRepayRecord findByLoanIdAndPeriodAndRepayStatus(String loanId, Integer period, ProcessStatus status) {
        return bankRepayRecordRepository.findByLoanIdAndPeriodAndRepayStatus(loanId, period, status).orElse(null);
    }

    public BankRepayRecord saveBankRepayRecord(BankRepayRecord repayRecord) {
        return bankRepayRecordRepository.save(repayRecord);
    }

    public BankRepayRecord updateBankRepayRecord(BankRepayRecord repayRecord) {
        return bankRepayRecordRepository.save(repayRecord);
    }


    public BatchCustomerRepayRecord findBatchRepayRecordById(String id) {
        return batchCustomerRepayRecordRepository.findById(id).orElseThrow(() -> new BizException(BizErrorCode.BANK_REPAY_NOT_FOUND));
    }

    public BatchCustomerRepayRecord updateBatchRepayRecord(BatchCustomerRepayRecord batchRepayRecord) {
        return batchCustomerRepayRecordRepository.save(batchRepayRecord);
    }

    public List<BatchCustomerRepayDetail> findByGmxtRepayDetailsIds(List<String> gmxtRepayDetailsIds) {
        return batchCustomerRepayDetailRepository.findByIdIn(gmxtRepayDetailsIds);
    }

    public List<BatchCustomerRepayDetail> findGmxtRepayDetailsByBatchRepayId(String batchRepayId) {
        return batchCustomerRepayDetailRepository.findByBatchRepayId(batchRepayId);
    }

    public void saveAllCustomerRepayDetail(List<BatchCustomerRepayDetail> details) {
        batchCustomerRepayDetailRepository.saveAll(details);
    }

    public List<BankRepayRecord> findBankRepayRecordsByLoanIdAndPeriod(String loanId, Integer period) {
        return bankRepayRecordRepository.findBankRepayRecordsByLoanIdAndPeriod(loanId, period);
    }

    public Optional<BankRepayRecord> findBankRepayRecordsByLoanIdAndPeriodAndRepayStatus(String loanId, Integer period, ProcessStatus repayStatus) {
        return bankRepayRecordRepository.findTopBankRepayRecordsByLoanIdAndPeriodAndRepayStatus(loanId, period, repayStatus);
    }

    public CustomerRepayRecord getCustomRepayRecord(String recordId) {
        return customerRepayRecordRepository.findById(recordId).orElseThrow(() -> new BizException(BizErrorCode.REPAY_NOT_FOUND));
    }

    public List<CustomerRepayRecord> getCustomRepayRecordsByLoanIdAndPeriod(String loanId, Integer period) {
        return customerRepayRecordRepository.findByLoanIdAndPeriod(loanId, period);
    }

    public List<CustomerRepayRecord> getCustomOfflineRepayRecords(BankChannel channel, LocalDate repayDay) {
        LocalDateTime startTime = repayDay.atStartOfDay();
        LocalDateTime endTime = repayDay.plusDays(1L).atStartOfDay();
        return customerRepayRecordRepository.findCustRepaysOffline(channel, ProcessStatus.SUCCESS, RepayMode.OFFLINE, startTime, endTime);
    }


    public CustomerRepayRecord updateCustomRepayRecord(CustomerRepayRecord repayRecord) {
        return customerRepayRecordRepository.save(repayRecord);
    }

    public List<LoanReplan> getRepayPlans(String loanId) {
        return loanReplanRepository.findByLoanIdOrderByPeriod(loanId);
    }

    public List<LoanReplan> getRepayPlanByLoanIds(List<String> loanIds) {
        return loanReplanRepository.findByCustRepayStatusAndLoanIdIn(RepayStatus.NORMAL, loanIds);
    }

    public LoanReplan saveLoanReplan(LoanReplan loanReplan) {
        return loanReplanRepository.save(loanReplan);
    }

    public List<LoanReplan> findRepayPlanByLoanIdAndCustomStatus(String loanId, RepayStatus repayStatus) {
        return loanReplanRepository.findByLoanIdAndCustRepayStatusOrderByPeriod(loanId, repayStatus);
    }

    public LoanReplan findRepayPlanByLoanIdAndPeriod(String loanId, Integer period) {
        return loanReplanRepository.findByLoanIdAndPeriod(loanId, period).orElseThrow(() -> new BizException(BizErrorCode.LOAN_REPLAN_NOT_EXIST));
    }

    public List<LoanReplan> findRepayPlanByLoanIdAndBankStatus(String loanId, RepayStatus repayStatus) {
        return loanReplanRepository.findByLoanIdAndBankRepayStatusOrderByPeriod(loanId, repayStatus);
    }

    public List<LoanReplan> findRepayPlanBankNotPaid(LocalDate repayDate, BankChannel channel) {
        return loanReplanRepository.findByRepayDateAndBankRepayStatusAndChannel(repayDate, RepayStatus.NORMAL, channel.getCode());
    }

    public List<LoanReplan> findRepayPlanBankNotPaidAndGuaranteeCompany(LocalDate repayDate, BankChannel channel, GuaranteeCompany guaranteeCompany) {
        return loanReplanRepository.findByBankRepayStatusAndRepayDateAndChannel(RepayStatus.NORMAL, repayDate, channel.getCode(), guaranteeCompany);
    }

    public List<LoanReplan> findRepayPlanBankNotPaidAndCustomerNotPaid(LocalDate repayDate, BankChannel channel) {
        return loanReplanRepository.findByRepayDateAndCustRepayStatusAndBankRepayStatusAndChannel(repayDate, RepayStatus.NORMAL,
                RepayStatus.NORMAL, channel.getCode());
    }

    public List<LoanReplan> findRepayPlanBankNotPaidAndCustomerNotPaidAndRepayDate(LocalDate repayDate, BankChannel channel) {
        return loanReplanRepository.findByCustRepayStatusAndBankRepayStatusAndChannelAndRepayDateLessThanEqual(RepayStatus.NORMAL,
                RepayStatus.NORMAL, channel.getCode(), repayDate);
    }

    public List<LoanReplan> saveAllRepayPlanList(List<LoanReplan> newRepayList) {
        return loanReplanRepository.saveAll(newRepayList);
    }


    public LoanReplan getRepayPlan(String loanId, Integer period) {
        return loanReplanRepository.findByLoanIdAndPeriod(loanId, period).orElseThrow(() -> new BizException(BizErrorCode.LOAN_REPLAN_NOT_EXIST));
    }

    public LoanReplan findRepayPlan(String loanId, Integer period) {
        return loanReplanRepository.findByLoanIdAndPeriod(loanId, period).orElse(null);
    }

    public LoanReplan updateRepayPlan(LoanReplan loanReplan) {
        return loanReplanRepository.save(loanReplan);
    }

    public List<LoanReplan> findByRepayDateBeforeAndCustRepayStatus(String bankChannel, LocalDate localDate, RepayStatus repayStatus,
                                                                    PageRequest pageRequest) {
        return loanReplanRepository.findByChannelAndRepayDateBeforeAndCustRepayStatus(bankChannel, localDate, repayStatus, pageRequest);
    }

    public boolean existClaimedRepayPlan(String loanId, Integer period) {
        return bankLoanReplanRepository.existsByLoanIdAndPeriodAndRepayType(loanId, period, RepayType.CLAIM);
    }

    public boolean existClaimedAndSubstituteRepayPlan(String loanId, Integer period) {
        return bankLoanReplanRepository.existsByLoanIdAndPeriodAndRepayTypeIn(loanId, period, List.of(RepayType.CLAIM, RepayType.SUBSTITUTE));
    }

    public List<LoanReplan> findClaimedAndCustomNotPaid(String loanId, String channel, Integer period) {
        return loanReplanRepository.findClaimedAndCustomNotPaid(loanId, channel, period);
    }


    public BankLoanReplan getBankRepayPlan(String loanId, Integer period) {
        return bankLoanReplanRepository.findByLoanIdAndPeriod(loanId, period).orElseThrow(() -> new BizException(BizErrorCode.LOAN_REPLAN_NOT_EXIST));
    }

    public BankLoanReplan getBankRepayPlan(String bankLoanReplanId) {
        return bankLoanReplanRepository.findById(bankLoanReplanId).orElseThrow(() -> new BizException(BizErrorCode.LOAN_REPLAN_NOT_EXIST));
    }


    public BankLoanReplan findBankRepayPlan(String loanId, Integer period) {
        return bankLoanReplanRepository.findByLoanIdAndPeriod(loanId, period).orElse(null);
    }

    public List<BankLoanReplan> getBankRepayPlans(String loanId) {
        return bankLoanReplanRepository.findByLoanId(loanId);
    }

    public List<BankLoanReplan> getClaimBankRepayPlans(BankChannel bankChannel, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        return bankLoanReplanRepository.findClaimBankLoanReplans(bankChannel, startDateTime, endDateTime);
    }

    public BankLoanReplan saveBankRepayPlan(BankLoanReplan bankLoanReplan) {
        return bankLoanReplanRepository.save(bankLoanReplan);
    }

    public CustomerLoanReplan saveCustomerRepayPlan(CustomerLoanReplan customerLoanReplan) {
        return customerLoanReplanRepository.save(customerLoanReplan);
    }

    public CustomerLoanReplan getCustomRepayPlan(String loanId, Integer period) {
        return customerLoanReplanRepository.findByLoanIdAndPeriod(loanId, period).orElse(null);
    }

    public CompensatedRepayNotify saveCompensatedRepayNotify(CompensatedRepayNotify compensatedRepayNotify) {
        return compensatedRepayNotifyRepository.save(compensatedRepayNotify);
    }

    public CompensatedRepayNotifyDetail saveCompensatedRepayDetail(CompensatedRepayNotifyDetail detail) {
        return compensatedRepayDetailRepository.save(detail);
    }


    public FlowClaimRecord getClaimRecord(String claimRecordId) {
        return claimRecordRepository.findById(claimRecordId).orElse(null);
    }

    public FlowClaimRecord getClaimRecord(String loanId, Integer period, ProcessStatus processStatus) {
        return claimRecordRepository.findByLoanIdAndPeriodAndClaimStatus(loanId, period, processStatus);
    }

    public FlowClaimRecord saveClaimRecord(FlowClaimRecord flowClaimRecord) {
        return claimRecordRepository.save(flowClaimRecord);
    }

    public void saveCompensatedRepayDetails(List<CompensatedRepayNotifyDetail> detailList) {
        compensatedRepayDetailRepository.saveAll(detailList);
    }

    @Nullable
    public ClaimAfterRepayRecord findClaimAfterRecordById(String recordId) {
        return claimAfterRepayRecordRepository.findById(recordId).orElse(null);
    }

    /**
     * 查询代还前去通知的成功记录
     *
     * @return
     */
    public ClaimAfterRepayRecord findSuccessSubstituteNotifyRecord(String loanId, Integer period, WhetherState whetherState) {
        return claimAfterRepayRecordRepository.findByLoanIdAndPeriodAndIsSubstituteNotifyAndNotifyStatus(loanId, period, whetherState, ProcessStatus.SUCCESS);
    }

    /**
     * 是否存在代还前去通知的成功记录
     *
     * @return
     */
    public boolean isExistSuccessRecord(String loanId, Integer period) {
        return claimAfterRepayRecordRepository.existsByLoanIdAndPeriodAndNotifyStatus(loanId, period, ProcessStatus.SUCCESS);
    }

    public List<BankLoanReplan> findClaimBankRepayPlans(String loanId, Integer period) {
        return bankLoanReplanRepository.findByLoanIdAndRepayStatusAndRepayTypeAndPeriodLessThan(loanId, RepayStatus.REPAID, RepayType.CLAIM, period);
    }

    public boolean isExistValidClaimNotifyRecord(String loanId, Integer period) {
        return claimAfterRepayRecordRepository.existsByLoanIdAndPeriodAndNotifyStatusIn(loanId,
                period, List.of(ProcessStatus.SUCCESS, ProcessStatus.INIT, ProcessStatus.PROCESSING));
    }

    public ClaimAfterRepayRecord updateClaimAfterRecord(ClaimAfterRepayRecord record) {
        return claimAfterRepayRecordRepository.save(record);
    }


}
