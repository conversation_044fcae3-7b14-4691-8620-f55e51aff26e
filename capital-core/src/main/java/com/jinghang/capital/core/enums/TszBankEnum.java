package com.jinghang.capital.core.enums;


import com.jinghang.capital.core.exception.BizErrorCode;
import com.jinghang.capital.core.exception.BizException;
import java.util.Objects;

/**
 * 360端银行名称和编码
 */
public enum TszBankEnum {

    /**
     *
     */
    A("0001", "中国银行", "BOC"),
    B("0002", "中国农业银行", "ABC"),
    C("0003", "中国工商银行", "ICBC"),
    D("0004", "中国建设银行", "CCB"),
    E("0005", "交通银行", "COMM"),
    F("0006", "光大银行", "CEB"),
    G("0007", "中国民生银行", "CMBC"),
    H("0008", "中国平安银行", "SPABANK"),
    I("0009", "中国邮政储蓄银行", "PSBC"),
    J("0010", "广发银行", "CGB"),
    K("0011", "中信银行", "CITIC"),
    L("0012", "华夏银行", "HXB"),
    M("0013", "上海浦东发展银行", "SPDB"),
    N("0014", "兴业银行", "CIB"),
    O("0015", "招商银行", "CMB"),
    P("0017", "杭州银行", "HZB"),
    Q("0018", "上海银行", "BOS"),
    R("0019", "北京银行", "BOB");

    TszBankEnum(String outerCode, String innerName, String innerCode) {
        this.outerCode = outerCode;
        this.innerName = innerName;
        this.innerCode = innerCode;
    }

    /**
     * 360方的编码
     */
    private final String outerCode;
    private final String innerName;
    private final String innerCode;

    public static TszBankEnum findByInnerName(String innerName) throws BizException {
        for (TszBankEnum it : TszBankEnum.values()) {
            if (Objects.equals(it.getInnerName(), innerName)) {
                return it;
            }
        }
        throw new BizException(BizErrorCode.PARAM_EXCEPTION);
    }


    public static TszBankEnum findByInnerCode(String innerCode) throws BizException {
        for (TszBankEnum it : TszBankEnum.values()) {
            if (Objects.equals(it.getInnerCode(), innerCode)) {
                return it;
            }
        }
        throw new BizException(BizErrorCode.PARAM_EXCEPTION);
    }


    public String getOuterCode() {
        return outerCode;
    }


    public String getInnerName() {
        return innerName;
    }


    public String getInnerCode() {
        return innerCode;
    }


}
