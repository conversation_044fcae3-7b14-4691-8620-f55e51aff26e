package com.jinghang.capital.core.service.credit;


import com.jinghang.capital.core.entity.Account;
import com.jinghang.capital.core.entity.AccountBankCard;
import com.jinghang.capital.core.entity.AccountContactInfo;
import com.jinghang.capital.core.entity.AccountDevice;
import com.jinghang.capital.core.entity.Credit;
import com.jinghang.capital.core.entity.FailedCredit;
import com.jinghang.capital.core.entity.LoanFile;
import com.jinghang.capital.core.entity.QuotaCycleUserInfo;
import com.jinghang.capital.core.entity.Shop;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.CreditStatus;
import com.jinghang.capital.core.enums.CreditType;
import com.jinghang.capital.core.enums.FileType;
import com.jinghang.capital.core.exception.BizErrorCode;
import com.jinghang.capital.core.exception.BizException;
import com.jinghang.capital.core.repository.AccountBankCardRepository;
import com.jinghang.capital.core.repository.AccountContactInfoRepository;
import com.jinghang.capital.core.repository.AccountDeviceRepository;
import com.jinghang.capital.core.repository.AccountRepository;
import com.jinghang.capital.core.repository.CreditRepository;
import com.jinghang.capital.core.repository.FailedCreditRepository;
import com.jinghang.capital.core.repository.LoanFileRepository;
import com.jinghang.capital.core.repository.QuotaCycleUserInfoRepository;
import com.jinghang.capital.core.repository.ShopRepository;
import java.time.LocalDateTime;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class FinCreditService {
    private static final Logger logger = LoggerFactory.getLogger(FinCreditService.class);

    private final Integer thirty = 30;

    private final AccountRepository accountRepository;

    private final AccountContactInfoRepository accountContactInfoRepository;

    private final AccountBankCardRepository bankCardRepository;

    private final AccountDeviceRepository accountDeviceRepository;

    private final ShopRepository shopRepository;

    private final LoanFileRepository loanFileRepository;

    private final CreditRepository creditRepository;

    @Autowired
    private FailedCreditRepository failedCreditRepository;

    @Autowired
    private QuotaCycleUserInfoRepository quotaCycleUserInfoRepository;

    /**
     * FbankUserInfoRepository
     *
     * @param accountRepository
     * @param accountContactInfoRepository
     * @param loanFileRepository
     * @param creditRepository
     * @param accountDeviceRepository
     * @param accountBankCardRepository
     * @param shopRepository
     */

    public FinCreditService(AccountRepository accountRepository, AccountContactInfoRepository accountContactInfoRepository,
                            LoanFileRepository loanFileRepository, CreditRepository creditRepository, AccountDeviceRepository accountDeviceRepository,
                            AccountBankCardRepository accountBankCardRepository, ShopRepository shopRepository) {
        this.accountRepository = accountRepository;
        this.accountContactInfoRepository = accountContactInfoRepository;
        this.loanFileRepository = loanFileRepository;
        this.creditRepository = creditRepository;
        this.accountDeviceRepository = accountDeviceRepository;
        this.bankCardRepository = accountBankCardRepository;
        this.shopRepository = shopRepository;
    }

    public Account getAccount(String accountId) {
        return accountRepository.findById(accountId).orElseThrow(() -> new BizException(BizErrorCode.ACCOUNT_NOT_FOUND));
    }

    public List<AccountContactInfo> getAccountContactInfos(String accountId) {
        return accountContactInfoRepository.findByAccountId(accountId);
    }

    public AccountBankCard getBankCard(String cardId) {
        return bankCardRepository.findById(cardId).orElseThrow(() -> new BizException(BizErrorCode.PARAM_EXCEPTION));
    }

    public AccountDevice getAccountDevice(String accountId) {
        return accountDeviceRepository.findByAccountId(accountId);
    }

    public Shop getShop(String shopId) {
        return shopRepository.findById(shopId).orElseThrow(() -> new BizException(BizErrorCode.PARAM_EXCEPTION));
    }

    public List<LoanFile> getImageFiles(String creditId) {
        return loanFileRepository.findByCreditId(creditId);
    }

    public LoanFile getProtocolFile(String creditId, FileType fileType) {
        return loanFileRepository.findFirstByCreditIdAndFileTypeOrderByCreatedTimeDesc(creditId, fileType);
    }

    public Credit getCredit(String creditId) {
        return creditRepository.findById(creditId).orElseThrow(() -> new BizException(BizErrorCode.CREDIT_NOT_FOUND));
    }

    public FailedCredit getRiskFailedCredit(String creditId) {
        return failedCreditRepository.findById(creditId).orElseThrow(() -> new BizException(BizErrorCode.CREDIT_NOT_FOUND));
    }


    public Credit getCreditByOuterId(String outerCreditId) {
        return creditRepository.findCreditByOuterCreditId(outerCreditId).orElseThrow(() -> new BizException(BizErrorCode.CREDIT_NOT_FOUND));
    }

    public Credit updateCredit(Credit credit) {
        return creditRepository.save(credit);
    }

    public List<Credit> queryThirtyDayCreditFailRecord(BankChannel channel, String certNo) {
        LocalDateTime failCreditDate = LocalDateTime.now().minusDays(thirty);
        return creditRepository.queryThirtyDayCreditFailRecord(channel, CreditStatus.FAIL, certNo, failCreditDate);
    }

    public List<Credit> queryThirtyDayCreditFailRecord(BankChannel channel, String certNo, LocalDateTime failCreditDate) {
        return creditRepository.queryThirtyDayCreditFailRecord(channel, CreditStatus.FAIL, certNo, failCreditDate);
    }

    public Credit querySuccessCreditRecord(BankChannel channel, String certNo) {
        return creditRepository.getValidCredit(certNo, CreditStatus.SUCCESS.name(), channel.name(), CreditType.NORMAL_CREDIT.name()).orElse(null);
    }


    public QuotaCycleUserInfo getQuotaCycleUserInfoRepository(BankChannel bankChannel, String idCard) {
        return quotaCycleUserInfoRepository.findByChannelAndUserIdCard(bankChannel, idCard).orElseThrow();
    }
}
