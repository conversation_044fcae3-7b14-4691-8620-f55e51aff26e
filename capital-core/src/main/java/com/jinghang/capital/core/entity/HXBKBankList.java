package com.jinghang.capital.core.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serial;
import java.io.Serializable;


/**
 * 蚂蚁直连 放款支持银行列表
 */
@Entity
@Table(name = "hxbk_bank_list")
public class HXBKBankList implements Serializable {


    @Serial
    private static final long serialVersionUID = 825635379549238786L;

    @Id
    private Integer id;

    /**
     * 是否支持还款标志(默认N)：Y:是 N:否
     */
    private String isSupRepayFlg;

    /**
     * 银行编码
     */
    private String bankCode;

    /**
     * 银行名称
     */
    private String bankName;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getIsSupRepayFlg() {
        return isSupRepayFlg;
    }

    public void setIsSupRepayFlg(String isSupRepayFlg) {
        this.isSupRepayFlg = isSupRepayFlg;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

}
