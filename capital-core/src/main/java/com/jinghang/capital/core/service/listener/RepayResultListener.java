package com.jinghang.capital.core.service.listener;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.service.*;
import com.rabbitmq.client.Channel;
import java.nio.charset.StandardCharsets;
import java.time.Duration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/5/14
 */
@Component
public class RepayResultListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(RepayResultListener.class);

    private final int five = 5;
    private final int waitTime = 2;
    public RepayResultListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @Autowired
    private ManageService manageService;

    @Autowired
    private LockService lockService;

    @RabbitListener(queues = RabbitConfig.Queues.REPAY_QUERY)
    public void listenRepayResult(Message message, Channel channel) {
        String repayId = new String(message.getBody(), StandardCharsets.UTF_8);
        logger.info("监听还款结果查询:{}", repayId);

        String redisKey = "fin_repayId_query_" + repayId;
        Locker lock = lockService.getLock(redisKey);
        try {
            boolean locked = lock.tryLock(Duration.ofSeconds(waitTime), Duration.ofSeconds(five));
            if (locked) {
                logger.info("监听还款结果查询,key：{}", redisKey);
                // manageService
                manageService.repayQuery(repayId);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            logger.error("还款异常，bankRepayId: {}", repayId, e);
            processException(repayId, message, e, "查询还款结果异常", getMqService()::submitRepayQueryDelay);
        } finally {
            lock.unlock();
            ackMsg(repayId, message, channel);
        }
    }
}
