package com.jinghang.capital.core.service.listener;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.dto.UserInfoUpdateDto;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.jinghang.common.util.JsonUtil;
import com.rabbitmq.client.Channel;
import java.nio.charset.StandardCharsets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 用户信息更新申请
 */
@Component
public class UserInfoUpdateApplyListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(UserInfoUpdateApplyListener.class);

    @Autowired
    private ManageService manageService;

    public UserInfoUpdateApplyListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @RabbitListener(queues = RabbitConfig.Queues.USER_INFO_UPDATE_APPLY)
    public void userInfoUpdateApply(Message message, Channel channel) {
        String json = new String(message.getBody(), StandardCharsets.UTF_8);
        logger.info("用户信息更新申请: {}", json);
        try {
            UserInfoUpdateDto dto = JsonUtil.convertToObject(json, UserInfoUpdateDto.class);
            manageService.userInfoUpdateApply(dto);
        } catch (Exception e) {
            logger.error("用户信息更新申请 异常: {} ", json, e);
        } finally {
            ackMsg(json, message, channel);
        }
    }
}
