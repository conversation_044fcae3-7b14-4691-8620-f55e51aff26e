package com.jinghang.capital.core.service.listener;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.rabbitmq.client.Channel;
import java.nio.charset.StandardCharsets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class LoanUploadImageListener extends AbstractListener {

    private static final Logger logger = LoggerFactory.getLogger(LoanUploadImageListener.class);

    @Autowired
    private ManageService manageService;

    public LoanUploadImageListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @RabbitListener(queues = RabbitConfig.Queues.LOAN_UPLOAD_IMAGE)
    public void loanUploadImageListener(Message message, Channel channel) {
        String accountId = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            logger.info("监听文件上传结果:{}", accountId);
            manageService.loanUploadImage(accountId);
        } catch (Exception e) {
            processException(accountId, message, e, "上送合同给资方", getMqService()::submitLoanUploadImageDelay);
        } finally {
            ackMsg(accountId, message, channel);
        }
    }
}
