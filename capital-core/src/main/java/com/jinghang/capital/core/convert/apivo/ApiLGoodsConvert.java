package com.jinghang.capital.core.convert.apivo;


import com.jinghang.capital.api.dto.goods.GoodsDetailUploadDto;
import com.jinghang.capital.core.convert.StatusConvert;
import com.jinghang.capital.core.vo.file.FilePushVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * api 和  vo 的转换器
 */
@Mapper(uses = {StatusConvert.class, FileInfoConvert.class})
public interface ApiLGoodsConvert {
    ApiLGoodsConvert INSTANCE = Mappers.getMapper(ApiLGoodsConvert.class);

    FilePushVo toGoodsDetailUploadVo(GoodsDetailUploadDto goodsDetailUploadDto);


}
