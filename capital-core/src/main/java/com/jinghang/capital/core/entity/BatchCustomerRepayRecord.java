package com.jinghang.capital.core.entity;

import com.jinghang.capital.api.dto.repay.RepayCategory;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.enums.RepayMode;
import com.jinghang.capital.core.enums.RepayPurpose;
import com.jinghang.capital.core.enums.RepayType;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.math.BigDecimal;

/**
 * 批量扣款
 */
@Entity
@Table(name = "batch_customer_repay_record")
public class BatchCustomerRepayRecord extends BaseEntity {

    /**
     * 外部还款id
     */
    private String outerRepayId;

    /**
     * 资方渠道
     */
    @Enumerated(EnumType.STRING)
    private BankChannel channel;

    /**
     * 还款状态
     */
    @Enumerated(EnumType.STRING)
    private ProcessStatus status;

    /**
     * 总金额
     */
    private BigDecimal totalAmt;

    /**
     * 本金
     */
    private BigDecimal principalAmt;

    /**
     * 利息
     */
    private BigDecimal interestAmt;

    /**
     * 罚息
     */
    private BigDecimal penaltyAmt;

    /**
     * 违约金
     */
    private BigDecimal breachAmt;
    /**
     * 融单费
     */
    private BigDecimal guaranteeFee;

    /**
     * 减免金额
     */
    private BigDecimal reduceAmt;

    /**
     * 还款类型
     */
    @Enumerated(EnumType.STRING)
    private RepayType repayType;

    /**
     * 还款模式
     */
    @Enumerated(EnumType.STRING)
    private RepayMode repayMode;

    /**
     * 还款目的
     */
    @Enumerated(EnumType.STRING)
    private RepayPurpose repayPurpose;

    /**
     * 还款分类（代偿前后）
     */
    @Enumerated(EnumType.STRING)
    private RepayCategory repayCategory;

    /**
     * 扣款协议号
     */
    private String agreementNo;

    /**
     * 还款银行编码
     */
    private String repayBankCode;

    /**
     * 还款卡号
     */
    private String repayAcctNo;

    /**
     * 还款方用户名
     */
    private String repayRelUser;

    /**
     * 还款方手机号
     */
    private String repayRelPhone;

    /**
     * 还款方身份证号
     */
    private String repayRelCard;

    public String getOuterRepayId() {
        return outerRepayId;
    }

    public void setOuterRepayId(String outerRepayId) {
        this.outerRepayId = outerRepayId;
    }

    public String getAgreementNo() {
        return agreementNo;
    }

    public void setAgreementNo(String agreementNo) {
        this.agreementNo = agreementNo;
    }

    public String getRepayBankCode() {
        return repayBankCode;
    }

    public void setRepayBankCode(String repayBankCode) {
        this.repayBankCode = repayBankCode;
    }

    public String getRepayAcctNo() {
        return repayAcctNo;
    }

    public void setRepayAcctNo(String repayAcctNo) {
        this.repayAcctNo = repayAcctNo;
    }

    public String getRepayRelUser() {
        return repayRelUser;
    }

    public void setRepayRelUser(String repayRelUser) {
        this.repayRelUser = repayRelUser;
    }

    public String getRepayRelPhone() {
        return repayRelPhone;
    }

    public void setRepayRelPhone(String repayRelPhone) {
        this.repayRelPhone = repayRelPhone;
    }

    public String getRepayRelCard() {
        return repayRelCard;
    }

    public void setRepayRelCard(String repayRelCard) {
        this.repayRelCard = repayRelCard;
    }

    public BankChannel getChannel() {
        return channel;
    }

    public void setChannel(BankChannel channel) {
        this.channel = channel;
    }

    public ProcessStatus getStatus() {
        return status;
    }

    public void setStatus(ProcessStatus status) {
        this.status = status;
    }

    public BigDecimal getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(BigDecimal totalAmt) {
        this.totalAmt = totalAmt;
    }

    public BigDecimal getPrincipalAmt() {
        return principalAmt;
    }

    public void setPrincipalAmt(BigDecimal principalAmt) {
        this.principalAmt = principalAmt;
    }

    public BigDecimal getInterestAmt() {
        return interestAmt;
    }

    public void setInterestAmt(BigDecimal interestAmt) {
        this.interestAmt = interestAmt;
    }

    public BigDecimal getPenaltyAmt() {
        return penaltyAmt;
    }

    public void setPenaltyAmt(BigDecimal penaltyAmt) {
        this.penaltyAmt = penaltyAmt;
    }

    public BigDecimal getBreachAmt() {
        return breachAmt;
    }

    public void setBreachAmt(BigDecimal breachAmt) {
        this.breachAmt = breachAmt;
    }

    public BigDecimal getReduceAmt() {
        return reduceAmt;
    }

    public void setReduceAmt(BigDecimal reduceAmt) {
        this.reduceAmt = reduceAmt;
    }

    public RepayType getRepayType() {
        return repayType;
    }

    public void setRepayType(RepayType repayType) {
        this.repayType = repayType;
    }

    public RepayMode getRepayMode() {
        return repayMode;
    }

    public void setRepayMode(RepayMode repayMode) {
        this.repayMode = repayMode;
    }

    public RepayPurpose getRepayPurpose() {
        return repayPurpose;
    }

    public void setRepayPurpose(RepayPurpose repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public RepayCategory getRepayCategory() {
        return repayCategory;
    }

    public void setRepayCategory(RepayCategory repayCategory) {
        this.repayCategory = repayCategory;
    }

    public BigDecimal getGuaranteeFee() {
        return guaranteeFee;
    }

    public void setGuaranteeFee(BigDecimal guaranteeFee) {
        this.guaranteeFee = guaranteeFee;
    }
}
