package com.jinghang.capital.core.enums;

/**
 * 额度调整业务类型枚举
 */
public enum AdjustBusinessType {

    // 额度调整申请类
    ADJUSTAMTAPPLY("ADJUST_AMT_APPLY", "额度调整申请"),
    ADJUSTAMTUPAPPLY("ADJUST_AMT_UP_APPLY", "额度调增"),
    ADJUSTAMTDOWNAPPLY("ADJUST_AMT_DOWN_APPLY", "额度调减"),

    // 利率调整类
    ADJUSTRATEAPPLY("ADJUST_RATE_APPLY", "利率调整"),
    ADJUSTRATEUPAPPLY("ADJUST_RATE_UP_APPLY", "利率调增"),
    ADJUSTRATEDOWNAPPLY("ADJUST_RATE_DOWN_APPLY", "利率调减"),

    // 清退类
    CLEARUPAPPLY("CLEAR_UP_APPLY", "清退(注销)"),

    // 临时额度类
    TMPAMTAPPLY("TMP_AMT_APPLY", "临时额度"),

    // 有效期调整类
    ADJUSTVALIDDATE("ADJUST_VALID_DATE", "调整有效期"),

    // 额度冻结类
    CREDITFROZEN("CREDIT_FROZEN", "额度冻结"),
    CREDITUNFROZEN("CREDIT_UNFROZEN", "额度解冻");

    // 枚举属性
    private final String code;
    private final String description;

    /**
     * 构造方法
     */
    AdjustBusinessType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 获取业务类型代码（带下划线）
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取业务类型描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举实例
     */
    public static AdjustBusinessType getByCode(String code) {
        for (AdjustBusinessType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }
}
