package com.jinghang.capital.core.service.loan;


import com.jinghang.capital.core.convert.entity.PlanConvert;
import com.jinghang.capital.core.entity.Loan;
import com.jinghang.capital.core.entity.LoanFile;
import com.jinghang.capital.core.entity.LoanReplan;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.FileType;
import com.jinghang.capital.core.enums.LoanStatus;
import com.jinghang.capital.core.enums.RepayStatus;
import com.jinghang.capital.core.exception.BizErrorCode;
import com.jinghang.capital.core.exception.BizException;
import com.jinghang.capital.core.repository.CreditRepository;
import com.jinghang.capital.core.repository.LoanFileRepository;
import com.jinghang.capital.core.repository.LoanReplanRepository;
import com.jinghang.capital.core.repository.LoanRepository;
import com.jinghang.capital.core.vo.repay.PlanVo;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FinLoanService {

    private static final Logger logger = LoggerFactory.getLogger(FinLoanService.class);

    private CreditRepository creditRepository;

    private LoanRepository loanRepository;

    private LoanReplanRepository loanReplanRepository;

    private LoanFileRepository loanFileRepository;

    @Autowired
    public FinLoanService(CreditRepository creditRepository, LoanRepository loanRepository, LoanReplanRepository loanReplanRepository,
                          LoanFileRepository loanFileRepository) {
        this.creditRepository = creditRepository;
        this.loanRepository = loanRepository;
        this.loanReplanRepository = loanReplanRepository;
        this.loanFileRepository = loanFileRepository;
    }

    public Loan findLoan(String creditId, String sysLoanId) {
        // Credit credit = creditRepository.findById(creditId).orElseThrow(() -> new BizException(BizErrorCode.CREDIT_NOT_FOUND);

        Loan loan = loanRepository.findLoanByCreditIdAndOuterLoanId(creditId, sysLoanId)
                .orElseThrow(() -> new BizException(BizErrorCode.LOAN_NOT_FOUND));

        return loan;
    }

    public Loan findLoanById(String loanId) {
        // Credit credit = creditRepository.findById(creditId).orElseThrow(() -> new BizException(BizErrorCode.CREDIT_NOT_FOUND);

        Loan loan = loanRepository.findById(loanId)
                .orElseThrow(() -> new BizException(BizErrorCode.LOAN_NOT_FOUND));

        return loan;
    }


    public Loan updateLoan(Loan loan) {
        return loanRepository.save(loan);
    }

    public List<LoanReplan> savePlans(List<LoanReplan> replanList) {
        loanReplanRepository.saveAll(replanList);
        return replanList;
    }

    /**
     * 生成还款计划
     *
     * @param planVo
     */
    public List<LoanReplan> genRepayPlans(PlanVo planVo) {
        List<LoanReplan> loanReplans = new ArrayList<>();
        planVo.getPlanItems().forEach(
                planItemVo -> {
                    LoanReplan loanReplan = PlanConvert.INSTANCE.toEntity(planItemVo);
                    loanReplan.setCustRepayStatus(RepayStatus.NORMAL);
                    loanReplan.setBankRepayStatus(RepayStatus.NORMAL);
                    loanReplans.add(loanReplan);
                }
        );
        return loanReplanRepository.saveAll(loanReplans);
    }

    public List<LoanReplan> findLoanReplanByLoanId(String loanId) {
        return loanReplanRepository.findByLoanIdOrderByPeriod(loanId);
    }


    public LoanFile getLoanFileByLoanIdAndType(String loanId, FileType fileType) {
        List<LoanFile> files = loanFileRepository.findByRelatedIdAndFileType(loanId, fileType);
        if (files.isEmpty()) {
            throw new BizException(BizErrorCode.FILE_TYPE_NOT_FOUND);
        }
        return files.stream().max(Comparator.comparing(LoanFile::getCreatedTime))
                .orElseThrow(() -> new BizException(BizErrorCode.FILE_TYPE_NOT_FOUND));
    }

    /**
     * 检查是否存在授信处理中
     *
     * @param channel    资方
     * @param custCertNo 用户身份证号码
     * @return 存在 true 不存在 false
     */
    public boolean checkLoanProcessing(BankChannel channel, String custCertNo) {
        return loanRepository.existsByChannelAndCustCertNoAndLoanStatus(channel, custCertNo, LoanStatus.PROCESSING);
    }
}
