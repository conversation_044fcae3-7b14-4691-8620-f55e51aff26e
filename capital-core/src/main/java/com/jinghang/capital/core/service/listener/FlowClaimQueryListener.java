package com.jinghang.capital.core.service.listener;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.rabbitmq.client.Channel;
import java.nio.charset.StandardCharsets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 流量方代偿结果查询监听
 */
@Component
public class FlowClaimQueryListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(FlowClaimQueryListener.class);

    public FlowClaimQueryListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @Autowired
    private ManageService manageService;

    @RabbitListener(queues = RabbitConfig.Queues.FLOW_CLAIM_QUERY)
    public void listenFlowClaimResult(Message message, Channel channel) {
        String claimId = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            logger.info("监听流量方代偿结果查询:{}", claimId);
            // manageService
            manageService.flowClaimQuery(claimId);
        } catch (Exception e) {
            processException(claimId, message, e, "监听流量方代偿结果查询异常", getMqService()::submitFlowClaimQueryDelay);
        } finally {
            ackMsg(claimId, message, channel);
        }
    }
}
