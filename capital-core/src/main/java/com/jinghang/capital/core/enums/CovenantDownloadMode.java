package com.jinghang.capital.core.enums;

/**
 * 文件协议下载模式
 */
public enum CovenantDownloadMode {
    DIRECT("直接下载", "COVENANT_DOWNLOAD_DIRECT"),
    APPLY("申请发起", "COVENANT_DOWNLOAD_APPLY"),
    QUERY("申请查询", "COVENANT_DOWNLOAD_QUERY");

    private final String mode;

    private final String rateTag;

    CovenantDownloadMode(String mode, String rateTag) {
        this.mode = mode;
        this.rateTag = rateTag;
    }

    public String getMode() {
        return mode;
    }

    public String getRateTag() {
        return rateTag;
    }
}
