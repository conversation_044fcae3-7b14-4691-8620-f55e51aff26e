package com.jinghang.capital.core.repository;

import com.jinghang.capital.core.entity.BankSubstituteRecord;
import com.jinghang.capital.core.enums.BankChannel;
import java.time.LocalDate;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

public interface BankSubstituteRecordRepository extends JpaRepository<BankSubstituteRecord, Long> {

    /**
     * 根据资方极代还日期查询数据
     *
     * @param date
     * @param channel
     * @return
     */
    List<BankSubstituteRecord> findBySubstituteDateAndChannel(LocalDate date, BankChannel channel);
}
