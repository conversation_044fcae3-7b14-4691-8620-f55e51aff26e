package com.jinghang.capital.core.convert;


import com.jinghang.capital.core.enums.CreditStatus;
import com.jinghang.capital.core.enums.LoanStatus;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.vo.recc.ReccType;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import static com.jinghang.capital.core.enums.CreditStatus.FAIL;
import static com.jinghang.capital.core.enums.CreditStatus.INIT;
import static com.jinghang.capital.core.enums.CreditStatus.PROCESSING;
import static com.jinghang.capital.core.enums.CreditStatus.SUCCESS;


/**
 * <AUTHOR>
 * @date 2023/8/27
 */
@Mapper
public interface EnumConvert {

    EnumConvert INSTANCE = Mappers.getMapper(EnumConvert.class);

    default ReccType convertReccType(ReccType type) {
        return ReccType.valueOf(type.name());
    }

    default CreditStatus toCreditStatus(ProcessStatus status) {
        return switch (status) {
            case INIT -> INIT;
            case PROCESSING -> PROCESSING;
            case SUCCESS -> SUCCESS;
            default -> FAIL;
        };
    }

    default LoanStatus toLoanStatus(ProcessStatus status) {
        return switch (status) {
            case INIT -> LoanStatus.INIT;
            case PROCESSING -> LoanStatus.PROCESSING;
            case SUCCESS -> LoanStatus.SUCCESS;
            default -> LoanStatus.FAIL;
        };
    }
}
