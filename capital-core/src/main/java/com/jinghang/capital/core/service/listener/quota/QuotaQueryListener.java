package com.jinghang.capital.core.service.listener.quota;

import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.dto.QuotaQueryDto;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.jinghang.common.util.JsonUtil;
import com.rabbitmq.client.Channel;
import java.nio.charset.StandardCharsets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 额度查询
 */
@Component
public class QuotaQueryListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(QuotaQueryListener.class);

    @Autowired
    private ManageService manageService;

    public QuotaQueryListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @RabbitListener(queues = RabbitConfig.Queues.QUOTA_QUERY)
    public void quotaQuery(Message message, Channel channel) {
        String json = new String(message.getBody(), StandardCharsets.UTF_8);
        logger.info("额度查询: {}", json);
        try {
            QuotaQueryDto dto = JsonUtil.convertToObject(json, QuotaQueryDto.class);
            manageService.quotaQuery(dto);
        } catch (Exception e) {
            logger.error("额度查询异常: {} ", json, e);
            processException(json, message, e, "额度查询异常", getMqService()::submitQuotaQueryDelay);
        } finally {
            ackMsg(json, message, channel);
        }
    }
}
