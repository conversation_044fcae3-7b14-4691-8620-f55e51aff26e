package com.jinghang.capital.core.entity;


import com.jinghang.capital.api.dto.ProtocolChannel;
import com.jinghang.capital.core.enums.BankChannel;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.io.Serial;


/**
 * 风控失败用户银行卡表
 */
@Entity
@Table(name = "failed_account_bank_card")
public class FailedAccountBankCard extends BaseEntity {


    @Serial
    private static final long serialVersionUID = 679431850814625127L;

    /**
     * 资方渠道
     */
    @Enumerated(EnumType.STRING)
    private BankChannel channel;

    /**
     * 卡号
     */
    private String cardNo;
    /**
     * 卡的户名
     */
    private String cardName;
    /**
     * 银行预留手机号
     */
    private String phone;
    /**
     * 身份证号
     */
    private String certNo;
    /**
     * 协议号
     */
    private String agreeNo;
    /**
     * 银行编码
     */
    private String bankCode;
    /**
     * 银行名称
     */
    private String bankName;

    @Enumerated(EnumType.STRING)
    private ProtocolChannel payChannel;

    public ProtocolChannel getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(ProtocolChannel payChannel) {
        this.payChannel = payChannel;
    }

    /**
     * 资方渠道
     */
    public BankChannel getChannel() {
        return this.channel;
    }

    /**
     * 资方渠道
     */
    public void setChannel(BankChannel channel) {
        this.channel = channel;
    }


    /**
     * 卡号
     */
    public String getCardNo() {
        return this.cardNo;
    }

    /**
     * 卡号
     */
    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    /**
     * 卡的户名
     */
    public String getCardName() {
        return this.cardName;
    }

    /**
     * 卡的户名
     */
    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    /**
     * 银行预留手机号
     */
    public String getPhone() {
        return this.phone;
    }

    /**
     * 银行预留手机号
     */
    public void setPhone(String phone) {
        this.phone = phone;
    }

    /**
     * 身份证号
     */
    public String getCertNo() {
        return this.certNo;
    }

    /**
     * 身份证号
     */
    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    /**
     * 协议号
     */
    public String getAgreeNo() {
        return this.agreeNo;
    }

    /**
     * 协议号
     */
    public void setAgreeNo(String agreeNo) {
        this.agreeNo = agreeNo;
    }

    /**
     * 银行编码
     */
    public String getBankCode() {
        return this.bankCode;
    }

    /**
     * 银行编码
     */
    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    /**
     * 银行名称
     */
    public String getBankName() {
        return this.bankName;
    }

    /**
     * 银行名称
     */
    public void setBankName(String bankName) {
        this.bankName = bankName;
    }


    @Override
    public String prefix() {
        return "FBC";
    }
}
