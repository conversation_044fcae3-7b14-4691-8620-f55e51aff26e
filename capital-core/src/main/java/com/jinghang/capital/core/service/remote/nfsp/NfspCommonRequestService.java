package com.jinghang.capital.core.service.remote.nfsp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.jinghang.common.http.exception.HttpException;
import com.jinghang.common.util.HttpUtil;
import com.jinghang.common.util.JsonUtil;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;


@Service
public class NfspCommonRequestService {

    private static final Logger logger = LoggerFactory.getLogger(NfspCommonRequestService.class);
    @Value("${common.service.privateKey}")
    private String privateKey;
    @Value("${common.service.clientId}")
    private String clientId;
    @Value("${common.service.url}")
    private String url;

    public <T> T request(NfspCommonBaseReq request, TypeReference<T> type) throws HttpException, JsonProcessingException, NfspBizException {
        try {
            logger.info("请求公共报文:{}", JsonUtil.convertToString(request));
            String rspStr = HttpUtil.post(url + request.getRequestType().getPath(), signRequest(request));
            //
            logger.info("公共返回原始报文:{}", rspStr);
            NfspCommonResponse response = JsonUtil.convertToObject(rspStr, NfspCommonResponse.class);
            if (!NfspCommonResponse.SUCCESS_CODE.equals(response.getCode())) {
                logger.error("公共接口异常:{}", JsonUtil.convertToString(response));
                if (Objects.equals(response.getCode(), NfspCommonResponse.ERROR_CODE) && Objects.equals(response.getMessage(), "订单不存在")) {
                    throw NfspBizException.NOT_EXIST;
                }
                throw new NfspBizException(response.getCode(), response.getMessage());
            }
            //
            String commonData = JsonUtil.convertToString(response.getData());
            logger.info("公共返回Data:{}", commonData);
            return JsonUtil.convertToCollection(commonData, type);
        } catch (HttpException e) {
            logger.error("请求nfsp-common,http异常", e);
            throw e;
        } catch (JsonProcessingException e) {
            logger.error("请求nfsp-common,json解析异常", e);
            throw e;
        }
    }

    private String signRequest(Object object) {
        JSONObject request = new JSONObject();
        request.put("pubMerNo", clientId);
        request.put("pubVersion", "1.0");
        request.put("pubNotifyUrl", "");
        String pubDataStr = JSON.toJSONString(object, SerializerFeature.SortField,
                SerializerFeature.MapSortField);
        //logger.info("1111111==={}", pubDataStr);
        String pubSign = null;
        try {
            pubSign = RSAUtils.sign(pubDataStr, privateKey);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        request.put("pubSign", pubSign);
        request.put("pubData", JSON.parse(pubDataStr, Feature.OrderedField));
        return request.toJSONString();
    }
}
