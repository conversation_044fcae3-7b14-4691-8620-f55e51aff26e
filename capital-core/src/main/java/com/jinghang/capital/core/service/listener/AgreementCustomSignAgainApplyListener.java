package com.jinghang.capital.core.service.listener;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.jinghang.capital.core.service.agreement.AgreementSignAgainService;
import com.rabbitmq.client.Channel;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class AgreementCustomSignAgainApplyListener extends AbstractListener {

    private static final Logger logger = LoggerFactory.getLogger(AgreementCustomSignAgainApplyListener.class);

    public AgreementCustomSignAgainApplyListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @Autowired
    private AgreementSignAgainService agreementResignService;

    @RabbitListener(queues = RabbitConfig.Queues.SIGNATURE_CUSTOM_AGAIN_APPLY)
    public void listenAgreementSignAgainApply(Message message, Channel channel) {
        String dateMsg = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            logger.info("listener agreement custom sign again: {}", dateMsg);
            // date format  2024-11-23
            LocalDate processDate = LocalDate.parse(dateMsg, DateTimeFormatter.ISO_LOCAL_DATE);
            agreementResignService.signApplyAgain(processDate);
        } catch (Exception e) {
            logger.error("listener agreement  custom sign again error, data date: {}", dateMsg, e);
        } finally {
            ackMsg(dateMsg, message, channel);
        }
    }
}
