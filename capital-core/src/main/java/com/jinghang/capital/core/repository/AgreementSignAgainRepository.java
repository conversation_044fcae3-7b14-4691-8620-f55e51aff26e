package com.jinghang.capital.core.repository;

import com.jinghang.capital.core.entity.AgreementSignAgain;
import com.jinghang.capital.core.enums.ProcessStatus;
import java.time.LocalDate;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;


public interface AgreementSignAgainRepository extends JpaRepository<AgreementSignAgain, String> {

    List<AgreementSignAgain> findBySignStatusAndProcessDate(ProcessStatus signStatus, LocalDate processDate);

}
