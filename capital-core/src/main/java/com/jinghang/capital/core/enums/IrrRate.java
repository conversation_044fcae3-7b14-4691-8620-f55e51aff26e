package com.jinghang.capital.core.enums;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/12/2
 */
public enum IrrRate {
    IRR24(new BigDecimal("0.2354")),
    IRR36(new BigDecimal("0.359525"));

    private BigDecimal bankCustomRate;

    IrrRate(BigDecimal bankCustomRate) {
        this.bankCustomRate = bankCustomRate;
    }

    public BigDecimal getBankCustomRate() {
        return bankCustomRate;
    }
}
