package com.jinghang.capital.core.convert.entity;


import com.jinghang.capital.core.entity.FailedLoanFile;
import com.jinghang.capital.core.entity.LoanFile;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


@Mapper
public interface LoanFileConvert {
    LoanFileConvert INSTANCE = Mappers.getMapper(LoanFileConvert.class);


    LoanFile toLoanFile(FailedLoanFile failedLoanFile);


    List<LoanFile> toLoanFileList(List<FailedLoanFile> failedLoanFile);


}
