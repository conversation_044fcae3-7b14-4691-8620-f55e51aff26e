package com.jinghang.capital.core.service.listener;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.rabbitmq.client.Channel;
import java.nio.charset.StandardCharsets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

/**
 * 对客还款重试
 * 线上还款
 */
@Component
public class CustomRepayRetryListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(CustomRepayRetryListener.class);

    private ManageService manageService;

    public CustomRepayRetryListener(MqService mqService, WarningService mqWarningService, ManageService manageService) {
        super(mqService, mqWarningService);
        this.manageService = manageService;
    }

    @RabbitListener(queues = RabbitConfig.Queues.REPAY_RETRY_QUEUE)
    public void customRepayRetry(Message message, Channel channel) {
        String customRepayIds = new String(message.getBody(), StandardCharsets.UTF_8);
        logger.info("repay retry ids: {}", customRepayIds);

        try {
            manageService.repayCustomRetry(customRepayIds);
        } catch (Exception e) {
            logger.error("还款重试, custom_record_id: {} ", customRepayIds, e);
            // processException(customRepayIds, message, e, "还款重试异常", getMqService()::submitRepayBankNotifyDelay);
        } finally {
            ackMsg(customRepayIds, message, channel);
        }


    }


}
