package com.jinghang.capital.core.controller;

import com.jinghang.capital.api.LoanService;
import com.jinghang.capital.api.dto.BusinessChronosProcessDto;
import com.jinghang.capital.api.dto.BusinessChronosProcessResultDto;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.bank.BankResultBackDto;
import com.jinghang.capital.api.dto.loan.LoanApplyDto;
import com.jinghang.capital.api.dto.loan.LoanLimitQueryDto;
import com.jinghang.capital.api.dto.loan.LoanLimitResultDto;
import com.jinghang.capital.api.dto.loan.LoanLprQueryDto;
import com.jinghang.capital.api.dto.loan.LoanLprResultDto;
import com.jinghang.capital.api.dto.loan.LoanNoticeDto;
import com.jinghang.capital.api.dto.loan.LoanNoticeResultDto;
import com.jinghang.capital.api.dto.loan.LoanQueryDto;
import com.jinghang.capital.api.dto.loan.LoanResultDto;
import com.jinghang.capital.api.dto.loan.LoanTrialQueryDto;
import com.jinghang.capital.api.dto.loan.LoanTrialResultDto;
import com.jinghang.capital.core.convert.apivo.ApiLoanConvert;
import com.jinghang.capital.core.convert.apivo.BankCallBackConvert;
import com.jinghang.capital.core.convert.apivo.BusinessDataConvert;
import com.jinghang.capital.core.convert.apivo.LoanNoticeConvert;
import com.jinghang.capital.core.convert.apivo.LoanTrialConvert;
import com.jinghang.capital.core.convert.apivo.LprConvert;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MockService;
import com.jinghang.capital.core.vo.BusinessChronosProcessResultVo;
import com.jinghang.capital.core.vo.BusinessChronosProcessVo;
import com.jinghang.capital.core.vo.bank.BankResultBackVo;
import com.jinghang.capital.core.vo.loan.LoanApplyVo;
import com.jinghang.capital.core.vo.loan.LoanLprQueryVo;
import com.jinghang.capital.core.vo.loan.LoanLprResultVo;
import com.jinghang.capital.core.vo.loan.LoanNoticeResultVo;
import com.jinghang.capital.core.vo.loan.LoanNoticeVo;
import com.jinghang.capital.core.vo.loan.LoanQueryVo;
import com.jinghang.capital.core.vo.loan.LoanResultVo;
import com.jinghang.capital.core.vo.loan.LoanTrialQueryVo;
import com.jinghang.capital.core.vo.loan.LoanTrialResultVo;
import com.jinghang.common.util.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("loan")
public class LoanController implements LoanService {

    private static final Logger logger = LoggerFactory.getLogger(LoanController.class);

    private final ManageService manageService;

    @Autowired
    private MockService mockService;

    @Value("${sms.mock.qhyp}")
    private Boolean mock;

    @Autowired
    public LoanController(ManageService manageService) {
        this.manageService = manageService;
    }

    @Override
    public RestResult<LoanResultDto> loan(LoanApplyDto apply) {
        if (mock) {
            return mockService.loan(apply);
        }

        logger.info("begin loan apply: outerLoanId: {}, outCreditId: {}, creditId: {}", apply.getSysId(), apply.getSysCreditId(), apply.getCreditId());
        LoanApplyVo applyVo = ApiLoanConvert.INSTANCE.toLoanApplyVo(apply);
        LoanResultVo result = manageService.loanApply(applyVo);
        LoanResultDto resultDto = ApiLoanConvert.INSTANCE.toLoanResultDto(result);
        logger.info("end loan apply: outerLoanId: {}, outCreditId: {}, creditId: {}, status: {}",
                apply.getSysId(), apply.getSysCreditId(), apply.getCreditId(), resultDto.getStatus());
        return RestResult.success(resultDto);
    }

    @Override
    public RestResult<LoanLimitResultDto> queryLimit(LoanLimitQueryDto loanLimitQueryDto) {
        return null;
    }

    @Override
    public RestResult<LoanResultDto> queryResult(LoanQueryDto loanQuery) {
        if (mock) {
            return mockService.queryResult(loanQuery);
        }

        logger.info("begin loan query: outerLoanId: {}, loanId: {}", loanQuery.getSysId(), loanQuery.getLoanId());
        LoanQueryVo queryVo = ApiLoanConvert.INSTANCE.toLoanQueryVo(loanQuery);
        LoanResultVo result = manageService.loanQuery(queryVo);
        LoanResultDto resultDto = ApiLoanConvert.INSTANCE.toLoanResultDto(result);
        logger.info("end loan query: outerLoanId: {}, loanId: {}, result: {}", loanQuery.getSysId(), loanQuery.getLoanId(), resultDto.getStatus());
        return RestResult.success(resultDto);

    }

    @Override
    public RestResult<LoanLprResultDto> lprResult(LoanLprQueryDto lprQuery) {
        if (mock) {
            return mockService.lprResult(lprQuery);
        }

        LoanLprQueryVo applyVo = LprConvert.INSTANCE.toApplyVo(lprQuery);
        LoanLprResultVo result = manageService.lprQuery(applyVo);
        LoanLprResultDto resultDto = LprConvert.INSTANCE.toResultDto(result);
        return RestResult.success(resultDto);
    }

    @Override
    public RestResult<LoanTrialResultDto> loanTrial(LoanTrialQueryDto loanTrial) {
        logger.info("借款试算, 请求入参：{}", JsonUtil.toJsonString(loanTrial));
        LoanTrialQueryVo vo = LoanTrialConvert.INSTANCE.toApplyVo(loanTrial);
        LoanTrialResultVo result = manageService.loanTrial(vo);
        LoanTrialResultDto resultDto = LoanTrialConvert.INSTANCE.toResultDto(result);
        logger.info("借款试算, 请求结果：{}", JsonUtil.toJsonString(resultDto));
        return RestResult.success(resultDto);
    }

    @Override
    public RestResult<LoanNoticeResultDto> loanNotice(LoanNoticeDto loanNoticeDto) {
        logger.info("放款通知, 请求入参：{}", JsonUtil.toJsonString(loanNoticeDto));
        LoanNoticeVo vo = LoanNoticeConvert.INSTANCE.toApplyVo(loanNoticeDto);
        LoanNoticeResultVo result = manageService.loanNotice(vo);
        LoanNoticeResultDto resultDto = LoanNoticeConvert.INSTANCE.toResultDto(result);
        logger.info("放款通知, 请求结果：{}", JsonUtil.toJsonString(resultDto));
        return RestResult.success(resultDto);
    }

    @Override
    public RestResult<BusinessChronosProcessResultDto> loanContractNoQuery(BusinessChronosProcessDto processDto) {
        logger.info("查询借款合同编号, 请求入参：{}", JsonUtil.toJsonString(processDto));
        BusinessChronosProcessVo vo = BusinessDataConvert.INSTANCE.toBusinessProcessVo(processDto);
        BusinessChronosProcessResultVo result = manageService.loanCotractNoQuery(vo);
        BusinessChronosProcessResultDto resultDto = BusinessDataConvert.INSTANCE.toBusinessProcessResultDto(result);
        logger.info("查询借款合同编号, 请求结果：{}", JsonUtil.toJsonString(resultDto));
        return RestResult.success(resultDto);
    }

    /**
     * 放款结果回调
     *
     * @param bankResultBackDto
     * @return
     */
    @Override
    public RestResult<BankResultBackDto> loanResultBack(BankResultBackDto bankResultBackDto) {
        logger.info("放款结果回调入参：{}", JsonUtil.toJsonString(bankResultBackDto));
        BankResultBackVo vo = BankCallBackConvert.INSTANCE.toBankResultBackVo(bankResultBackDto);
        String callbackResStr = manageService.loanResultCallback(vo.getBankChannel(), vo.getJson());
        bankResultBackDto.setJson(callbackResStr);
        logger.info("放款结果回调出参：{}", JsonUtil.toJsonString(bankResultBackDto));
        return RestResult.success(bankResultBackDto);
    }
}
