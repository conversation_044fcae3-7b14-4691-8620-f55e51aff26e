package com.jinghang.capital.core.service.listener.quota;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.service.*;
import com.rabbitmq.client.Channel;
import java.nio.charset.StandardCharsets;
import java.time.Duration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 调额申请结果查询
 */
@Component
public class QuotaAdjustResultQueryListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(QuotaAdjustResultQueryListener.class);

    private final int five = 5;
    private final int waitTime = 2;
    @Autowired
    private ManageService manageService;

    @Autowired
    private LockService lockService;

    public QuotaAdjustResultQueryListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @RabbitListener(queues = RabbitConfig.Queues.QUOTA_ADJUST_RESULT_QUERY)
    public void quotaAdjustResultQuery(Message message, Channel channel) {
        String adjustId = new String(message.getBody(), StandardCharsets.UTF_8);
        logger.info("调额申请结果查询: {}", adjustId);

        String redisKey = "fin_adjustId_query_" + adjustId;
        Locker lock = lockService.getLock(redisKey);
        try {
            boolean locked = lock.tryLock(Duration.ofSeconds(waitTime), Duration.ofSeconds(five));
            if (locked) {
                logger.info("调额申请结果查询,key：{}", redisKey);
                // manageService
                manageService.quotaAdjustResultQuery(adjustId);
            }
        } catch (Exception e) {
            logger.error("调额申请结果查询 异常: {} ", adjustId, e);
        } finally {
            lock.unlock();
            ackMsg(adjustId, message, channel);
        }
    }
}
