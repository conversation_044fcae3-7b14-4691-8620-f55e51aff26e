package com.jinghang.capital.core.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.io.Serial;

/**
 * 湖消账户属性
 *
 * @Author: Lior
 * @CreateTime: 2025/7/9 18:28
 */
@Entity
@Table(name = "hxbk_account_attr")
public class HXBKAccountAttr extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String certNo;

    /**
     * HX的openid，md5(身份证号)
     */
    private String openId;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }
}
