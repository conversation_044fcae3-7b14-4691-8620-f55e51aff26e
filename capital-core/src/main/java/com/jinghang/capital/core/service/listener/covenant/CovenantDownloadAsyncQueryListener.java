package com.jinghang.capital.core.service.listener.covenant;

import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.rabbitmq.client.Channel;
import java.nio.charset.StandardCharsets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 下载需申请下载的协议文件--查询申请结果
 */
@Component
public class CovenantDownloadAsyncQueryListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(CovenantDownloadAsyncQueryListener.class);

    @Autowired
    private ManageService manageService;

    public CovenantDownloadAsyncQueryListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @RabbitListener(queues = RabbitConfig.Queues.COVENANT_DOWNLOAD_ASYNC_QUERY)
    public void covenantAsyncQuery(Message message, Channel channel) {
        String applyId = new String(message.getBody(), StandardCharsets.UTF_8);
        logger.info("下载需申请下载的协议文件--查询申请结果: {}", applyId);

        try {
            manageService.covenantDownloadAsyncQuery(applyId);
        } catch (Exception e) {
            logger.error("下载需申请下载的协议文件--查询申请结果 异常: {} ", applyId, e);
        } finally {
            ackMsg(applyId, message, channel);
        }
    }
}
