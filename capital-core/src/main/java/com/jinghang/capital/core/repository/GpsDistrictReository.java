package com.jinghang.capital.core.repository;

import com.jinghang.capital.core.entity.GpsDistrict;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;

public interface GpsDistrictReository extends JpaRepository<GpsDistrict, String> {

    Optional<GpsDistrict> findByCityCodeAndDistrictSeqNum(String cityCode, Integer seqNo);

    List<GpsDistrict> findByProvinceCode(String provinceCode);

    List<GpsDistrict> findByCityCode(String cityCode);
}
