package com.jinghang.capital.core.service;


import com.jinghang.capital.api.BindService;
import com.jinghang.capital.api.CreditService;
import com.jinghang.capital.api.LoanService;
import com.jinghang.capital.api.RepayService;
import com.jinghang.capital.api.dto.BusinessChronosProcessDto;
import com.jinghang.capital.api.dto.BusinessChronosProcessResultDto;
import com.jinghang.capital.api.dto.ProcessStatus;
import com.jinghang.capital.api.dto.RepayStatus;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.bank.BankResultBackDto;
import com.jinghang.capital.api.dto.credit.BindApplyDto;
import com.jinghang.capital.api.dto.credit.BindConfirmDto;
import com.jinghang.capital.api.dto.credit.BindResultDto;
import com.jinghang.capital.api.dto.credit.CreditApplyDto;
import com.jinghang.capital.api.dto.credit.CreditQueryDto;
import com.jinghang.capital.api.dto.credit.CreditResultDto;
import com.jinghang.capital.api.dto.credit.ExtInfoDto;
import com.jinghang.capital.api.dto.credit.PreCreditApplyDto;
import com.jinghang.capital.api.dto.credit.PreCreditApplyResultDto;
import com.jinghang.capital.api.dto.credit.RecreditApplyDto;
import com.jinghang.capital.api.dto.loan.LoanApplyDto;
import com.jinghang.capital.api.dto.loan.LoanLimitQueryDto;
import com.jinghang.capital.api.dto.loan.LoanLimitResultDto;
import com.jinghang.capital.api.dto.loan.LoanLprQueryDto;
import com.jinghang.capital.api.dto.loan.LoanLprResultDto;
import com.jinghang.capital.api.dto.loan.LoanNoticeDto;
import com.jinghang.capital.api.dto.loan.LoanNoticeResultDto;
import com.jinghang.capital.api.dto.loan.LoanQueryDto;
import com.jinghang.capital.api.dto.loan.LoanResultDto;
import com.jinghang.capital.api.dto.loan.LoanTrialQueryDto;
import com.jinghang.capital.api.dto.loan.LoanTrialResultDto;
import com.jinghang.capital.api.dto.repay.ActiveLaunchClaimApplyDto;
import com.jinghang.capital.api.dto.repay.ActiveLaunchClaimResultDto;
import com.jinghang.capital.api.dto.repay.BatchTrailDto;
import com.jinghang.capital.api.dto.repay.BatchTrialResultDto;
import com.jinghang.capital.api.dto.repay.ClaimMarkApplyDto;
import com.jinghang.capital.api.dto.repay.ClaimMarkResultDto;
import com.jinghang.capital.api.dto.repay.ClaimRetryDto;
import com.jinghang.capital.api.dto.repay.ClaimRetryResultDto;
import com.jinghang.capital.api.dto.repay.CompensatedRepaySyncDto;
import com.jinghang.capital.api.dto.repay.CompensatedRepaySyncRltDto;
import com.jinghang.capital.api.dto.repay.DefrayDto;
import com.jinghang.capital.api.dto.repay.DefrayResultDto;
import com.jinghang.capital.api.dto.repay.OverduePlanDto;
import com.jinghang.capital.api.dto.repay.OverduePlanQueryDto;
import com.jinghang.capital.api.dto.repay.Payee;
import com.jinghang.capital.api.dto.repay.PlanDto;
import com.jinghang.capital.api.dto.repay.PlanItemDto;
import com.jinghang.capital.api.dto.repay.PlanQueryDto;
import com.jinghang.capital.api.dto.repay.RepayApplyDto;
import com.jinghang.capital.api.dto.repay.RepayBatchResultDto;
import com.jinghang.capital.api.dto.repay.RepayDateApplyDto;
import com.jinghang.capital.api.dto.repay.RepayDateResultDto;
import com.jinghang.capital.api.dto.repay.RepayDeductionApplyDto;
import com.jinghang.capital.api.dto.repay.RepayNoticeDto;
import com.jinghang.capital.api.dto.repay.RepayNoticeResultDto;
import com.jinghang.capital.api.dto.repay.RepayQueryDto;
import com.jinghang.capital.api.dto.repay.RepayResultDto;
import com.jinghang.capital.api.dto.repay.RepayReturnUploadDto;
import com.jinghang.capital.api.dto.repay.RepayReturnUploadResultDto;
import com.jinghang.capital.api.dto.repay.RepayTrailDto;
import com.jinghang.capital.api.dto.repay.SubstituteApplyDto;
import com.jinghang.capital.api.dto.repay.SubstituteApplyResultDto;
import com.jinghang.capital.api.dto.repay.SubstituteMarkApplyDto;
import com.jinghang.capital.api.dto.repay.SubstituteMarkResultDto;
import com.jinghang.capital.api.dto.repay.TrailResultDto;
import com.jinghang.capital.api.dto.repay.TrustPlanReleaseDto;
import com.jinghang.common.loan.PlanGenerator;
import com.jinghang.common.loan.plan.InterestType;
import com.jinghang.common.loan.plan.RepayPlan;
import com.jinghang.common.util.IdGen;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class MockService implements LoanService, CreditService, BindService, RepayService, DateTimeService {

    public static final int ID_LENGTH = 32;

    public static final int PERIODS = 12;

    public static final int NUMBER_100 = 100;
    @Value("${cybk.sys.time}")
    private String time;
    @Value("${cybk.sysTime.mock}")
    private Boolean mock;

    @Override
    public RestResult<PreCreditApplyResultDto> preCredit(PreCreditApplyDto preCreditApply) {
        PreCreditApplyResultDto result = new PreCreditApplyResultDto();
        result.setRuleCode("03");
        result.setRuleDesc("额度类通过");
        result.setCreditStatus(1);
        return RestResult.success(result);
    }


    @Override
    public RestResult<CreditResultDto> credit(CreditApplyDto<ExtInfoDto> creditApplyDto) {
        CreditResultDto result = new CreditResultDto();
        result.setCreditId(IdGen.genId("CR", ID_LENGTH));
        result.setCreditAmt(creditApplyDto.getCreditAmt());
        result.setPeriod(creditApplyDto.getPeriods());
        return RestResult.success(result);
    }

    @Override
    public RestResult<CreditResultDto> recredit(RecreditApplyDto recreditApply) {
        CreditResultDto result = new CreditResultDto();
        result.setCreditId(IdGen.genId("MO", ID_LENGTH));
        result.setStatus(ProcessStatus.PROCESSING);
        return RestResult.success(result);
    }

    @Override
    public RestResult<CreditResultDto> queryResult(CreditQueryDto creditQueryDto) {
        CreditResultDto result = new CreditResultDto();
        result.setCreditId(creditQueryDto.getCreditId());
        result.setCreditAmt(new BigDecimal("10000"));
        result.setCreditResultAmt(new BigDecimal("10000"));
        result.setPeriod(PERIODS);
        result.setCreditTime(LocalDateTime.now());
        result.setStatus(ProcessStatus.SUCCESS);
        return RestResult.success(result);
    }

    @Override
    public RestResult<LoanResultDto> queryResult(LoanQueryDto loanQueryDto) {
        LoanResultDto result = new LoanResultDto();
        result.setLoanId(loanQueryDto.getLoanId());
        result.setLoanAmt(new BigDecimal("10000"));
        result.setPeriods(PERIODS);
        result.setLoanTime(LocalDateTime.now());
        result.setStatus(ProcessStatus.SUCCESS);
        return RestResult.success(result);
    }

    @Override
    public RestResult<RepayResultDto> queryResult(RepayQueryDto repayQueryDto) {
        RepayResultDto result = new RepayResultDto();
        result.setActRepayTime(LocalDateTime.now());
        result.setOuterRepayId(repayQueryDto.getOuterRepayId());
        result.setRepayId("FIN-MOCK" + repayQueryDto.getOuterRepayId());
        result.setStatus(ProcessStatus.SUCCESS);
        return RestResult.success(result);
    }


    @Override
    public RestResult<CreditResultDto> failedNotify(CreditApplyDto<ExtInfoDto> creditApply) {
        return null;
    }

    @Override
    public RestResult<BankResultBackDto> creditResultBack(BankResultBackDto bankResultBackDto) {
        return null;
    }


    @Override
    public RestResult<LoanResultDto> loan(LoanApplyDto loanApplyDto) {
        LoanResultDto result = new LoanResultDto();
        result.setLoanId(IdGen.genId("LO", ID_LENGTH));
        result.setLoanAmt(loanApplyDto.getLoanAmt());
        result.setPeriods(loanApplyDto.getPeriods());
        result.setStatus(ProcessStatus.PROCESSING);
        return RestResult.success(result);
    }

    @Override
    public RestResult<LoanLimitResultDto> queryLimit(LoanLimitQueryDto loanLimitQueryDto) {
        return null;
    }


    @Override
    public RestResult<LoanLprResultDto> lprResult(LoanLprQueryDto loanLprQueryDto) {
        LoanLprResultDto result = new LoanLprResultDto();
        result.setLprRate("3.2");
        result.setLprDate(LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE));
        //        result.setBusinessRate(BankChannel.LH_RL.getIrrRate().multiply(BigDecimal.valueOf(NUMBER_100)).toString());
        return RestResult.success(result);
    }

    @Override
    public RestResult<LoanTrialResultDto> loanTrial(LoanTrialQueryDto loanTrial) {
        return null;
    }

    @Override
    public RestResult<LoanNoticeResultDto> loanNotice(LoanNoticeDto loanNoticeDto) {
        return null;
    }

    @Override
    public RestResult<BindResultDto> bindApply(BindApplyDto bindApplyDto) {
        BindResultDto result = new BindResultDto();
        result.setSysId(IdGen.genId("BC", ID_LENGTH));
        result.setStatus(ProcessStatus.PROCESSING);
        return RestResult.success(result);
    }

    @Override
    public RestResult<BindResultDto> bindConfirm(BindConfirmDto bindConfirmDto) {
        BindResultDto result = new BindResultDto();
        result.setSysId(bindConfirmDto.getBandCardId());
        if ("111111".equals(bindConfirmDto.getSms())) {
            result.setStatus(ProcessStatus.SUCCESS);
        } else {
            result.setStatus(ProcessStatus.FAIL);
        }
        return RestResult.success(result);
    }

    @Override
    public RestResult<TrailResultDto> trial(RepayTrailDto repayTrailDto) {
        TrailResultDto trailResultDto = new TrailResultDto();
        trailResultDto.setAmount(new BigDecimal("1068.88"));
        trailResultDto.setPrincipal(new BigDecimal("745.63"));
        trailResultDto.setInterest(new BigDecimal("199.92"));
        trailResultDto.setGuaranteeFee(new BigDecimal("0"));
        trailResultDto.setOverdueFee(new BigDecimal("20"));
        trailResultDto.setBreachFee(new BigDecimal("0"));
        trailResultDto.setPayee(Payee.CAPITAL);
        return RestResult.success(trailResultDto);
    }


    @Override
    public RestResult<RepayResultDto> batchApply(RepayDeductionApplyDto repayApply) {
        RepayResultDto result = new RepayResultDto();
        result.setActRepayTime(LocalDateTime.now());
        result.setOuterRepayId(repayApply.getOuterRepayId());
        result.setRepayId("FIN-MOCK" + repayApply.getOuterRepayId());
        result.setStatus(ProcessStatus.PROCESSING);
        return RestResult.success(result);
    }

    @Override
    public RestResult<RepayBatchResultDto> batchQuery(RepayQueryDto repayQuery) {
        RepayBatchResultDto result = new RepayBatchResultDto();
        result.setOuterRepayId(repayQuery.getOuterRepayId());
        result.setRepayId("FIN-MOCK" + repayQuery.getOuterRepayId());
        result.setStatus(ProcessStatus.SUCCESS);
        return RestResult.success(result);
    }

    @Override
    public RestResult<BatchTrialResultDto> batchTrial(BatchTrailDto trailDto) {
        return RestResult.success(new BatchTrialResultDto());
    }

    @Override
    public RestResult<Void> trustPlanRelease(TrustPlanReleaseDto releaseDto) {
        return RestResult.success(null);
    }

    @Override
    public RestResult<DefrayResultDto> defray(DefrayDto defrayDto) {
        return RestResult.success(null);
    }

    @Override
    public RestResult<SubstituteMarkResultDto> substituteMark(SubstituteMarkApplyDto substituteMarkApplyDto) {
        return RestResult.success(null);
    }

    @Override
    public RestResult<SubstituteApplyResultDto> substituteApply(SubstituteApplyDto substituteApplyDto) {
        return RestResult.success(null);
    }


    @Override
    public RestResult<SubstituteApplyResultDto> handSubstituteApply(SubstituteApplyDto substituteApplyDto) {
        return RestResult.success(null);
    }

    @Override
    public RestResult<RepayReturnUploadResultDto> repayOfflineReturnFileUpload(RepayReturnUploadDto repayReturnUploadDto) {
        return RestResult.success(null);
    }

    @Override
    public RestResult<BankResultBackDto> repayResultBack(BankResultBackDto bankResultBackDto) {
        return null;
    }


    @Override
    public RestResult<RepayResultDto> repay(RepayApplyDto repayApplyDto) {
        RepayResultDto result = new RepayResultDto();
        result.setActRepayTime(LocalDateTime.now());
        result.setOuterRepayId(repayApplyDto.getOuterRepayId());
        result.setRepayId("FIN-MOCK" + repayApplyDto.getOuterRepayId());
        result.setStatus(ProcessStatus.PROCESSING);
        return RestResult.success(result);
    }


    @Override
    public RestResult<PlanDto> queryPlan(PlanQueryDto planQueryDto) {

        PlanDto planDto = new PlanDto();
        // planDto.setChannel(com.qiangyun.fin.api.dto.BankChannel.TC_ZB);
        planDto.setPeriods(PERIODS);
        planDto.setSysLoanId(planQueryDto.getSysLoanId());
        planDto.setLoanId(planQueryDto.getLoanId());
        planDto.setLoanAmt(new BigDecimal("5000"));


        List<RepayPlan> toolPlans = PlanGenerator.
                genPlan(InterestType.AVERAGE_PRINCIPAL_PLUS_INTEREST, LocalDate.now(), planDto.getLoanAmt(), new BigDecimal("0.085"), PERIODS);
        List<RepayPlan> toolPlanFee = PlanGenerator.
                genPlan(InterestType.AVERAGE_PRINCIPAL_PLUS_INTEREST, LocalDate.now(), planDto.getLoanAmt(), new BigDecimal("0.2395"), PERIODS);

        Map<Integer, RepayPlan> toolPlanMap = toolPlans.stream().collect(Collectors.toMap(RepayPlan::getCurrentPeriod, plan -> plan));
        List<PlanItemDto> planItemDtos = new ArrayList<>();
        Map<Integer, RepayPlan> toolPlanFeeMap = toolPlanFee.stream().collect(Collectors.toMap(RepayPlan::getCurrentPeriod, plan -> plan));

        toolPlans.forEach(p -> {
            RepayPlan repayPlan = toolPlanMap.get(p.getCurrentPeriod());
            RepayPlan repayPlanFee = toolPlanFeeMap.get(p.getCurrentPeriod());
            BigDecimal addFee = repayPlanFee.getPrincipal().add(repayPlanFee.getInterest());
            BigDecimal amtCap = repayPlan.getPrincipal().add(repayPlan.getInterest());
            PlanItemDto planItemDto = new PlanItemDto();
            planItemDto.setPeriod(repayPlan.getCurrentPeriod());
            planItemDto.setRepayDate(repayPlan.getRepayDate());
            planItemDto.setTotalAmt(addFee);
            planItemDto.setPrincipalAmt(repayPlan.getPrincipal());
            planItemDto.setInterestAmt(repayPlan.getInterest());
            planItemDto.setPenaltyAmt(new BigDecimal("0.00"));
            planItemDto.setBreachAmt(new BigDecimal("0.00"));
            planItemDto.setGuaranteeAmt(addFee.subtract(amtCap));
            planItemDto.setCustRepayStatus(RepayStatus.NORMAL);
            planItemDtos.add(planItemDto);
        });
        planDto.setPlanItems(planItemDtos);
        return RestResult.success(planDto);
    }

    @Override
    public RestResult<OverduePlanDto> queryOverduePlan(OverduePlanQueryDto overduePlanQueryDto) {
        return null;
    }

    @Override
    public RestResult<CompensatedRepaySyncRltDto> compensatedRepaySync(CompensatedRepaySyncDto compensatedRepaySyncDto) {
        return null;
    }

    @Override
    public RestResult<ClaimMarkResultDto> claimMark(ClaimMarkApplyDto claimMarkApplyDto) {
        return null;
    }

    @Override
    public RestResult<RepayDateResultDto> appropriationInterest(RepayDateApplyDto repayDateApplyDto) {
        return null;
    }

    @Override
    public RestResult<ClaimRetryResultDto> claimRetry(ClaimRetryDto claimRetryDto) {
        return null;
    }

    @Override
    public RestResult<RepayNoticeResultDto> repayNotice(RepayNoticeDto repayNoticeDto) {
        return null;
    }

    @Override
    public RestResult<ActiveLaunchClaimResultDto> activeLaunchClaim(ActiveLaunchClaimApplyDto activeLaunchClaimApplyDto) {
        return null;
    }

    @Override
    public RestResult<BusinessChronosProcessResultDto> loanContractNoQuery(BusinessChronosProcessDto processDto) {
        return null;
    }

    @Override
    public RestResult<BankResultBackDto> loanResultBack(BankResultBackDto bankResultBackDto) {
        return null;
    }

    @Override
    public LocalDateTime getServerDateTime(String repayDate) {
        return this.mock
                ? LocalDateTime.parse(time, DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))
                : LocalDate.parse(repayDate, DateTimeFormatter.ISO_LOCAL_DATE).atStartOfDay();
    }
}
