package com.jinghang.capital.core.convert.apivo;


import com.jinghang.capital.api.dto.recc.*;
import com.jinghang.capital.core.convert.BankChannelConvert;
import com.jinghang.capital.core.convert.StatusConvert;
import com.jinghang.capital.core.entity.CYBKReconcileFile;
import com.jinghang.capital.core.vo.recc.ReccApplyVo;
import com.jinghang.capital.core.vo.recc.ReccDownloadVo;
import com.jinghang.capital.core.vo.recc.ReccFileApplyVo;
import com.jinghang.capital.core.vo.recc.ReccResultVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


@Mapper(uses = {StatusConvert.class, BankChannelConvert.class})
public interface ReccConvert {

    ReccConvert INSTANCE = Mappers.getMapper(ReccConvert.class);

    ReccApplyVo toApplyVo(ReccApplyDto applyDto);

    ReccResultDto toResultDto(ReccResultVo result);

    ReccDownloadVo toDownloadVo(ReccDownloadDto dto);

    ReccFileApplyVo toFileApplyVo(ReccFileApplyDto dto);

    ReccFileDto toReccFileDto(CYBKReconcileFile reconcileFile);
}
