package com.jinghang.capital.core.entity;


import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.FileType;
import com.jinghang.capital.core.enums.ProcessStatus;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.io.Serial;

/**
 * 借据文件信息表
 */
@Entity
@Table(name = "loan_file")
public class LoanFile extends BaseEntity {
    @Serial
    private static final long serialVersionUID = -5209766934809331841L;

    /**
     * 授信id
     */
    private String creditId;
    private String relatedId;
    /**
     * 资方渠道
     */
    @Enumerated(EnumType.STRING)
    private BankChannel channel;
    /**
     * 阶段
     */
    private String stage;
    /**
     * 文件类型
     */
    @Enumerated(EnumType.STRING)
    private FileType fileType;
    /**
     * 文件名
     */
    private String fileName;
    /**
     * 签章状态
     */
    @Enumerated(EnumType.STRING)
    private ProcessStatus signStatus;
    /**
     * 未签章ossKey
     */
    private String ossUnsignKey;
    /**
     * 未签章ossBucket
     */
    private String ossUnsignBucket;
    /**
     * oss路径
     */
    private String ossKey;
    /**
     * ossBucket
     */
    private String ossBucket;

    public String getRelatedId() {
        return relatedId;
    }

    public void setRelatedId(String relatedId) {
        this.relatedId = relatedId;
    }

    /**
     * 授信id
     */
    public String getCreditId() {
        return this.creditId;
    }

    /**
     * 授信id
     */
    public void setCreditId(String creditId) {
        this.creditId = creditId;
    }

    /**
     * 资方渠道
     */
    public BankChannel getChannel() {
        return this.channel;
    }

    /**
     * 资方渠道
     */
    public void setChannel(BankChannel channel) {
        this.channel = channel;
    }

    /**
     * 阶段
     */
    public String getStage() {
        return this.stage;
    }

    /**
     * 阶段
     */
    public void setStage(String stage) {
        this.stage = stage;
    }

    public FileType getFileType() {
        return fileType;
    }

    public void setFileType(FileType fileType) {
        this.fileType = fileType;
    }

    /**
     * 文件名
     */
    public String getFileName() {
        return this.fileName;
    }

    /**
     * 文件名
     */
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    /**
     * 签章状态
     */
    public ProcessStatus getSignStatus() {
        return this.signStatus;
    }

    /**
     * 签章状态
     */
    public void setSignStatus(ProcessStatus signStatus) {
        this.signStatus = signStatus;
    }

    /**
     * 未签章ossKey
     */
    public String getOssUnsignKey() {
        return this.ossUnsignKey;
    }

    /**
     * 未签章ossKey
     */
    public void setOssUnsignKey(String ossUnsignKey) {
        this.ossUnsignKey = ossUnsignKey;
    }

    /**
     * 未签章ossBucket
     */
    public String getOssUnsignBucket() {
        return this.ossUnsignBucket;
    }

    /**
     * 未签章ossBucket
     */
    public void setOssUnsignBucket(String ossUnsignBucket) {
        this.ossUnsignBucket = ossUnsignBucket;
    }

    /**
     * oss路径
     */
    public String getOssKey() {
        return this.ossKey;
    }

    /**
     * oss路径
     */
    public void setOssKey(String ossKey) {
        this.ossKey = ossKey;
    }

    /**
     * ossBucket
     */
    public String getOssBucket() {
        return this.ossBucket;
    }

    /**
     * ossBucket
     */
    public void setOssBucket(String ossBucket) {
        this.ossBucket = ossBucket;
    }

    @Override
    public String prefix() {
        return "LF";
    }
}
