package com.jinghang.capital.core.repository;


import com.jinghang.capital.core.entity.LoanFile;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.FileType;
import com.jinghang.capital.core.enums.ProcessStatus;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface LoanFileRepository extends JpaRepository<LoanFile, String> {
    List<LoanFile> findByCreditId(String creditId);

    List<LoanFile> findByRelatedId(String relatedId);

    List<LoanFile> findByCreditIdAndStage(String creditId, String stage);

    List<LoanFile> findByCreditIdAndFileType(String creditId, FileType fileType);

    LoanFile findTopByCreditIdAndStageAndFileType(String creditId, String stage, FileType fileType);

    LoanFile findTopByCreditIdAndRelatedIdAndStageAndFileType(String creditId, String relationId, String stage, FileType fileType);

    @Query(value = "select l from LoanFile l where l.creditId = ?1 and l.stage = ?2 and l.fileType in ?3")
    List<LoanFile> findByCreditIdAndFileTypeList(String creditId, String stage, List<FileType> fileTypeList);

    @Query(value = "select l from LoanFile l where l.relatedId = ?1 and l.stage = ?2 and l.signStatus = ?3 and l.fileType in ?4")
    List<LoanFile> findSuccessFileList(String relatedId, String stage, ProcessStatus signStatus, List<FileType> fileTypeList);


    @Query(value = "select l from LoanFile l where l.creditId = ?1 and l.relatedId = ?2 and l.stage = ?3 and l.fileType in ?4")
    List<LoanFile> findByCreditIdAndRelatedIdAndFileTypeList(String creditId, String relationId, String stage, List<FileType> fileTypeList);


    List<LoanFile> findByRelatedIdAndFileType(String relatedId, FileType fileType);

    List<LoanFile> findByRelatedIdAndFileTypeAndRemark(String relatedId, FileType fileType, String remark);

    List<LoanFile> findByRelatedIdAndFileTypeAndStage(String relatedId, FileType fileType, String stage);

    LoanFile findFirstByRelatedIdAndFileTypeAndChannelOrderByCreatedTimeDesc(String relatedId, FileType fileType, BankChannel channel);

    LoanFile findByRelatedIdAndFileTypeOrderByCreatedTimeDesc(String relatedId, FileType fileType);

    LoanFile findFirstByCreditIdAndFileTypeOrderByCreatedTimeDesc(String creditId, FileType fileType);

    List<LoanFile> findByChannelAndCreditIdAndFileTypeIn(BankChannel channel, String creditId, List<FileType> fileType);

    List<LoanFile> findByRelatedIdIn(List<String> relatedIds);

    List<LoanFile> findByCreditIdIn(List<String> creditIds);

    List<LoanFile> findByCreditIdAndFileTypeIn(String creditId, List<FileType> fileType);

    List<LoanFile> findByRelatedIdAndFileTypeIn(String relatedId, List<FileType> fileType);

    LoanFile findTopByRelatedIdAndStageAndFileType(String relatedId, String stage, FileType fileType);

    LoanFile findFirstByRelatedIdOrderByCreatedTimeDesc(String relatedId);

    List<LoanFile> findByCreditIdAndFileTypeInAndSignStatus(String creditId, List<FileType> list, ProcessStatus processStatus);
}
