package com.jinghang.capital.core.banks;


import com.jinghang.capital.core.entity.AccountBankCard;
import com.jinghang.capital.core.entity.Credit;
import com.jinghang.capital.core.entity.Loan;
import com.jinghang.capital.core.exception.BizException;
import com.jinghang.capital.core.service.CommonService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.vo.bind.BindApplyVo;
import com.jinghang.capital.core.vo.bind.BindConfirmVo;
import com.jinghang.capital.core.vo.bind.BindResultVo;
import com.jinghang.common.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class AbstractBankBindService implements BankBindService {

    @Autowired
    private CommonService commonService;

    @Autowired
    private MqService mqService;


    @Override
    public BindResultVo apply(BindApplyVo apply) {
        // 落库保存
        AccountBankCard bankCard = commonService.bindApply(apply, apply.getBankChannel());
        String sysCreditId = apply.getSysCreditId();
        String sysLoanId = apply.getSysLoanId();
        Credit credit = null;
        try {
            if (!StringUtil.isEmpty(sysCreditId)) {
                credit = commonService.findCreditByOutId(sysCreditId);
            }
        } catch (BizException biz) {
            // no throw
        }
        Loan loan = null;
        try {
            if (!StringUtil.isEmpty(sysLoanId)) {
                loan = commonService.findLoanByOutId(sysLoanId);
            }
        } catch (BizException biz) {
            // no throw
        }
        return bankBindApply(bankCard, credit, loan, apply);
    }

    @Override
    public BindResultVo confirm(BindConfirmVo confirm) {
        String cardId = confirm.getBandCardId();
        AccountBankCard bankCard = commonService.getBankCard(cardId);

        String sysCreditId = confirm.getSysCreditId();
        String sysLoanId = confirm.getSysLoanId();
        Credit credit = null;
        try {
            if (!StringUtil.isEmpty(sysCreditId)) {
                credit = commonService.findCreditByOutId(sysCreditId);
            }
        } catch (BizException biz) {
            // no throw
        }
        Loan loan = null;

        try {
            if (!StringUtil.isEmpty(sysLoanId)) {
                loan = commonService.findLoanByOutId(sysLoanId);
            }
        } catch (BizException biz) {
            // no throw
        }

        return bankBindConfirm(bankCard, confirm.getSms(), credit, loan);
    }


    /**
     * 调用资方授信
     *
     * @param bankCard 银行卡
     */
    protected abstract BindResultVo bankBindApply(AccountBankCard bankCard, Credit credit, Loan loan, BindApplyVo applyVo);

    /**
     * 调用资方授信查询
     *
     * @param bankCard 银行卡
     * @param smsCode  验证码
     */
    protected abstract BindResultVo bankBindConfirm(AccountBankCard bankCard, String smsCode, Credit credit, Loan loan);


    protected MqService getMqService() {
        return mqService;
    }

    protected CommonService getCommonService() {
        return commonService;
    }
}
