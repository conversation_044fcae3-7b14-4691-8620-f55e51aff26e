package com.jinghang.capital.core.convert.entity;


import com.jinghang.capital.core.entity.BankBatchSubstituteRecord;
import com.jinghang.capital.core.entity.BankLoanReplan;
import com.jinghang.capital.core.entity.BankRepayRecord;
import com.jinghang.capital.core.entity.BatchCustomerRepayDetail;
import com.jinghang.capital.core.entity.BatchCustomerRepayRecord;
import com.jinghang.capital.core.entity.CompensatedRepayNotify;
import com.jinghang.capital.core.entity.CompensatedRepayNotifyDetail;
import com.jinghang.capital.core.entity.CustomerLoanReplan;
import com.jinghang.capital.core.entity.CustomerRepayRecord;
import com.jinghang.capital.core.entity.FlowClaimRecord;
import com.jinghang.capital.core.entity.LoanReplan;
import com.jinghang.capital.core.enums.Payee;
import com.jinghang.capital.core.enums.RepayPurpose;
import com.jinghang.capital.core.util.AmountUtil;
import com.jinghang.capital.core.vo.repay.BankPlanItemVo;
import com.jinghang.capital.core.vo.repay.BankRepayRecordVo;
import com.jinghang.capital.core.vo.repay.CompensatedRepaySyncVo;
import com.jinghang.capital.core.vo.repay.RepayApplyVo;
import com.jinghang.capital.core.vo.repay.RepayBatchInfoVo;
import com.jinghang.capital.core.vo.repay.RepayBatchResultVo;
import com.jinghang.capital.core.vo.repay.RepayResultVo;
import com.jinghang.capital.core.vo.repay.RepayTrailVo;
import com.jinghang.capital.core.vo.repay.RepaymentDetail;
import com.jinghang.capital.core.vo.repay.TrailResultVo;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;


/**
 * <AUTHOR>
 * @date 2023/5/20
 */
@Mapper
public interface RepayConvert {
    RepayConvert INSTANCE = Mappers.getMapper(RepayConvert.class);

    @Mapping(source = "amount", target = "totalAmt")
    @Mapping(source = "principal", target = "principalAmt")
    @Mapping(source = "interest", target = "interestAmt")
    @Mapping(source = "overdueFee", target = "penaltyAmt")
    @Mapping(source = "platformOverdueFee", target = "platformPenaltyAmt")
    @Mapping(source = "guaranteeFee", target = "guaranteeAmt")
    @Mapping(source = "breachFee", target = "breachAmt")
    @Mapping(source = "consultFee", target = "consultAmt")
    @Mapping(source = "reduceAmount", target = "reduceAmt")
    CustomerRepayRecord toCrrEntity(RepayApplyVo repayApplyVo);


    @Mapping(source = "amount", target = "totalAmt")
    @Mapping(source = "principal", target = "principalAmt")
    @Mapping(source = "interest", target = "interestAmt")
    @Mapping(source = "overdueFee", target = "penaltyAmt")
    @Mapping(source = "guaranteeFee", target = "guaranteeAmt")
    @Mapping(source = "breachFee", target = "breachAmt")
    BankRepayRecord toBrrEntity(RepayApplyVo repayApplyVo);

    @Mapping(source = "repayTime", target = "actRepayTime")
    @Mapping(source = "totalAmt", target = "actTotalAmt")
    @Mapping(source = "principalAmt", target = "actPrincipalAmt")
    @Mapping(source = "interestAmt", target = "actInterestAmt")
    @Mapping(source = "penaltyAmt", target = "actPenaltyAmt")
    @Mapping(source = "platformPenaltyAmt", target = "actPlatformPenaltyAmt")
    @Mapping(source = "guaranteeAmt", target = "actGuaranteeAmt")
    @Mapping(source = "breachAmt", target = "actBreachAmt")
    @Mapping(source = "consultAmt", target = "actConsultAmt")
    @Mapping(target = "repayStatus", ignore = true)
    @Mapping(target = "revision", ignore = true)
    BankLoanReplan toPlanEntity(BankRepayRecord bankRepayRecord);

    @Mapping(source = "repayTime", target = "actRepayTime")
    @Mapping(source = "totalAmt", target = "actTotalAmt")
    @Mapping(source = "principalAmt", target = "actPrincipalAmt")
    @Mapping(source = "interestAmt", target = "actInterestAmt")
    @Mapping(source = "penaltyAmt", target = "actPenaltyAmt")
    @Mapping(source = "platformPenaltyAmt", target = "actPlatformPenaltyAmt")
    @Mapping(source = "guaranteeAmt", target = "actGuaranteeAmt")
    @Mapping(source = "consultAmt", target = "actConsultAmt")
    @Mapping(source = "breachAmt", target = "actBreachAmt")
    @Mapping(target = "repayStatus", ignore = true)
    @Mapping(target = "revision", ignore = true)
    CustomerLoanReplan toPlanEntity(CustomerRepayRecord customerRepayRecord);

    @Mapping(target = "id", ignore = true)
    @Mapping(source = "loanNo", target = "loanId")
    @Mapping(target = "repayStatus", ignore = true)
    @Mapping(target = "revision", ignore = true)
    BankRepayRecord toBankRepayEntity(BatchCustomerRepayDetail batchCustomerRepayDetail);

    @Mapping(target = "id", ignore = true)
    @Mapping(source = "loanNo", target = "loanId")
    @Mapping(source = "reductAmt", target = "reduceAmt")
    CustomerRepayRecord repayDetailsToCustomerRepayRecord(BatchCustomerRepayDetail batchCustomerRepayDetail);

    @Mapping(source = "bankSerial", target = "bankSeq")
    @Mapping(source = "repayStatus", target = "status")
    @Mapping(source = "repayTime", target = "actRepayTime")
    @Mapping(source = "id", target = "repayId")
    @Mapping(source = "remark", target = "failMsg")
    RepayResultVo toVo(CustomerRepayRecord customerRepayRecord);

    CompensatedRepayNotify toEntity(CompensatedRepaySyncVo compensatedRepaySyncVo);

    @Mapping(source = "principal", target = "principalAmt")
    @Mapping(source = "interest", target = "interestAmt")
    @Mapping(source = "guaranteeFee", target = "guaranteeAmt")
    @Mapping(source = "overdueFee", target = "penaltyAmt")
    CompensatedRepayNotifyDetail toEntity(RepaymentDetail repaymentDetail);

    @Mapping(source = "totalAmt", target = "amount")
    @Mapping(source = "principalAmt", target = "principal")
    @Mapping(source = "interestAmt", target = "interest")
    @Mapping(source = "guaranteeAmt", target = "guaranteeFee")
    @Mapping(source = "penaltyAmt", target = "overdueFee")
    @Mapping(source = "breachAmt", target = "breachFee")
    TrailResultVo loanReplanToTrailResultVo(LoanReplan loanReplan);


    @Mapping(target = "id", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(source = "id", target = "sysId")
    @Mapping(source = "reduceAmt", target = "couponAmt")
    BankRepayRecord customerRepayRecordToBankRepayRecord(CustomerRepayRecord customerRepayRecord);


    RepayApplyVo toRepayApplyVo(RepayTrailVo repayTrailVo);


    @Mapping(target = "id", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(source = "id", target = "sysId")
    @Mapping(source = "reduceAmt", target = "couponAmt")
    BankRepayRecord toBankRepayRecord(CustomerRepayRecord customerRepayRecord);

    /**
     * 对资实还计划转换为对资还款记录
     * 适用于代偿后生成对资还款记录，还款成功
     *
     * @param bankLoanReplan 对资实还计划
     * @return
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "repayStatus", expression = "java(com.jinghang.capital.core.enums.ProcessStatus.SUCCESS)")
    @Mapping(target = "repayTime", expression = "java(java.time.LocalDateTime.now())")
    @Mapping(source = "actTotalAmt", target = "totalAmt")
    @Mapping(source = "actPrincipalAmt", target = "principalAmt")
    @Mapping(source = "actInterestAmt", target = "interestAmt")
    @Mapping(source = "actPenaltyAmt", target = "penaltyAmt")
    @Mapping(source = "actGuaranteeAmt", target = "guaranteeAmt")
    @Mapping(source = "actBreachAmt", target = "breachAmt")
    @Mapping(target = "reduceAmount", expression = "java(java.math.BigDecimal.ZERO)")
    @Mapping(target = "couponAmt", expression = "java(java.math.BigDecimal.ZERO)")
    BankRepayRecord toBankRepayRecord(BankLoanReplan bankLoanReplan);

    /**
     * 代偿记录表转对资还款记录表
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(source = "id", target = "sysId")
    @Mapping(source = "applyTime", target = "repayTime")
    @Mapping(source = "claimPurpose", target = "repayPurpose")
    @Mapping(target = "repayType", expression = "java(com.jinghang.capital.core.enums.RepayType.CLAIM)")
    @Mapping(target = "repayMode", expression = "java(com.jinghang.capital.core.enums.RepayMode.OFFLINE)")
    @Mapping(source = "totalAmt", target = "totalAmt")
    @Mapping(source = "principalAmt", target = "principalAmt")
    @Mapping(source = "interestAmt", target = "interestAmt")
    @Mapping(source = "penaltyAmt", target = "penaltyAmt")
    @Mapping(source = "guaranteeAmt", target = "guaranteeAmt")
    @Mapping(source = "breachAmt", target = "breachAmt")
    @Mapping(target = "reduceAmount", expression = "java(java.math.BigDecimal.ZERO)")
    @Mapping(target = "couponAmt", expression = "java(java.math.BigDecimal.ZERO)")
    BankRepayRecord toBankRepayRecord(FlowClaimRecord flowClaimRecord);

    /**
     * 代偿初始化对资实还计划
     *
     * @param loanReplan   还款计划信息
     * @param repayPurpose 还款目的
     * @return
     */
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "repayStatus", expression = "java(com.jinghang.capital.core.enums.RepayStatus.REPAID)")
    @Mapping(target = "actRepayTime", expression = "java(java.time.LocalDateTime.now())")
    @Mapping(target = "actTotalAmt", expression = "java(java.math.BigDecimal.ZERO)")
    @Mapping(target = "actPrincipalAmt", expression = "java(java.math.BigDecimal.ZERO)")
    @Mapping(target = "actInterestAmt", expression = "java(java.math.BigDecimal.ZERO)")
    @Mapping(target = "actPenaltyAmt", expression = "java(java.math.BigDecimal.ZERO)")
    @Mapping(target = "actBreachAmt", expression = "java(java.math.BigDecimal.ZERO)")
    @Mapping(target = "actGuaranteeAmt", expression = "java(java.math.BigDecimal.ZERO)")
    @Mapping(target = "repayType", expression = "java(com.jinghang.capital.core.enums.RepayType.CLAIM)")
    @Mapping(target = "repayMode", expression = "java(com.jinghang.capital.core.enums.RepayMode.OFFLINE)")
    @Mapping(source = "repayPurpose", target = "repayPurpose")
    BankLoanReplan initClaimBankLoanReplan(LoanReplan loanReplan, RepayPurpose repayPurpose);


    /**
     * 代还初始化记录
     *
     * @param loanReplan 还款计划信息
     * @return
     */
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "claimDate", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "totalCompensations", ignore = true)
    @Mapping(target = "consecutiveCompensations", ignore = true)
    @Mapping(target = "substituteStatus", expression = "java(com.jinghang.capital.core.enums.ProcessStatus.INIT)")
    @Mapping(target = "pushDate", expression = "java(java.time.LocalDate.now())")
    @Mapping(target = "pushStatus", expression = "java(com.jinghang.capital.core.enums.PushStatus.WAIT)")
    @Mapping(target = "actTotalAmt", expression = "java(java.math.BigDecimal.ZERO)")
    @Mapping(target = "actPrincipalAmt", expression = "java(java.math.BigDecimal.ZERO)")
    @Mapping(target = "actInterestAmt", expression = "java(java.math.BigDecimal.ZERO)")
    @Mapping(target = "actPenaltyAmt", expression = "java(java.math.BigDecimal.ZERO)")
    @Mapping(target = "actBreachAmt", expression = "java(java.math.BigDecimal.ZERO)")
    @Mapping(target = "actGuaranteeAmt", expression = "java(java.math.BigDecimal.ZERO)")
    @Mapping(target = "actConsultAmt", expression = "java(java.math.BigDecimal.ZERO)")
    BankBatchSubstituteRecord initSubstituteBankRecord(LoanReplan loanReplan);

    @Mapping(source = "id", target = "repayId")
    @Mapping(source = "remark", target = "failMsg")
    RepayBatchResultVo toBatchQuery(BatchCustomerRepayRecord customerRepayRecord);


    @Mapping(source = "loanNo", target = "loanId")
    @Mapping(source = "repayTime", target = "actRepayTime")
    @Mapping(source = "remark", target = "failMsg")
    RepayBatchInfoVo toBatchQuery(BatchCustomerRepayDetail batchRepayDetail);

    List<RepayBatchInfoVo> toBatchQueryList(List<BatchCustomerRepayDetail> batchRepayDetailList);

    List<BankPlanItemVo> toPlanItems(List<LoanReplan> repayPlan);

    /**
     * 代偿后试算 参数转换
     *
     * @param plans
     * @return
     */
    default TrailResultVo loanReplansToTrailResultVo(List<LoanReplan> plans) {
        TrailResultVo trailResultVo = new TrailResultVo();
        trailResultVo.setPrincipal(toTrailPrincipal(plans));
        trailResultVo.setInterest(toTrailInterest(plans));
        trailResultVo.setOverdueFee(toTrailOverdueFee(plans));
        trailResultVo.setBreachFee(toTrailBreachFee(plans));
        trailResultVo.setGuaranteeFee(toTrailGuaranteeFee(plans));
        trailResultVo.setAmount(AmountUtil.sum(
                trailResultVo.getPrincipal(),
                trailResultVo.getInterest(),
                trailResultVo.getOverdueFee(),
                trailResultVo.getBreachFee(),
                trailResultVo.getGuaranteeFee()
        ));
        trailResultVo.setPayee(Payee.GUARANTEE);
        return trailResultVo;
    }

    /**
     * 代偿后结清试算 总本金计算
     *
     * @param plans
     * @return
     */
    default BigDecimal toTrailPrincipal(List<LoanReplan> plans) {
        return plans.stream()
                .map(LoanReplan::getPrincipalAmt)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 代偿后结清试算 总利息计算
     *
     * @param plans
     * @return
     */
    default BigDecimal toTrailInterest(List<LoanReplan> plans) {
        return plans.stream()
                .map(LoanReplan::getInterestAmt)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 代偿后结清试算 总罚息计算
     *
     * @param plans
     * @return
     */
    default BigDecimal toTrailOverdueFee(List<LoanReplan> plans) {
        return plans.stream()
                .map(LoanReplan::getPenaltyAmt)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 代偿后结清试算 总融担费计算
     *
     * @param plans
     * @return
     */
    default BigDecimal toTrailGuaranteeFee(List<LoanReplan> plans) {
        return plans.stream()
                .map(LoanReplan::getGuaranteeAmt)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 代偿后结清试算 总违约金计算
     *
     * @param plans
     * @return
     */
    default BigDecimal toTrailBreachFee(List<LoanReplan> plans) {
        return plans.stream()
                .map(LoanReplan::getBreachAmt)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    List<BankRepayRecordVo> toBatchRepayRecordList(List<BankRepayRecord> bankRepayRecordList);
}
