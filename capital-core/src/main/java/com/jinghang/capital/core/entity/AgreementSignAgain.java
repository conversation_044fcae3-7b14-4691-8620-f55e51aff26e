package com.jinghang.capital.core.entity;


import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.FileType;
import com.jinghang.capital.core.enums.ProcessStatus;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.time.LocalDate;

@Entity
@Table(name = "agreement_sign_again")
public class AgreementSignAgain extends BaseEntity {
    /**
     * 业务
     */
    private String businessId;

    @Enumerated(EnumType.STRING)
    private BankChannel channel;

    private String agreementSignatureId;

    /**
     * 文件类型
     */
    @Enumerated(EnumType.STRING)
    private FileType fileType;

    /**
     * 签署状态
     */
    @Enumerated(EnumType.STRING)
    private ProcessStatus signStatus;

    /**
     * 文件日期
     */
    private LocalDate processDate;

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public BankChannel getChannel() {
        return channel;
    }

    public void setChannel(BankChannel channel) {
        this.channel = channel;
    }

    public String getAgreementSignatureId() {
        return agreementSignatureId;
    }

    public void setAgreementSignatureId(String agreementSignatureId) {
        this.agreementSignatureId = agreementSignatureId;
    }

    public FileType getFileType() {
        return fileType;
    }

    public void setFileType(FileType fileType) {
        this.fileType = fileType;
    }

    public ProcessStatus getSignStatus() {
        return signStatus;
    }

    public void setSignStatus(ProcessStatus signStatus) {
        this.signStatus = signStatus;
    }

    public LocalDate getProcessDate() {
        return processDate;
    }

    public void setProcessDate(LocalDate processDate) {
        this.processDate = processDate;
    }
}
