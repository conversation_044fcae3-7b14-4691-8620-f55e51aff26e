package com.jinghang.capital.core.entity;


import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.FileType;
import com.jinghang.capital.core.enums.LoanStage;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.enums.SignatureType;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

@Entity
@Table(name = "agreement_signature")
public class AgreementSignature extends BaseEntity {
    /**
     * 业务
     */
    private String businessId;

    @Enumerated(EnumType.STRING)
    private BankChannel channel;
    /**
     * 文件类型
     */
    @Enumerated(EnumType.STRING)
    private FileType fileType;

    @Enumerated(EnumType.STRING)
    private LoanStage loanStage;
    /**
     * 模板号
     */
    private String templateNo;
    /**
     * 公共参数
     */
    private String address;
    /**
     * 公共参数
     */
    private String bankMobilePhone;
    /**
     * 公共参数
     */
    private String identNo;
    /**
     * 公共参数
     */
    private String personName;

    /**
     * 动态签章ossBucket
     */
    private String dynamicOssBucket;

    /**
     * 动态签章 ossKey
     */
    private String dynamicOssKey;
    /**
     * 签署状态
     */
    @Enumerated(EnumType.STRING)
    private ProcessStatus signState;
    /**
     * 失败原因
     */
    private String failReason;
    /**
     * 公共返回userId
     */
    private String commonUserId;
    /**
     * 公共返回taskId
     */
    private String commonTaskId;
    /**
     * 公共返回路径
     */
    private String ossFileKey;
    /**
     * 公共返回地址
     */
    private String ossFileUrl;
    /**
     * 公共返回contract_no
     */
    private String contractNo;
    /**
     * 签章类型
     */
    @Enumerated(EnumType.STRING)
    private SignatureType signatureType;

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public BankChannel getChannel() {
        return channel;
    }

    public void setChannel(BankChannel channel) {
        this.channel = channel;
    }

    public FileType getFileType() {
        return fileType;
    }

    public void setFileType(FileType fileType) {
        this.fileType = fileType;
    }

    public String getTemplateNo() {
        return templateNo;
    }

    public void setTemplateNo(String templateNo) {
        this.templateNo = templateNo;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getBankMobilePhone() {
        return bankMobilePhone;
    }

    public void setBankMobilePhone(String bankMobilePhone) {
        this.bankMobilePhone = bankMobilePhone;
    }

    public String getIdentNo() {
        return identNo;
    }

    public void setIdentNo(String identNo) {
        this.identNo = identNo;
    }

    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public ProcessStatus getSignState() {
        return signState;
    }

    public void setSignState(ProcessStatus signState) {
        this.signState = signState;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public String getCommonUserId() {
        return commonUserId;
    }

    public void setCommonUserId(String commonUserId) {
        this.commonUserId = commonUserId;
    }

    public String getCommonTaskId() {
        return commonTaskId;
    }

    public void setCommonTaskId(String commonTaskId) {
        this.commonTaskId = commonTaskId;
    }

    public String getOssFileKey() {
        return ossFileKey;
    }

    public void setOssFileKey(String ossFileKey) {
        this.ossFileKey = ossFileKey;
    }

    public String getOssFileUrl() {
        return ossFileUrl;
    }

    public void setOssFileUrl(String ossFileUrl) {
        this.ossFileUrl = ossFileUrl;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public LoanStage getLoanStage() {
        return loanStage;
    }

    public void setLoanStage(LoanStage loanStage) {
        this.loanStage = loanStage;
    }

    public SignatureType getSignatureType() {
        return signatureType;
    }

    public void setSignatureType(SignatureType signatureType) {
        this.signatureType = signatureType;
    }

    public String getDynamicOssBucket() {
        return dynamicOssBucket;
    }

    public void setDynamicOssBucket(String dynamicOssBucket) {
        this.dynamicOssBucket = dynamicOssBucket;
    }

    public String getDynamicOssKey() {
        return dynamicOssKey;
    }

    public void setDynamicOssKey(String dynamicOssKey) {
        this.dynamicOssKey = dynamicOssKey;
    }

    @Override
    public String prefix() {
        return "AS";
    }
}
