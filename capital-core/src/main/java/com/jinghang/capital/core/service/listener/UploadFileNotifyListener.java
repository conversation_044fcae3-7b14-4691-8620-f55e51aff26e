package com.jinghang.capital.core.service.listener;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.rabbitmq.client.Channel;
import java.nio.charset.StandardCharsets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 文件上传通知监听
 */
@Component
public class UploadFileNotifyListener extends AbstractListener {

    private static final Logger logger = LoggerFactory.getLogger(UploadFileNotifyListener.class);

    public UploadFileNotifyListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @Autowired
    private ManageService manageService;

    @RabbitListener(queues = RabbitConfig.Queues.FILE_UPLOAD_NOTIFY)
    public void fileUploadNotifyApply(Message message, Channel channel) {
        String creditId = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            logger.info("贷后文件上传, 通知资方:{}", creditId);
            // manageService
            manageService.fileUploadNotify(creditId);
        } catch (Exception e) {
            processException(creditId, message, e, "贷后文件上传通知异常", getMqService()::submitUploadFileNotifyQueryDelay);
        } finally {
            ackMsg(creditId, message, channel);
        }
    }

}
