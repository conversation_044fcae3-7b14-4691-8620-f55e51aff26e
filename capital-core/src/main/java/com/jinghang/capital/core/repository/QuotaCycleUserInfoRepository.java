package com.jinghang.capital.core.repository;


import com.jinghang.capital.core.entity.QuotaCycleUserInfo;
import com.jinghang.capital.core.enums.BankChannel;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * <AUTHOR>
 * @date 2023/8/24
 */
public interface QuotaCycleUserInfoRepository extends JpaRepository<QuotaCycleUserInfo, String> {

    Optional<QuotaCycleUserInfo> findByChannelAndUserIdCard(BankChannel channel, String userIdCard);

    Optional<QuotaCycleUserInfo> findByCreditId(String creditId);
}
