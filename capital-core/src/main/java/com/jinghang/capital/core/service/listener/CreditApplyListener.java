package com.jinghang.capital.core.service.listener;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.rabbitmq.client.Channel;
import java.nio.charset.StandardCharsets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/5/14
 */
@Component
public class CreditApplyListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(CreditApplyListener.class);

    public CreditApplyListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @Autowired
    private ManageService manageService;

    @RabbitListener(queues = RabbitConfig.Queues.CREDIT_STAGE_UPLOAD)
    public void listenCreditApply(Message message, Channel channel) {
        String creditId = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            logger.info("上传影像, 申请资方授信:{}", creditId);
            // manageService
            manageService.creditApply(creditId);
        } catch (Exception e) {
            logger.error("credit apply error. creditId: {}", creditId, e);
            processException(creditId, message, e, "授信申请异常", getMqService()::submitCreditResultQueryDelay);
        } finally {
            ackMsg(creditId, message, channel);
        }
    }
}
