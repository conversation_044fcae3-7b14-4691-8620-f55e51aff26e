package com.jinghang.capital.core.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.io.Serial;


@Entity
@Table(name = "failed_account_device")
public class FailedAccountDevice extends BaseEntity {


    @Serial
    private static final long serialVersionUID = -4248182280624997977L;

    /**
     * accountId
     */
    private String accountId;
    /**
     * 机型
     */
    private String model;
    /**
     * 系统类型
     */
    private String osType;
    /**
     * 版本
     */
    private String osVersion;
    /**
     * gps
     */
    private String gps;
    /**
     * ip
     */
    private String ip;
    /**
     * mac地址
     */
    private String mac;


    /**
     * 维度
     */
    private String latitude;

    /**
     * 经度
     */
    private String longitude;

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getOsType() {
        return osType;
    }

    public void setOsType(String osType) {
        this.osType = osType;
    }

    public String getOsVersion() {
        return osVersion;
    }

    public void setOsVersion(String osVersion) {
        this.osVersion = osVersion;
    }

    public String getGps() {
        return gps;
    }

    public void setGps(String gps) {
        this.gps = gps;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    @Override
    protected String prefix() {
        return "FAD";
    }
}
