package com.jinghang.capital.core.repository;


import com.jinghang.capital.core.entity.BatchCustomerRepayDetail;
import com.jinghang.capital.core.enums.ProcessStatus;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

public interface BatchCustomerRepayDetailRepository extends JpaRepository<BatchCustomerRepayDetail, String> {
    List<BatchCustomerRepayDetail> findByBatchRepayId(String batchRepayId);

    List<BatchCustomerRepayDetail> findByBatchRepayIdOrderByPeriodAsc(String batchRepayId);

    List<BatchCustomerRepayDetail> findByLoanNoAndPeriodAndStatus(String id, Integer period, ProcessStatus processStatus);

    List<BatchCustomerRepayDetail> findByLoanNoIn(List<String> loanIds);

    List<BatchCustomerRepayDetail> findByIdIn(List<String> ids);
}
