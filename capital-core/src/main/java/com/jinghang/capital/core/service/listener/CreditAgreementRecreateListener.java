package com.jinghang.capital.core.service.listener;


import com.jinghang.capital.core.config.RabbitConfig;
import com.jinghang.capital.core.service.AbstractListener;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.MqService;
import com.jinghang.capital.core.service.WarningService;
import com.rabbitmq.client.Channel;
import java.nio.charset.StandardCharsets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
public class CreditAgreementRecreateListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(CreditAgreementRecreateListener.class);

    public CreditAgreementRecreateListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @Autowired
    private ManageService manageService;

    @RabbitListener(queues = RabbitConfig.Queues.CREDIT_AGREEMENT_RECREATE)
    public void creditAgreementRecreate(Message message, Channel channel) {
        String creditId = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            logger.info("listener credit agreement recreate, creditId: {}", creditId);
            // manageService
            manageService.creditAgreementRecreate(creditId);
        } catch (Exception e) {
            logger.error("listener credit agreement recreate error, creditId: {}", creditId, e);
        } finally {
            ackMsg(creditId, message, channel);
        }
    }
}
