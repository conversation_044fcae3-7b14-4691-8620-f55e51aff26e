package com.jinghang.capital.core.service.loan;

import com.jinghang.capital.core.banks.hxbk.callback.service.HXBKCallbackService;
import com.jinghang.capital.core.banks.hxbk.enums.HXBKMethod;
import com.jinghang.capital.core.service.ManageService;
import com.jinghang.capital.core.service.listener.LoanResultListener;
import com.jinghang.capital.core.vo.loan.LoanApplyVo;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicBoolean;
import org.junit.jupiter.api.Test;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @date 2025-07-11 16:09
 */
//@ActiveProfiles("fat")
@SpringBootTest
public class LoanServiceTest {
    @Autowired
    private ManageService manageService;


    @Test
    public void testStartLoan() {
        LoanApplyVo loanApplyVo = LoanApplyVoBuilder.defaultTestVo();
        manageService.loanApply(loanApplyVo);
    }

    private volatile boolean failed = false;
    @Autowired
    private HXBKCallbackService hxbkCallbackService;
    @Autowired
    private LoanResultListener loanResultListener;

    @Test
    public void testConcurrentAccessToMethodsAAndB() throws InterruptedException {
        String biz = "{\"order_no\":\"LO250804121114875812159586823325\",\"msg\":\"\",\"contract_no\":\"LO250719134142124489743712561123\",\"receipt_info\":{\"already_accrual\":0,\"already_corpus\":0,\"already_date\":\"2025-07-21\",\"apply_amount\":2000,\"card_no\":\"******************\",\"cur_period\":1,\"custom_name\":\"姜科\",\"loan_amount\":2000,\"loan_time\":\"2025-07-21 14:41:37\",\"mobile\":\"13611239918\",\"period\":12,\"receipt_no\":\"LA202407010000006\",\"repay_date\":\"1\",\"repay_type\":\"1\",\"status\":\"1\",\"workflow_status\":\"0\"},\"status\":\"1\",\"repay_ref\":[{\"already_amt\":1,\"end_time\":\"2025-07-21\",\"guarantee_fee\":1,\"liquidated_damages\":1,\"need_amt\":1,\"period\":1,\"remain_principal\":1,\"repay_time\":\"2024-7-4\",\"start_time\":\"2025-07-21\",\"trans_fee\":1,\"trans_interest\":1,\"trans_principal\":1}]}";

        // 使用CountDownLatch确保两个线程同时启动
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch endLatch = new CountDownLatch(2); // 用于等待两个线程结束

        AtomicBoolean failed = new AtomicBoolean(false);

        Thread threadA = new Thread(() -> {
            try {
                startLatch.await(); // 等待启动信号
                hxbkCallbackService.processCallback(HXBKMethod.LOAN_RESULT_CALLBACK.getName(), "1", biz);
            } catch (Exception e) {
                e.printStackTrace();
                failed.set(true);
            } finally {
                endLatch.countDown(); // 标记线程完成
            }
        });

        Thread threadB = new Thread(() -> {
            try {
                startLatch.await(); // 等待启动信号
                Message message = new Message("LO250804121114875812159586823325".getBytes(StandardCharsets.UTF_8));
                loanResultListener.listenLoanResult(message, null);
            } catch (Exception e) {
                e.printStackTrace();
                failed.set(true);
            } finally {
                endLatch.countDown(); // 标记线程完成
            }
        });

        // 启动线程（此时线程会在startLatch.await()处等待）
        threadA.start();
        threadB.start();

        // 释放启动门闩，让两个线程同时开始执行
        startLatch.countDown();

        // 等待两个线程都完成
        endLatch.await();

        // 检查是否有并发问题发生
        if (failed.get()) {
            System.out.println("Concurrency issue detected between methodA and methodB");
        }
    }
}

