package com.jinghang.capital.core.banks.cybk.recc;

import com.jinghang.capital.core.service.FileService;
import com.jinghang.capital.core.service.WarningService;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR> gale
 * @Classname CYBKReccAbstractHandlerTest
 * @Description TODO
 * @Date 2025/5/15 00:32
 */
@SpringBootTest
class CYBKReccAbstractHandlerTest {

    @Autowired
    private WarningService warningService;

    @Autowired
    private FileService fileService;

    @Test
    void findCustReccRepay() throws Exception {

        Path tempFile = Path.of("/Users/<USER>/Downloads/SecretKey.csv");

        InputStream is = Files.newInputStream(tempFile);
        fileService.uploadOss("test-jh01", "test/SecretKey.csv", is);
    }
}
