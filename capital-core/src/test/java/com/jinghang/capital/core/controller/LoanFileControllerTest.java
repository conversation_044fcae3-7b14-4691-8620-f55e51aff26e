package com.jinghang.capital.core.controller;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.FileType;
import com.jinghang.capital.api.dto.file.FileDailyProcessDto;
import java.time.LocalDate;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR> gale
 * @Classname LoanFileControllerTest
 * @Description TODO
 * @Date 2025/6/4 14:20
 */
@SpringBootTest
class LoanFileControllerTest {

    @Autowired
    private LoanFileController loanFileController;

    @Test
    void dailyProcess() {
        FileDailyProcessDto processDto = new FileDailyProcessDto();

        processDto.setBankChannel(BankChannel.CYBK);

        processDto.setProcessDate(LocalDate.of(2025, 11, 30));
        processDto.setType(FileType.LOAN_CONTRACT);
        processDto.setBankChannel(BankChannel.CYBK);


        loanFileController.dailyProcess(processDto);
    }
}
