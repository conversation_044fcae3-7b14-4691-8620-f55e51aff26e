package com.jinghang.capital.core.banks.cybk.recc;

import com.cycfc.base.vo.Header;
import com.cycfc.base.vo.Image;
import com.cycfc.base.vo.Response;
import com.cycfc.client.api.CycfcAPI;
import com.google.common.io.Files;
import java.io.File;
import java.io.IOException;
import java.util.UUID;
import org.junit.jupiter.api.Test;

/**
 * 发送影像接口示例。 演示如何以文件形式，bytes形式， 串行以及并行发送影像件。
 *
 * <AUTHOR>
 */
public class SendImageExample {
    private static String aesKey = "****************";
    private static String signPrivateKey = "MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAMzQ98Ptl9OZ6gzJqNdNTJktb6Vy4IXTVZe+f81QSETfjt7o7xEXXZ215T3hiP7+USHRLJdpL9WoFbXnOJf9Ep44QzuQsRtyF/RM67tAfqxWuVwHZ69GXi8N9q1HuJn62HZDRaBdY8YG+ZVxMEqpBCH8Tju/VsNaioy3GWi6VEnpAgMBAAECgYBtDcsOwKA5o9cyLeG/mcHyQXYT4wHXX7pQTLdhy6BfQ6Wf3OkF8aoAAkzoUQEPvLvYRLb4e6JjodbFfzLsAmz5a+HHeGHTb/uPBB1rmriSwXSYpH1Mghj9W71ErF7tLRNnRFzWTWHnINBkYh/Qr1CUhWulx6aT8WkomN4iHsi8sQJBAOdbmzR0ef4dmh3cajfgWmwhIOpUU9qjXlVRHlSBcCoX9LYaesV+fTNB1u5TSoZqlHKU2OpDx8pMnhBZfg/JmaUCQQDioahXTB5xYYPoYPbXGN6JzAgiDrD1h/MOn5zrx4e/1In5qCrHSz917T8PsHh6kT/YvB0bIhVwSqc/PI2VeRP1AkEAmS91b2Dj2Va+UaE8jyy0kj3JLn6RWwWqkzD3tRpTKvvboqubuKJMwaCJcBmkEaYGDmvxyAMDCTZ7vfWTW16vpQJBAJSJs9YTs9hIO7Zw5xgv2puiOKppyp4CztGE18rJajXWzd/t0qvwq9VU/AICfdSHorshAEqqWjbRFzEhNQME6okCQQCmy/60tH5P+tOhWOlKeJaEerrV+qRyIY2llklwbUxhR0OnXXm7DXZW0M6vVVNPXmisO59tQ1Icayr+lF64eyJp";
    String url = "**************";
    int port = 18382;

    /**
     * 发送影像预上传
     */
    @Test
    public void sendImage() throws IOException {
        CycfcAPI.instance(url, port, aesKey, signPrivateKey);
        // 设置报文头信息
        Header header = new Header();
        header.setChannel("6134");//渠道编号--由长银分配
        header.setAppID("C6134T1V1");//接入方系统ID-有长银分配
        header.setReqNo(UUID.randomUUID().toString());//请求方流水号-保证唯一
        // 设置报文内容
        Image image = new Image();
        image.setApplCde("6125986784665451");//长银流水号
        image.setChannelId("6134");//渠道编号--由长银分配
        image.setApplyTime("2021-01-08");//申请时间
        image.setFileName("idcard.jpg");//文件名称
        image.setIdNo("6895647895565661");//身份证号
        image.setImgType("1");//影像类型编码,文档约定中有
        image.setFileType("jpg");//文件类型
        //若Attachments为空，则LocalFilePath不能为空，否则程序无法找到本地文件
        image.setFileBytes(Files.toByteArray(new File("D:\\Test\\idcard.jpg")));
        long start = System.currentTimeMillis();
        Response response = CycfcAPI.submitImage(header, image);
        long spent = System.currentTimeMillis() - start;
        System.out.println("request file:" + image.getFileName() + ". output:" + response);
        System.out.println("spent time:" + (spent));
    }


    /**
     * 发送影像预上传 后续会根据信息流做文件校验
     */
   /* @Test
    public void sendImagePre() throws IOException {
        CycfcAPI.instance(url, port, aesKey, signPrivateKey);
        // 设置报文头信息
        Header header = new Header();
        header.setChannel("6134");//渠道编号--由长银分配
        header.setAppID("C6134T1V1");//接入方系统ID-有长银分配
        header.setReqNo(UUID.randomUUID().toString());//请求方流水号-保证唯一
        // 设置报文内容
        Image image = new Image();
        image.setOutApplSeq("6125986784665451");//外部流水号
        image.setChannelId("6134");//渠道编号--由长银分配
        image.setApplyTime("2021-01-08");//申请时间
        image.setFileName("idcard.jpg");//文件名称
        image.setIdNo("6895647895565661");//身份证号
        image.setImgType("1");//影像类型编码,文档约定中有
        image.setFileType("jpg");//文件类型
        //若Attachments为空，则LocalFilePath不能为空，否则程序无法找到本地文件
        image.setFileBytes(Files.toByteArray(new File("D:\\Test\\test.txt")));
        long start = System.currentTimeMillis();
        Response response = CycfcAPI.submitImagePre(header, image);
        long spent = System.currentTimeMillis() - start;
        System.out.println("request file:" + image.getFileName() + ". output:" + response);
        System.out.println("spent time:" + (spent));
        //每个文件会返回影像id,后续把id通过数组形式,传入授信申请接口的imageUrl
    }*/
}
