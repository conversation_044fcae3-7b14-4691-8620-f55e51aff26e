package com.jinghang.capital.core.controller;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.credit.PreCreditApplyDto;
import com.jinghang.capital.api.dto.credit.PreCreditApplyResultDto;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 撞库接口测试
 *
 * @公司 中数金智(上海)有限公司
 * @作者 Mr.sandman
 * @时间 2025/06/25 15:33
 */
@SpringBootTest
public class CreditControllerTest {

    @Autowired
    private CreditController creditController;

    @Test
    void preCredit() {
        PreCreditApplyDto preCreditApply = new PreCreditApplyDto();
        preCreditApply.setMobile("***********");
        preCreditApply.setCardNo("342222199702034418");
        preCreditApply.setBankChannel(BankChannel.CYBK);
        RestResult<PreCreditApplyResultDto> result = creditController.preCredit(preCreditApply);
        System.out.println(result);
    }

}
